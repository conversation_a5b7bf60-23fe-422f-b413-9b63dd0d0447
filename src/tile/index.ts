import { LitElement, html, type TemplateResult, type CSSResultGroup } from "lit";
import { property } from "lit/decorators.js";
import { styles } from '../styles';;
import { tileStyle } from "./tile.style";
import type { TileAppearances } from "./types";
import "../heading";
import "../text";
import "../icons";

class Tile extends LitElement {
  static styles: CSSResultGroup = [styles, tileStyle];

  @property({ type: String }) appearance: TileAppearances = 'secondary';
  @property({ type: String }) iconName = "";
  @property({ type: String }) title = "";
  @property({ type: String }) text = "";

  protected render = (): TemplateResult => {
    return html`
      <div part="base" class="${this.appearance}">
        ${this.iconName ? html`<eds-icon part="icon" name="${this.iconName}"></eds-icon>` : ""}
        <div part="content">
          ${this.title ? html`<eds-heading part="title" as="h6" text="${this.title}" size="sm"></eds-heading>` : ""}
          ${this.text ? html`<eds-text part="text" as="p" text="${this.text}" size="lg" weight="medium"></eds-text>` : ""}
        </div>
      </div>
    `;
  };
}

if (!customElements.get("eds-tile")) {
  customElements.define("eds-tile", Tile);
}

declare global {
  interface HTMLElementTagNameMap {
    "eds-tile": Tile;
  }
}

export { Tile };
