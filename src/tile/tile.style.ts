import { css, type CSSResultGroup } from 'lit';

const tileStyle: CSSResultGroup = css`
  :host {
    --eds-tile-flex-direction: var(--tile-flex-direction, column);
    --eds-tile-gap: var(--tile-gap, var(--eds-spacing-300));
    --eds-tile-background-color: var(--tile-background-color, var(--eds-colors-primary-lighter));
    --eds-tile-border-properties: var(--tile-border-properties, var(--eds-stroke-025) var(--eds-border-style-base) var(--eds-border-color-default));
    --eds-tile-border-radius: var(--tile-border-radius, var(--eds-radius-200));
    
    --eds-tile-content-gap: var(--tile-content-gap, 0);
    
    
    
    --eds-tile-icon-size: var(--tile-icon-size, var(--eds-sizing-600));
  }
  
  [part="base"] {
    display: flex;
    align-items: var(--eds-tile-align-items);
    gap: var(--eds-tile-gap);
    padding: var(--eds-tile-padding);
    background-color: var(--eds-tile-background-color);
    border: var(--eds-tile-border-properties);
    border-radius: var(--eds-tile-border-radius);
  }

  [part="base"].primary {
    --eds-tile-align-items: var(--tile-align-items, flex-start);
    --eds-tile-padding: var(--tile-padding, var(--eds-spacing-300));

    --eds-tile-icon-color: var(--tile-icon-color, var(--eds-colors-icon-default));

    --eds-tile-text-color: var(--tile-text-color, var(--eds-colors-text-dark));
    
    --eds-tile-title-color: var(--tile-title-color, var(--eds-colors-text-default));

    flex-direction: var(--eds-tile-flex-direction);
  }

  [part="base"].secondary {
    --eds-tile-align-items: var(--tile-align-items, center);
    --eds-tile-padding: var(--tile-padding, var(--eds-spacing-400) var(--eds-spacing-300));

    --eds-tile-icon-color: var(--tile-icon-color, var(--eds-colors-secondary-light));
  }
  
  [part="content"] {
    display: flex;
    flex-direction: column;
    gap: var(--eds-tile-content-gap);
  }
  
  [part="icon"] {
    color: var(--eds-tile-icon-color);
    width: var(--eds-tile-icon-size);
    height: var(--eds-tile-icon-size);
    flex-shrink: 0;
  }
  
  [part="title"] {
    --eds-heading-text-color: var(--eds-tile-title-color);
  }

  [part="text"] {
    --eds-text-color: var(--eds-tile-text-color);
  }
`

export { tileStyle };