import { css } from 'lit';

export const toggleStyle = css`
  :host {
    --eds-toggle-flex-direction: var(--toggle-flex-direction, row);
    --eds-toggle-align-items: var(--toggle-align-items, center);
    --eds-toggle-gap: var(--toggle-gap, var(--eds-sizing-200));
    --eds-toggle-height: var(--toggle-height, var(--eds-sizing-600));
    --eds-toggle-border-radius: var(--toggle-border-radius, var(--eds-radius-400));
    --eds-toggle-width: var(--toggle-width, var(--eds-sizing-800));
    --eds-toggle-background-color: var(--toggle-background-color, var(--eds-colors-surface-level-2));
    --eds-toggle-checked-background-color: var(--toggle-checked-background-color, var(--eds-colors-primary-default));
    --eds-toggle-thumb-size: var(--toggle-thumb-size, var(--eds-sizing-500));
    --eds-toggle-transition: var(
      --toggle-transition,
      background-color var(--eds-transition-duration-base) var(--eds-transition-timing-function-base)
    );
  }

  [part='base'] {
    display: inline-flex;
    flex-direction: var(--eds-toggle-flex-direction);
    align-items: var(--eds-toggle-align-items);
    gap: var(--eds-toggle-gap);
    cursor: pointer;
    -webkit-user-select: none;
    -moz-user-select: none;
    -ms-user-select: none;
    user-select: none;
  }

  [part='input'] {
    position: absolute;
    width: 1px;
    height: 1px;
    padding: 0;
    margin: -1px;
    overflow: hidden;
    clip: rect(0, 0, 0, 0);
    white-space: nowrap;
    border-width: 0;
  }

  [part='control'] {
    display: inline-flex;
    align-items: center;
    position: relative;
    height: var(--eds-toggle-height);
    width: var(--eds-toggle-width);
  }

  :host([disabled]) [part='base'] {
    cursor: not-allowed;
    opacity: 0.5;
  }

  [part='track'] {
    width: 100%;
    height: 100%;
    background-color: var(--eds-toggle-background-color);
    border-radius: var(--eds-toggle-border-radius);
    transition: var(--eds-toggle-transition);
  }

  [part='input']:checked ~ [part='control'] [part='track'] {
    background-color: var(--eds-toggle-checked-background-color);
  }

  [part='input']:focus-visible ~ [part='control'] {
    outline: var(--eds-stroke-025) var(--eds-border-style-base) var(--eds-toggle-checked-background-color);
    outline-offset: var(--eds-stroke-025);
    border-radius: var(--eds-toggle-border-radius);
  }

  [part='thumb'] {
    position: absolute;
    top: calc((var(--eds-toggle-height) - var(--eds-toggle-thumb-size)) / 2);
    left: calc((var(--eds-toggle-height) - var(--eds-toggle-thumb-size)) / 2);
    width: var(--eds-toggle-thumb-size);
    height: var(--eds-toggle-thumb-size);
    background-color: var(--eds-white);
    border-radius: 50%;
    transition: transform var(--eds-transition-duration-base) var(--eds-transition-timing-function-base);
  }

  [part='input']:checked ~ [part='control'] [part='thumb'] {
    transform: translateX(calc(var(--eds-toggle-width) - var(--eds-toggle-height)));
  }
`;
