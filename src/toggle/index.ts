import { LitElement, html, type TemplateResult, type CSSResultGroup } from 'lit';
import { property } from 'lit/decorators.js';
import { styles } from '../styles';
import { toggleStyle } from './toggle.style';

class Toggle extends LitElement {
  static styles: CSSResultGroup = [styles, toggleStyle];

  @property({ type: String }) id = crypto.randomUUID() as string;
  @property({ type: Boolean, reflect: true }) checked = false;
  @property({ type: Boolean, reflect: true }) disabled = false;
  @property({ type: String }) name?: string;
  @property({ type: String }) value = 'on';
  @property({ type: <PERSON><PERSON><PERSON>, reflect: true }) required = false;

  private _handleChange(event: Event) {
    const target = event.target as HTMLInputElement;
    this.checked = target.checked;
    this.dispatchEvent(
      new CustomEvent('change', {
        detail: {
          checked: this.checked,
        },
        bubbles: true,
        composed: true,
      }),
    );
  }

  protected render = (): TemplateResult => {
    return html`
      <label part="base">
        <input
          part="input"
          type="checkbox"
          id="${this.id}"
          role="switch"
          name=${this.name}
          value=${this.value}
          ?required=${this.required}
          .checked=${this.checked}
          .disabled=${this.disabled}
          @change=${this._handleChange}
        />
        <span part="control">
          <span part="track"></span>
          <span part="thumb"></span>
        </span>
        <slot></slot>
      </label>
    `;
  };
}

if (!customElements.get('eds-toggle')) {
  customElements.define('eds-toggle', Toggle);
}

declare global {
  interface HTMLElementTagNameMap {
    'eds-toggle': Toggle;
  }
}

export { Toggle };
