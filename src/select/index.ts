import { LitElement, html, type TemplateResult, type CSSResultGroup, type PropertyValues } from 'lit';
import { property, query, queryAssignedElements, state } from 'lit/decorators.js';
import { classMap } from 'lit/directives/class-map.js';
import { live } from 'lit/directives/live.js';
import { styles } from '../styles';
import { selectStyle } from './select.style';
import { SelectAppearanceValues, type SelectChangeEventDetail, type SelectOptionSelectedEventDetail } from './types';
import type { SelectOption } from './select-option';
import './select-option';
import '../icons';
import '../text';

class Select extends LitElement {
  static styles: CSSResultGroup = [styles, selectStyle];

  @property({ type: String }) id = '';
  @property({ type: String }) appearance: SelectAppearanceValues = 'default';
  @property({ type: String }) name = '';
  @property({ type: String }) value = '';
  @property({ type: String }) placeholder = '';
  @property({ type: String }) iconName = '';
  @property({ type: Boolean }) isRequired = false;
  @property({ type: Boolean }) isCompact = false;
  @property({ type: Boolean }) isInvalid = false;
  @property({ type: Boolean }) isMultiple = false;
  @property({ type: Boolean }) showTags = false;
  @property({ type: Boolean, reflect: true }) isDisabled = false;
  @property({ type: Boolean, reflect: true }) isLoading = false;
  @property({ type: Boolean, reflect: true }) isSearchable = false;
  @property({ type: Number }) maxOptionsShown = null;

  @state() private isOpen = false;
  @state() private allOptions: SelectOption[] = [];
  @state() private hasOptions = false;
  @state() private selectedOptions: SelectOption[] = [];
  @state() private hasUpdatedBefore = false;
  @state() private searchText = '';
  @state() private _justOpened = false;
  @state() private _newItemsLoaded = false;

  @queryAssignedElements({ selector: 'eds-select-option', flatten: true })
  selectOptions!: SelectOption[];
  @query('input') input!: HTMLInputElement;
  @query('slot') optionsSlot!: HTMLSlotElement;
  @query('[part="input-wrapper"]') inputWrapper!: HTMLDivElement;
  @query('[part="options-wrapper"]') optionsContainer!: HTMLDivElement;

  connectedCallback(): void {
    super.connectedCallback();
    this.setAttribute('role', 'combobox');
    this.setAttribute('aria-haspopup', 'listbox');
  }

  firstUpdated(): void {
    this.allOptions = [...this.selectOptions];
    this.hasOptions = this.allOptions.length > 0;
    this._updateSelectedOptions();

    // Handle pre-set value by syncing options first
    if (this.value) {
      this._syncOptionsFromValue();
    } else {
      // Default behavior when no value is pre-set
      if (!this.isMultiple && this.selectedOptions.length > 1) {
        this._deselectAllOptions();
        const firstSelectableOption = this._findFirstSelectableOption();
        if (firstSelectableOption) firstSelectableOption.isSelected = true;
      }

      this._updateSelectedOptions();
      this._updateSelectValue();
    }
  }

  protected updated(changedProperties: PropertyValues): void {
    if (changedProperties.has('isMultiple')) {
      this._updateSelectValue();
    }

    if (changedProperties.has('value')) {
      this._syncOptionsFromValue();
    }

    let scrollToBottomAfterUpdate = false;

    if (changedProperties.has('isLoading') && !this.isLoading && this._newItemsLoaded) {
      scrollToBottomAfterUpdate = true;
    }

    if (changedProperties.has('isLoading') && !this.isLoading) {
      if (this.hasUpdatedBefore && this.isSearchable) {
        this._filterSearch(this.searchText);
      } else {
        this.hasUpdatedBefore = true;
      }
    }

    if (changedProperties.has('maxOptionsShown') || changedProperties.has('isOpen')) {
      if (this.isOpen) {
        this._updateDropdownHeight();
      }
    }

    this._observeOptionChanges();
    this._updateSelect();

    if (this.isOpen && this.optionsContainer) {
      if (scrollToBottomAfterUpdate) {
        requestAnimationFrame(() => {
          const container = this.optionsContainer;
          const desiredOffsetFromBottom = 15;

          const maxScrollTop = container.scrollHeight - container.clientHeight;

          const targetScrollTop = Math.max(0, maxScrollTop - desiredOffsetFromBottom);

          if (maxScrollTop > 0 && container.scrollTop !== targetScrollTop) {
            container.scrollTop = targetScrollTop;
          }
          this._newItemsLoaded = false;
        });
      }
    }
  }

  private _observeOptionChanges(): void {
    const observer = new MutationObserver((mutations) => {
      let optionsChanged = false;

      mutations.forEach((mutation) => {
        if (mutation.type === 'childList') {
          optionsChanged = true;
        }
      });

      if (optionsChanged) {
        this.allOptions = [...this.selectOptions];
        this.hasOptions = this.allOptions.length > 0;
        this._updateSelectedOptions();

        // Sync value when options change (e.g., options loaded after value was set)
        if (this.value) {
          this._syncOptionsFromValue();
        }
      }
    });

    observer.observe(this, { childList: true, subtree: true });
  }

  private _deselectAllOptions(): void {
    this.allOptions.forEach((option) => (option.isSelected = false));
  }

  private _findFirstSelectableOption(): SelectOption | undefined {
    return this.allOptions.find((option) => !option.isDisabled);
  }

  private _updateSelectedOptions(): void {
    this.selectedOptions = this.allOptions.filter((option) => option.isSelected);
  }

  private _inputFocus(): void {
    this.input.focus();
    if (this.isSearchable) {
      const length = this.input.value.length;
      this.input.setSelectionRange(length, length);
    }
  }

  private _toggleSelect(): void {
    if (this.isDisabled) return;

    this._inputFocus();
    this._removeOptionFocus();

    this.isOpen = !this.isOpen;
    this.setAttribute('aria-expanded', this.isOpen.toString());

    if (this.isOpen) {
      this._justOpened = true;
      this._updateDropdownHeight();

      if (this.optionsContainer) {
        this.optionsContainer.scrollTop = 0;
      }

      this._newItemsLoaded = false;

      requestAnimationFrame(() => {
        setTimeout(() => {
          this._justOpened = false;
        }, 0);
      });
    } else {
      this._justOpened = false;

      // if (this.optionsContainer) {
      //   this.optionsContainer.scrollTop = 0;
      // }
    }
  }

  private _updateDropdownHeight(): void {
    if (!this.allOptions.length) return;

    let optionHeight = 0;
    if (this.allOptions[0] && this.allOptions[0].optionHeight) {
      optionHeight = this.allOptions[0].optionHeight;
    } else {
      const firstOption = this.allOptions[0];
      if (firstOption) {
        optionHeight = firstOption.clientHeight || 40;
      }
    }

    if (optionHeight && this.maxOptionsShown) {
      this.optionsContainer.style.maxHeight = `calc(${
        optionHeight * this.maxOptionsShown
      }px + (2 * var(--eds-select-options-wrapper-padding)) + ((${
        this.maxOptionsShown
      } - 1) * var(--eds-select-options-wrapper-gap)))`;
    }
  }

  private _updateSelectValue(): void {
    this._updateSelectedOptions();

    if (this.selectedOptions.length) {
      if (this.isMultiple) {
        this.value = '';
        this.input.value = '';
        this.input.placeholder = '';
      } else {
        const selectedOption = this.selectedOptions[0];
        this.value = selectedOption.value;
        this.input.value = selectedOption.label || selectedOption.textContent?.trim() || '';
      }
    } else {
      this.value = '';
      this.input.value = '';
      this.input.placeholder = this.placeholder;
    }

    this._dispatchChangeEvent();
  }

  private _dispatchChangeEvent(): void {
    this.dispatchEvent(
      new CustomEvent<SelectChangeEventDetail>('change', {
        bubbles: true,
        composed: true,
        detail: {
          value: this.value,
          selectedOption: !this.isMultiple ? this.selectedOptions[0] : null,
          selectedOptions: this.selectedOptions,
        },
      }),
    );
  }

  private _selectOption(option: SelectOption): void {
    if (this.isMultiple) {
      this._inputFocus();
      this.isOpen = true;
      option.isSelected = !option.isSelected;
      option.removeAttribute('focused');
    } else {
      this.isOpen = false;
      this._deselectAllOptions();
      option.isSelected = true;
    }

    this._updateSelectValue();
    if (this.isSearchable) {
      this._filterSearch('');
    }
  }

  private _filterSearch(value: string): void {
    this.searchText = value;
    const lowerCaseValue = value.toLowerCase();
    const fragment = document.createDocumentFragment();
    let hasVisibleOptions = false;

    this.allOptions.forEach((option: SelectOption) => {
      const label = option.label ? option.label.toLowerCase() : option.textContent?.toLowerCase().trim() ?? '';
      const isVisible = label.includes(lowerCaseValue);

      if (isVisible) {
        hasVisibleOptions = true;
        fragment.appendChild(option);
      }
    });

    this._clearOptionsSlot();

    this.optionsSlot.appendChild(fragment);
    this.hasOptions = hasVisibleOptions;
  }

  private _clearOptionsSlot(): void {
    this.optionsSlot.assignedNodes()?.forEach((node) => {
      if (node.parentNode) node.parentNode.removeChild(node);
    });

    while (this.optionsSlot.firstChild) {
      this.optionsSlot.removeChild(this.optionsSlot.firstChild);
    }
  }

  private _onInputChange(value: string): void {
    this.isOpen = true;

    if (this.isSearchable) {
      if (this.isMultiple) this.inputWrapper.setAttribute('data-value', value);
      this._filterSearch(value);
    } else {
      this.value = value;
    }
  }

  private _updateSelect(): void {
    const selectedOption = this.selectedOptions.length ? this.selectedOptions[0] : null;
    const focusedOption = this.allOptions.find((option) => option.hasAttribute('focused'));

    focusedOption?.removeAttribute('focused');

    if (!this.isMultiple && selectedOption) {
      this._updateInputValueFromSelectedOption(selectedOption);
    }
    if (this.isSearchable) {
      this._filterSearch('');
    }
  }

  private _updateInputValueFromSelectedOption(selectedOption: SelectOption): void {
    const displayText = selectedOption.label || selectedOption.textContent?.trim() || '';
    const lowerCaseInput = this.input.value.trim().toLowerCase();
    const lowerCaseOption = displayText.toLowerCase();

    if (lowerCaseInput !== lowerCaseOption) {
      this.input.value = displayText;
    }
  }

  private _removeOption(e: Event, value: string): void {
    e.stopPropagation();
    this.isOpen = true;
    this._inputFocus();

    const option = this.allOptions.find((opt) => opt.value === value);
    if (option) option.isSelected = false;

    this._updateSelectedOptions();
    this._updateSelectValue();
  }

  private _clearInput(): void {
    this.input.value = '';
    this.searchText = '';
    this._filterSearch('');
  }

  private _removeTag(): void {
    const targetOption = this.selectedOptions[this.selectedOptions.length - 1];
    if (!targetOption) return;

    targetOption.isSelected = false;
    this._updateSelectedOptions();
    this._updateSelectValue();
  }

  private _clearTags(e: Event): void {
    e.stopPropagation();
    this._deselectAllOptions();
    this._updateSelectedOptions();
    this._updateSelectValue();
    this._clearInput();
  }

  private _highlightOption(direction: 'next' | 'previous'): void {
    const visibleOptions = Array.from(this.optionsSlot.assignedElements()) as SelectOption[];
    if (!visibleOptions.length) return;

    const currentIndex = visibleOptions.findIndex((opt) => opt.hasAttribute('focused'));
    let newIndex: number;

    if (direction === 'next') {
      newIndex = currentIndex === -1 ? 0 : (currentIndex + 1) % visibleOptions.length;
    } else {
      newIndex =
        currentIndex === -1
          ? visibleOptions.length - 1
          : (currentIndex - 1 + visibleOptions.length) % visibleOptions.length;
    }

    visibleOptions[currentIndex]?.removeAttribute('focused');

    visibleOptions[newIndex].setAttribute('focused', 'true');
    visibleOptions[newIndex].scrollIntoView({ block: 'nearest' });
  }

  private _selectHighlightedOption(): void {
    const highlightedOption = this.allOptions.find((option) => option.hasAttribute('focused'));
    if (highlightedOption && !highlightedOption.isDisabled) {
      this._selectOption(highlightedOption);
    }
  }

  private _handleScroll(e: Event): void {
    const container = this.optionsContainer;

    if (!container || this._justOpened) {
      return;
    }

    const isScrollable = container.scrollHeight > container.clientHeight;
    const hasScrolled = container.scrollTop > 0;
    const isAtBottom = container.scrollHeight - container.scrollTop <= container.clientHeight + 5;

    if (isScrollable && hasScrolled && isAtBottom) {
      this.dispatchEvent(
        new CustomEvent('scroll-bottom', {
          bubbles: true,
          composed: true,
        }),
      );
      this._newItemsLoaded = true;
    }
  }

  private _onKeyDown(e: KeyboardEvent): void {
    switch (e.key) {
      case 'Escape':
        this._updateSelect();
        break;

      case 'Enter':
        if (this.isOpen) {
          e.preventDefault();
          this._selectHighlightedOption();
        }
        break;

      case 'ArrowDown':
        e.preventDefault();
        if (!this.isOpen) {
          this.isOpen = true;
          this.setAttribute('aria-expanded', 'true');
        } else {
          this._highlightOption('next');
        }
        break;

      case 'ArrowUp':
        e.preventDefault();
        if (this.isOpen) {
          this._highlightOption('previous');
        }
        break;

      case 'Tab':
        if (this.isOpen) {
          this._updateSelect();
        }
        break;

      case 'Backspace':
      case 'Delete':
        if (this.isMultiple && this.isSearchable && !this.input.value.length) {
          this._removeTag();
        }
        break;
    }
  }

  private _renderTags(options: SelectOption[]): TemplateResult[] {
    return options.map(
      (option) => html`
        <button part="tag" type="button" @mousedown="${(e: Event) => e.preventDefault()}">
          <eds-text
            part="tag-label"
            as="span"
            size="sm"
            weight="regular"
            text="${option.label || option.textContent?.trim()}"
          ></eds-text>
          <eds-icon
            part="remove-tag"
            @click="${(e: Event) => this._removeOption(e, option.value)}"
            name="cancel"
          ></eds-icon>
        </button>
      `,
    );
  }

  private _renderClearTagsButton(): TemplateResult {
    return html`<eds-icon role="button" part="clear-tags" @click="${this._clearTags}" name="cancel"></eds-icon>`;
  }

  private _renderPlaceholder(): TemplateResult {
    if (!this.showTags && this.selectedOptions.length && !this.value) {
      return html`<div part="placeholder">${this.selectedOptions.length} selected</div>`;
    } else if (this.placeholder && !this.value && !this.selectedOptions.length) {
      return html`<div part="placeholder">${this.placeholder}</div>`;
    }

    return html``;
  }

  private _renderMultipleInput(): TemplateResult {
    return html`
      <div part="value-wrapper">
        ${this.isMultiple && this.showTags && this.selectedOptions.length
          ? this._renderTags(this.selectedOptions)
          : html``}
        ${this._renderPlaceholder()}
        <div part="input-wrapper" data-value>${this._renderInput()}</div>
      </div>
      ${this.selectedOptions.length ? this._renderClearTagsButton() : html``}
    `;
  }

  private _renderInput(): TemplateResult {
    const uniqueId = this.id || `eds-select-${crypto.randomUUID()}`;

    return html`
      <input
        part="input"
        id="${uniqueId}"
        name="${this.name}"
        type="text"
        .value="${live(this.input?.value || this.value)}"
        placeholder="${this.placeholder}"
        ?disabled="${this.isDisabled}"
        ?required="${this.isRequired}"
        ?readonly="${!this.isSearchable}"
        spellcheck="false"
        aria-expanded="${this.isOpen}"
        aria-invalid="${this.isInvalid}"
        aria-controls="${uniqueId}-listbox"
        autocapitalize="none"
        autocomplete="off"
        tabindex="0"
        @focus="${this._inputFocus}"
        @blur="${() => {
          this.isOpen = false;
          this.setAttribute('aria-expanded', 'false');
        }}"
        @input="${(e: InputEvent) => this._onInputChange((e.target as HTMLInputElement).value)}"
        @keydown="${this._onKeyDown}"
      />
    `;
  }

  private _removeOptionFocus(): void {
    this.allOptions.forEach((option) => {
      if (option.hasAttribute('focused')) option.removeAttribute('focused');
    });
  }

  protected render(): TemplateResult {
    const uniqueId = this.id || `eds-select-${crypto.randomUUID()}`;

    return html`
      <div
        part="base"
        class="${classMap({
          disabled: this.isDisabled,
          open: this.isOpen,
        })}"
        @option-hover="${this._removeOptionFocus}"
        @option-selected="${(e: CustomEvent<SelectOptionSelectedEventDetail>) => {
          this._selectOption(e.detail.option as SelectOption);
        }}"
      >
        <div
          part="select"
          class="${classMap({
            'show-tags': this.isMultiple && this.selectedOptions.length,
            compact: this.isCompact,
            invalid: this.isInvalid,
            [this.appearance]: true,
          })}"
          @click="${this._toggleSelect}"
          @mousedown="${(e: Event) => {
            e.preventDefault();
          }}"
        >
          ${this.iconName ? html`<eds-icon part="icon" name="${this.iconName}"></eds-icon>` : html``}
          ${this.isMultiple ? this._renderMultipleInput() : this._renderInput()}
          <eds-icon part="arrow-down" name="arrowDown"></eds-icon>
        </div>

        <div
          part="options-wrapper"
          role="listbox"
          id="${uniqueId}-listbox"
          aria-multiselectable="${this.isMultiple}"
          @mousedown="${(e: Event) => {
            e.preventDefault();
          }}"
          @scrollend="${this._handleScroll}"
        >
          ${!this.hasOptions && !this.isLoading
            ? html`<eds-text part="no-options" as="span" size="md" weight="regular" text="No options"></eds-text>`
            : ''}
          <slot></slot>
          ${this.isLoading
            ? html`<eds-text part="loading" as="span" size="md" weight="regular" text="Loading..."></eds-text>`
            : html``}
        </div>
      </div>
    `;
  }

  /**
   * Updates the input display without triggering change events.
   * Used when syncing from external value changes to avoid infinite loops.
   */
  private _updateInputDisplay(): void {
    if (this.selectedOptions.length) {
      if (this.isMultiple) {
        this.input.value = '';
        this.input.placeholder = '';
      } else {
        const selectedOption = this.selectedOptions[0];
        this.input.value = selectedOption.label || selectedOption.textContent?.trim() || '';
      }
    } else {
      this.input.value = '';
      this.input.placeholder = this.placeholder;
    }
  }

  /**
   * Synchronizes option selection state based on the current value property.
   * This enables two-way data binding when value is set programmatically.
   */
  private _syncOptionsFromValue(): void {
    if (!this.allOptions.length) return;

    if (this.isMultiple) {
      // For multiple select, handle array of values or comma-separated string
      let targetValues: string[] = [];

      if (this.value) {
        // Handle both array and comma-separated string formats
        if (Array.isArray(this.value)) {
          targetValues = this.value;
        } else if (typeof this.value === 'string') {
          targetValues = this.value
            .split(',')
            .map((v) => v.trim())
            .filter((v) => v !== '');
        }
      }

      // Update all options based on target values
      this.allOptions.forEach((option) => {
        option.isSelected = targetValues.includes(option.value);
      });
    } else {
      // For single select, deselect all first, then select the matching option
      this._deselectAllOptions();

      if (this.value) {
        const targetOption = this.allOptions.find((option) => option.value === this.value);
        if (targetOption) {
          targetOption.isSelected = true;
        }
      }
    }

    // Update internal state after syncing
    this._updateSelectedOptions();

    // Update input display without triggering another value change
    this._updateInputDisplay();
  }
}

if (!customElements.get('eds-select')) {
  customElements.define('eds-select', Select);
}

declare global {
  interface HTMLElementTagNameMap {
    'eds-select': Select;
  }
}

export { Select };
