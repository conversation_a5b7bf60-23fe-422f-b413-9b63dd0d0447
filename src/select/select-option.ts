import { LitElement, html, type TemplateResult, type CSSResultGroup } from 'lit';
import { property, state } from 'lit/decorators.js';
import { classMap } from 'lit/directives/class-map.js';
import { styles } from '../styles';
import { selectStyle } from './select.style';
import { type Select } from './index';
import { type SelectOptionSelectedEventDetail } from './types';
import '../text';

class SelectOption extends LitElement {
  static styles: CSSResultGroup = [styles, selectStyle];

  @property({type: String}) label = '';
  @property({type: String}) value = '';
  @property({type: String}) name = '';
  @property({type: Boolean, reflect: true}) isSelected = false;
  @property({type: Boolean, reflect: true}) isDisabled = false;

  @state() private isMultiple = false;
  @state() public optionHeight = 0;

  connectedCallback(): void {
    super.connectedCallback();
    this.setAttribute('role', 'option');
    this.setAttribute('aria-selected', this.isSelected.toString());
    
    if (this.isDisabled) {
      this.setAttribute('aria-disabled', 'true');
    }

    requestAnimationFrame(() => this.optionHeight = this._measureOptionHeight());
  }

  updated(changedProperties: Map<string, unknown>): void {
    if (changedProperties.has('isSelected')) {
      this.setAttribute('aria-selected', this.isSelected.toString());
    }
    
    if (changedProperties.has('isDisabled')) {
      if (this.isDisabled) {
        this.setAttribute('aria-disabled', 'true');
      } else {
        this.removeAttribute('aria-disabled');
      }
    }
  }

  firstUpdated(): void {
    const selectEl = this.closest('eds-select') as Select;
    if (selectEl) {
      this.isMultiple = selectEl.isMultiple;
    }
  }

  private _measureOptionHeight(): number {
    const optionWrapper = this.shadowRoot?.querySelector('[part="option-wrapper"]');
    if (optionWrapper) {
      return optionWrapper.getBoundingClientRect().height;
    }
    return 0;
  }

  private _renderOption(): TemplateResult {
    return html`
      ${this.label 
        ? html`<eds-text part="label" as="span" size="md" weight="regular" text="${this.label}"></eds-text>` 
        : html`<slot part="label"></slot>`}
    `;
  }

  private _renderCheckboxOption(): TemplateResult {
    const uniqueId = `option-${this.value || crypto.randomUUID()}`;
    
    return html`
      <label for="${uniqueId}" part="multiple-option">
        <input 
          id="${uniqueId}" 
          part="checkbox" 
          type="checkbox" 
          ?checked="${this.isSelected}"
          ?disabled="${this.isDisabled}"
        >
        <span part="checkbox-mark"></span>
        ${this.label 
          ? html`<eds-text part="label" as="span" size="md" weight="regular" text="${this.label}"></eds-text>` 
          : html`<slot part="option"></slot>`}
      </label>`;
  }

  protected render(): TemplateResult {
    return html`
      <div part="option-wrapper" 
           class="${classMap({
             'disabled': this.isDisabled
           })}">
        <div part="option"
             @click="${this._handleClick}"
             @mouseover="${this._handleMouseOver}"
             aria-selected="${this.isSelected}"
             role="option">
          ${this.isMultiple ? this._renderCheckboxOption() : this._renderOption()}
        </div>
      </div>
    `;
  }

  private _handleMouseOver(): void {
    if (this.isDisabled) return;
    
    this.dispatchEvent(new CustomEvent('option-hover', {
      bubbles: true,
      composed: true,
    }));
  }

  private _handleClick(e: Event): void {
    e.preventDefault();
    
    if (this.isDisabled) return;
    
    this.dispatchEvent(new CustomEvent<SelectOptionSelectedEventDetail>('option-selected', {
      bubbles: true,
      composed: true,
      detail: {
        option: this,
      }
    }));
  }
}

if(!customElements.get('eds-select-option')) {
  customElements.define('eds-select-option', SelectOption);
}

declare global {
  interface HTMLElementTagNameMap {
    "eds-select-option": SelectOption;
  }
}

export { SelectOption };