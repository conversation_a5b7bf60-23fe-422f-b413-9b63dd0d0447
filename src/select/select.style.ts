import { css, type CSSResultGroup } from 'lit';

export const selectStyle: CSSResultGroup = css`
  :host {
    --eds-select-gap: var(--select-gap, var(--eds-spacing-150));
    --eds-select-align-items: var(--select-align-items, center);
    --eds-select-font-size: var(--select-font-size, var(--eds-font-size-body-md));
    --eds-select-icon-size: var(--select-icon-size, var(--eds-sizing-500));
    --eds-select-line-height: var(--select-line-height, var(--eds-line-height-body-md));
    --eds-select-border-radius: var(--select-border-radius, var(--eds-radius-200));
    --eds-select-placeholder-color: var(--select-placeholder-color, var(--eds-colors-text-light));
    --eds-select-transition-properties: var(--select-transition-properties, var(--eds-transition-property-base) var(--eds-transition-duration-base) var(--eds-transition-timing-function-base));
    
    /* Appearance Default */
    --eds-select-border-properties: var(--select-border-properties, var(--eds-stroke-025) var(--eds-border-style-base) var(--eds-border-color-light));
    --eds-select-background-color: var(--select-background-color, var(--eds-colors-surface-default));
    --eds-select-hover-border-properties: var(--select-hover-border-properties, var(--eds-stroke-025) var(--eds-border-style-base) var(--eds-border-color-default));
    --eds-select-hover-background-color: var(--select-background-color, var(--eds-colors-primary-lighter));
    --eds-select-focus-border-properties: var(--select-focus-border-properties, var(--eds-stroke-025) var(--eds-border-style-base) var(--eds-colors-primary-default));
    --eds-select-focus-background-color: var(--select-focus-background-color, var(--eds-colors-surface-default));
    --eds-select-focus-box-shadow: var(--select-focus-box-shadow, var(--eds-ring-sm));
    
    /* Appearance Subtle */
    --eds-select-subtle-border-properties: var(--select-subtle-border-properties, var(--eds-stroke-025) var(--eds-border-style-base) transparent);
    --eds-select-subtle-background-color: var(--select-subtle-background-color, var(--eds-colors-base-white));
    --eds-select-subtle-hover-border-properties: var(--select-subtle-hover-border-properties, var(--eds-stroke-025) var(--eds-border-style-base) var(--eds-border-color-default));
    --eds-select-subtle-hover-background-color: var(--select-subtle-hover-background-color, var(--eds-colors-primary-lighter));
    --eds-select-subtle-focus-border-properties: var(--select-subtle-focus-border-properties, var(--eds-stroke-025) var(--eds-border-style-base) var(--eds-colors-primary-default));
    --eds-select-subtle-focus-background-color: var(--select-subtle-focus-background-color, var(--eds-colors-surface-default));
    --eds-select-subtle-focus-box-shadow: var(--select-subtle-focus-box-shadow, var(--eds-ring-sm));
    
    /* Invalid */
    --eds-select-invalid-border-properties: var(--select-invalid-border-properties, var(--eds-stroke-025) var(--eds-border-style-base) var(--eds-colors-danger-default));
    
    /* Disabled */
    --eds-select-disabled-text-color: var(--select-disabled-text-color, var(--eds-colors-text-disabled));
    --eds-select-disabled-border-properties: var(--select-disabled-border-properties, var(--eds-stroke-025) var(--eds-border-style-base) var(--eds-border-color-default));
    --eds-select-disabled-background-color: var(--select-disabled-background-color, var(--eds-colors-surface-disabled));
    
    /* Size Default */
    --eds-select-height: var(--select-height, var(--eds-sizing-800));
    --eds-select-padding: var(--select-padding, var(--eds-spacing-200) var(--eds-spacing-300));
    
    /* Size Compact */
    --eds-select-compact-height: var(--select-compact-height, var(--eds-sizing-700));
    --eds-select-compact-padding: var(--select-padding-compact, var(--eds-spacing-150) var(--eds-spacing-300));

    --eds-select-options-wrapper-background-color: var(--select-options-wrapper-background-color, var(--eds-colors-surface-default));
    --eds-select-options-wrapper-margin-top: var(--select-options-wrapper-margin-top, var(--eds-spacing-200));
    --eds-select-options-wrapper-max-height: var(--select-options-wrapper-max-height, 240px);
    --eds-select-options-wrapper-gap: var(--select-options-wrapper-gap, 2px);
    --eds-select-options-wrapper-z-index: var(--select-options-wrapper-z-index, 1000);
    --eds-select-options-wrapper-border-properties: var(--select-options-wrapper-border-properties, var(--eds-stroke-025) var(--eds-border-style-base) var(--eds-border-color-default));
    --eds-select-options-wrapper-border-radius: var(--select-options-wrapper-border-radius, var(--eds-radius-200));
    --eds-select-options-wrapper-padding: var(--select-options-wrapper-padding, var(--eds-spacing-200));
    --eds-select-options-wrapper-box-shadow: var(--select-options-wrapper-box-shadow, var(--eds-shadow-sm));
    
    --eds-select-option-border-radius: var(--select-option-border-radius, var(--eds-radius-200));
    --eds-select-option-padding: var(--select-option-padding, var(--eds-spacing-200));
    --eds-select-option-align-items: var(--select-option-align-items, center);
    --eds-select-option-gap: var(--select-option-gap, var(--eds-spacing-200));
    --eds-select-option-hover-background-color: var(--select-option-hover-background-color, var(--eds-colors-primary-lighter));
    --eds-select-option-selected-background-color: var(--select-option-selected-background-color, var(--eds-colors-primary-lighter));
    --eds-select-option-disabled-text-color: var(--select-option-disabled-text-color, var(--eds-colors-text-disabled));
    
    --eds-select-value-wrapper-row-gap: var(--select-value-wrapper-row-gap, var(--eds-spacing-100));
    --eds-select-value-wrapper-column-gap: var(--select-value-wrapper-column-gap, var(--eds-spacing-200));

    --eds-select-tag-height: var(--select-tag-height, var(--eds-sizing-500));
    --eds-select-tag-padding: var(--select-tag-padding, var(--eds-spacing-050) var(--eds-spacing-100));
    --eds-select-tag-gap: var(--select-tag-gap, var(--eds-spacing-050));
    --eds-select-tag-background-color: var(--select-tag-background-color, var(--eds-colors-surface-default));
    --eds-select-tag-border-properties: var(--select-tag-border-properties, var(--eds-stroke-025) var(--eds-border-style-base) var(--eds-border-color-default));
    --eds-select-tag-border-radius: var(--select-tag-border-radius, var(--eds-radius-100));
    --eds-select-tag-icon-size: var(--select-tag-icon-size, var(--eds-sizing-300));
    --eds-select-tag-hover-background-color: var(--select-tag-hover-background-color, var(--eds-colors-primary-lighter));
    --eds-select-tag-hover-border-properties: var(--select-tag-hover-border-properties, var(--eds-stroke-025) var(--eds-border-style-base) var(--eds-border-color-dark));
    --eds-select-tag-disabled-border-properties: var(--select-tag-disabled-border-properties, var(--eds-stroke-025) var(--eds-border-style-base) var(--eds-border-color-dark));

    --eds-select-clear-tags-background-color: var(--select-clear-tags-background-color, var(--eds-colors-primary-light));
    --eds-select-clear-tags-hover-background-color: var(--select-clear-tags-background-color, var(--eds-border-color-default));
    --eds-select-clear-tags-border-properties: var(--select-clear-tags-border-properties, var(--eds-stroke-025) var(--eds-border-style-base) var(--eds-border-color-default));
    --eds-select-clear-tags-border-radius: var(--select-clear-tags-border-radius, var(--eds-radius-full));
  }

  [part="base"] {
    position: relative;
  }
  
  [part="base"].disabled {
    cursor: not-allowed;

    [part="input"],
    [part="placeholder"],
    [part="icon"],
    [part="arrow-down"],
    [part="remove-tag"],
    [part="clear-tags"],
    [part="tag-label"] {
      color: var(--eds-select-disabled-text-color);
    }
    
    [part="tag"] {
      border: var(--eds-select-tag-disabled-border-properties);
      background-color: var(--eds-select-disabled-background-color);
    }
  }
  
  [part="select"] {
    display: flex;
    align-items: var(--eds-select-align-items);
    gap: var(--eds-select-gap);
    width: 100%;
    min-height: var(--eds-select-height);
    padding: var(--eds-select-padding);
    border-radius: var(--eds-select-border-radius);
    cursor: pointer;
    transition: var(--eds-select-transition-properties);
  }

  [part="select"].compact {
    min-height: var(--eds-select-compact-height);
    padding: var(--eds-select-compact-padding);
  }

  [part="select"].default {
    border: var(--eds-select-border-properties);
    background-color: var(--eds-select-background-color);
  }

  [part="select"].default:hover {
    border: var(--eds-select-hover-border-properties);
    background-color: var(--eds-select-hover-background-color);
  }

  [part="select"].default:focus-within {
    border: var(--eds-select-focus-border-properties);
    background-color: var(--eds-select-focus-background-color);
    box-shadow: var(--eds-select-focus-box-shadow);
  }
  
  [part="select"].subtle {
    border: var(--eds-select-subtle-border-properties);
    background-color: var(--eds-select-subtle-background-color);
  }
  
  [part="select"].subtle:hover {
    border: var(--eds-select-subtle-hover-border-properties);
    background-color: var(--eds-select-subtle-hover-background-color);
  }
  
  [part="select"].subtle:focus-within {
    border: var(--eds-select-subtle-focus-border-properties);
    background-color: var(--eds-select-subtle-focus-background-color);
    box-shadow: var(--eds-select-subtle-focus-box-shadow);
  }

  [part="select"].invalid {
    border: var(--eds-select-invalid-border-properties);
  }

  [part="select"]:has([part="input"][disabled]) {
    pointer-events: none;
    border: var(--eds-select-disabled-border-properties);
    background-color: var(--eds-select-disabled-background-color);
  }
  
  [part="value-wrapper"] {
    display: flex;
    flex: 1 1 0;
    align-items: center;
    row-gap: var(--eds-select-value-wrapper-row-gap);
    column-gap: var(--eds-select-value-wrapper-column-gap);
    overflow: hidden;
  }
  
  [part="value-wrapper"]:has([part="tag"]) {
    display: flex;
    flex-wrap: wrap;
  }

  [part="input-wrapper"] {
    box-sizing: border-box;
    display: inline-grid;
    grid-template-columns: 0 min-content;
    grid-area: 1 / 1 / 2 / 3;
    flex: 1 1 auto;
    visibility: visible;
  }

  [part="input-wrapper"]:after {
    content: attr(data-value) " ";
    visibility: hidden;
    white-space: pre;
    grid-area: 1 / 2;
    min-width: 2px;
    border: 0;
    margin: 0;
    outline: 0;
    padding: 0;
  }

  [part="input"] {
    font-size: var(--eds-select-font-size);
    line-height: var(--eds-select-line-height);
    height: 100%;
    border: 0;
    margin: 0;
    outline: 0;
    padding: 0;
    min-width: 2px;
    width: 100%;
    background: 0 center;
    grid-area: 1 / 2;
  }

  [part="input"][readonly] {
    cursor: pointer;
  }

  [part="checkbox"] {
    position: absolute;
    opacity: 0;
  }

  [part="input"].compact {
    --eds-select-padding: var(--select-padding-compact)
  }

  [part="input"].invalid {
    border-color: var(--eds-select-invalid-color);
  }

  [part="input"]::placeholder,
  [part="placeholder"],
  [part="no-options"],
  [part="loading"] {
    font-size: var(--eds-select-font-size);
    line-height: var(--eds-select-line-height);
    color: var(--eds-select-placeholder-color);
  }

  [part="placeholder"] {
    grid-area: 1 / 1 / 2 / 3;
  }

  [part="select"]:focus-within {
    border-color: var(--eds-select-focus-border-color);
    outline: none;
  }

  [part="input"]:disabled {
    pointer-events: var(--eds-pointer-events-disabled);
    opacity: var(--eds-select-disabled-opacity);
  }
  
  [part="clear-tags"]::part(base) {
    display: flex;
    background-color: var(--eds-select-clear-tags-background-color);
    border-radius: var(--eds-select-clear-tags-border-radius);
    border: var(--eds-select-clear-tags-border-properties);
    transition: var(--eds-select-transition-properties);
  }

  [part="clear-tags"]::part(base):hover {
    background-color: var(--eds-select-clear-tags-hover-background-color);
  }

  [part="options-wrapper"] {
    position: absolute;
    top: 100%;
    width: 100%;
    display: none;
    flex-direction: column;
    overflow-y: auto;
    background-color: var(--eds-select-options-wrapper-background-color);
    margin-top: var(--eds-select-options-wrapper-margin-top);
    max-height: var(--eds-select-options-wrapper-max-height);
    gap: var(--eds-select-options-wrapper-gap);
    z-index: var(--eds-select-options-wrapper-z-index);
    border: var(--eds-select-options-wrapper-border-properties);
    border-radius: var(--eds-select-options-wrapper-border-radius);
    padding: var(--eds-select-options-wrapper-padding);
    box-shadow: var(--eds-select-options-wrapper-box-shadow);
  }

  [part="base"].open [part="options-wrapper"] {
    display: flex;
  }

  [part="options-wrapper"]::-webkit-scrollbar {
    width: var(--eds-sizing-100);
  }

  [part="options-wrapper"]::-webkit-scrollbar-thumb {
    background-color: var(--eds-colors-primary-light);
    border-radius: var(--eds-radius-full)
  }
  
  [part="option-wrapper"].disabled {
    cursor: not-allowed;

    [part="option"] {
      pointer-events: none;

      [part="label"] {
        color: var(--eds-select-option-disabled-text-color);
      }
    }
  }

  [part="option"] {
    position: relative;
    display: flex;
    align-items: var(--eds-select-option-align-items);
    gap: var(--eds-select-option-gap);
    border-radius: var(--eds-select-option-border-radius);
    padding: var(--eds-select-option-padding);
    transition: var(--eds-select-transition-properties);
    cursor: pointer;
  }

  [part="option"]:hover {
    background-color: var(--eds-select-option-hover-background-color);
  }

  [part="option"][aria-selected="true"],
  ::slotted(eds-select-option[focused]) {
    background-color: var(--eds-select-option-selected-background-color);
    border-radius: var(--eds-select-option-border-radius);
  }

  [part="multiple-option"] {
    width: 100%;
    cursor: pointer;
    display: grid;
    grid-template-columns: auto 1fr;
    align-items: center;
    gap: var(--eds-spacing-200);
  }
  
  [part="label"] {
    width: 100%;
    cursor: pointer;
    display: flex;
    align-items: center;
  }

  [part="select"],
  [part="input"],
  [part="label"],
  [part="label"]::part(base),
  [part="tag-label"] {
    text-overflow: ellipsis;
    overflow: hidden;
    white-space: nowrap;
  }

  [part="checkbox"] {
    cursor: pointer;
    width: var(--eds-sizing-400);
    height: var(--eds-sizing-400);
    opacity: 0;
    z-index: 1;
    margin: 0;
    grid-area: 1 / 1 / 2 / 2;
  }

  [part="checkbox-mark"] {
    flex-shrink: 0;
    width: var(--eds-sizing-400);
    height: var(--eds-sizing-400);
    border: var(--eds-stroke-025) var(--eds-border-style-base) var(--eds-border-color-default);
    border-radius: var(--eds-radius-100);
    grid-area: 1 / 1 / 2 / 2;
  }

  [part="option"][aria-selected="true"] [part="checkbox-mark"] {
    border: var(--eds-stroke-025) var(--eds-border-style-base) var(--eds-colors-primary-dark);
    background-color: var(--eds-colors-primary-default);
    background-image: url("data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMTIiIGhlaWdodD0iMTIiIHZpZXdCb3g9IjAgMCAxMiAxMiIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPHBhdGggZD0iTTIuNSA3TDQuMjUgOC43NUw5LjUgMy4yNSIgc3Ryb2tlPSJ3aGl0ZSIgc3Ryb2tlLXdpZHRoPSIxLjUiIHN0cm9rZS1saW5lY2FwPSJyb3VuZCIgc3Ryb2tlLWxpbmVqb2luPSJyb3VuZCIvPgo8L3N2Zz4K");
    background-repeat: no-repeat;
    background-position: 50%;
  }

  [part="no-options"],
  [part="loading"] {
    display: flex;
    align-items: center;
    justify-content: center;
    text-align: center;
    pointer-events: var(--eds-pointer-events-disabled);
  }

  [part="tag"] {
    display: flex;
    align-items: center;
    cursor: pointer;
    height: var(--eds-select-tag-height);
    max-width: 100%;
    padding: var(--eds-select-tag-padding);
    gap: var(--eds-select-tag-gap);
    background-color: var(--eds-select-tag-background-color);
    border: var(--eds-select-tag-border-properties);
    border-radius: var(--eds-select-tag-border-radius);
    transition: var(--eds-select-transition-properties);
  }

  [part="tag"]:hover {
    background-color: var(--eds-select-tag-hover-background-color);
    border: var(--eds-select-tag-hover-border-properties)
  }

  [part="tag-label"] {
    font-size: 12px;
  }
  
  [part="remove-tag"] {
    width: var(--eds-select-tag-icon-size);
    height: var(--eds-select-tag-icon-size);
    flex-shrink: 0;
  }
  
  [part="clear-tags"],
  [part="arrow-down"],
  [part="icon"] {
    width: var(--eds-select-icon-size);
    height: var(--eds-select-icon-size);
    flex-shrink: 0;
}
`;