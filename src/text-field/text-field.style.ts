import { css } from "lit";

export const textFieldStyle = css`
  :host {
    --eds-text-field-font-size: var(--text-field-font-size, var(--eds-font-size-body-md));
    --eds-text-field-font-weight: var(--text-field-font-weight, var(--eds-font-weight-regular));
    --eds-text-field-line-height: var(--text-field-line-height, var(--eds-font-line-height-body-md));
    --eds-text-field-border-radius: var(--text-field-border-radius, var(--eds-radius-200));
    --eds-text-field-gap: var(--text-field-gap, var(--eds-spacing-150));
    --eds-text-field-color: var(--text-field-color, var(--eds-colors-text-default));
    --eds-text-field-placeholder-color: var(--text-field-placeholder-color, var(--eds-colors-text-light));
    --eds-text-field-icon-leading-size: var(--text-field-icon-leading-size, var(--eds-sizing-500));
    --eds-text-field-icon-leading-color: var(--text-field-icon-leading-color, var(--eds-colors-text-default));
    --eds-text-field-icon-trailing-size: var(--text-field-icon-trailing-size, var(--eds-sizing-500));
    --eds-text-field-icon-trailing-color: var(--text-field-icon-trailing-color, var(--eds-colors-text-default));
    
    /* DEFAULT SIZE */
    --eds-text-field-height: var(--text-field-height, var(--eds-sizing-800));
    --eds-text-field-padding: var(--text-field-padding, var(--eds-spacing-200) var(--eds-spacing-300));

    /* COMPACT SIZE */
    --eds-text-field-compact-height: var(--text-field-compact-height, var(--eds-sizing-700));
    --eds-text-field-compact-padding: var(--text-field-compact-padding, var(--eds-spacing-150) var(--eds-spacing-300));
    
    /* DEFAULT APPEARANCE */
    --eds-text-field-background-color: var(--text-field-background-color, var(--eds-colors-surface-default));
    --eds-text-field-border-properties: var(--text-field-border-properties, var(--eds-stroke-025) var(--eds-border-style-base) var(--eds-border-color-light));
    --eds-text-field-hover-background-color: var(--text-field-hover-background-color, var(--eds-colors-primary-lighter));
    --eds-text-field-hover-border-properties: var(--text-field-hover-border-properties, var(--eds-stroke-025) var(--eds-border-style-base) var(--eds-border-color-default));
    --eds-text-field-focus-background-color: var(--text-field-focus-background-color, var(--eds-colors-surface-default));
    --eds-text-field-focus-border-properties: var(--text-field-focus-border-properties, var(--eds-stroke-025) var(--eds-border-style-base) var(--eds-colors-primary-default));
    --eds-text-field-focus-box-shadow: var(--text-field-focus-box-shadow, var(--eds-ring-default));

    /* SUBTLE APPEARANCE */
    --eds-text-field-subtle-background-color: var(--text-field-subtle-background-color, transparent);
    --eds-text-field-subtle-border-properties: var(--text-field-subtle-border-properties, var(--eds-stroke-025) var(--eds-border-style-base) transparent);
    --eds-text-field-subtle-hover-background-color: var(--text-field-subtle-hover-background-color, var(--eds-colors-primary-lighter));
    --eds-text-field-subtle-hover-border-properties: var(--text-field-subtle-hover-border-properties, var(--eds-stroke-025) var(--eds-border-style-base) var(--eds-border-color-default));
    --eds-text-field-subtle-focus-background-color: var(--text-field-subtle-focus-background-color, var(--eds-colors-surface-default));
    --eds-text-field-subtle-focus-border-properties: var(--text-field-subtle-focus-border-properties, var(--eds-stroke-025) var(--eds-border-style-base) var(--eds-colors-primary-default));
    --eds-text-field-subtle-focus-box-shadow: var(--text-field-subtle-focus-box-shadow, var(--eds-ring-default));

    /* DISABLED */
    --eds-text-field-disabled-border-properties: var(--text-field-disabled-border-properties, var(--eds-stroke-025) var(--eds-border-style-base) var(--eds-border-color-default));
    --eds-text-field-disabled-background-color: var(--text-field-disabled-background-color, var(--eds-colors-surface-disabled));
    --eds-text-field-disabled-color: var(--text-field-disabled-color, var(--eds-colors-text-disabled));

    /* INVALID */
    --eds-text-field-invalid-border-properties: var(--text-field-invalid-border-properties, var(--eds-stroke-025) var(--eds-border-style-base) var(--eds-colors-danger-default));
  }
  
  [part="base"] {
    display: flex;
    align-items: center;
    overflow: hidden;
    overflow-wrap: break-word;
    pointer-events: auto;
    cursor: text;
    padding: var(--eds-text-field-padding);
    gap: var(--eds-text-field-gap);
    border-radius: var(--eds-text-field-border-radius);
    height: var(--eds-text-field-height);
    transition: var(--eds-transition-property-base) var(--eds-transition-duration-base) var(--eds-transition-timing-function-base);
  }

  [part="base"].compact {
    padding: var(--eds-text-field-compact-padding);
    height: var(--eds-text-field-compact-height);
  }

  [part="base"].default {
    border: var(--eds-text-field-border-properties);
    background-color: var(--eds-text-field-background-color);
  }

  [part="base"].default:hover {
    border: var(--eds-text-field-hover-border-properties);
    background-color: var(--eds-text-field-hover-background-color);
  }

  [part="base"].default:focus-within {
    border: var(--eds-text-field-focus-border-properties);
    background-color: var(--eds-text-field-focus-background-color);
    box-shadow: var(--eds-text-field-focus-box-shadow);
  }

  [part="base"].subtle {
    border: var(--eds-text-field-subtle-border-properties);
    background-color: var(--eds-text-field-subtle-background-color);
  }

  [part="base"].subtle:hover {
    border: var(--eds-text-field-subtle-hover-border-properties);
    background-color: var(--eds-text-field-subtle-hover-background-color);
  }

  [part="base"].subtle:focus-within {
    border: var(--eds-text-field-subtle-focus-border-properties);
    background-color: var(--eds-text-field-subtle-focus-background-color);
    box-shadow: var(--eds-text-field-subtle-focus-box-shadow);
  }

  [part="base"].disabled {
    cursor: not-allowed;
    background-color: var(--eds-text-field-disabled-background-color);
    border: var(--eds-text-field-disabled-border-properties);
  }

  [part="base"].disabled:hover {
    background-color: var(--eds-text-field-disabled-background-color);
    border: var(--eds-text-field-disabled-border-properties);
  }

  [part="base"].invalid {
    border: var(--eds-text-field-invalid-border-properties);
  }

  [part="input"] {
    background-color: transparent;
    border: 0;
    min-width: 0;
    width: 100%;
    outline: none;
    cursor: inherit;
    color: var(--eds-text-field-color);
  }

  [part="input"],
  [part="input"]::placeholder {
    font-weight: var(--eds-text-field-font-weight);
    line-height: var(--eds-text-field-line-height);
    font-size: var(--eds-text-field-font-size);
  }

  [part="input"]::placeholder {
    color: var(--eds-text-field-placeholder-color);
  }

  [part="base"].disabled [part="input"],
  [part="base"].disabled [part="input"]::placeholder,
  [part="base"].disabled [part="icon-leading"],
  [part="base"].disabled [part="icon-trailing"] {
    color: var(--eds-text-field-disabled-color);
  }
  
  [part="icon-leading"] {
    color: var(--eds-text-field-icon-leading-color);
    width: var(--eds-text-field-icon-leading-size);
    height: var(--eds-text-field-icon-leading-size);
    flex-shrink: 0;
  }

  [part="icon-trailing"],
  [part="icon-password"] {
    color: var(--eds-text-field-icon-trailing-color);
    width: var(--eds-text-field-icon-trailing-size);
    height: var(--eds-text-field-icon-trailing-size);
    flex-shrink: 0;
  }

  [part="icon-password"] {
    cursor: pointer;
  }
`;