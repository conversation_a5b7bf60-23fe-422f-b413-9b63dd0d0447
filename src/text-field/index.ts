import { LitElement, html, type TemplateResult, type CSSResultGroup } from 'lit';
import { property, query } from 'lit/decorators.js';
import { classMap } from 'lit/directives/class-map.js';
import { ifDefined } from 'lit-html/directives/if-defined.js';
import { styles } from '../styles';
import { textFieldStyle } from './text-field.style';
import { TextFieldAppearanceValues, TextFieldTypeValues } from './types';
import '../icons';
import { type Icon } from '../icons';

class TextField extends LitElement {
  static styles: CSSResultGroup = [styles, textFieldStyle];

  @property({ type: String }) id = crypto.randomUUID() as string;
  @property({ type: String }) appearance: TextFieldAppearanceValues = 'default';
  @property({ type: String }) type: TextFieldTypeValues = 'text';
  @property({ type: String }) value = '';
  @property({ type: String }) placeholder = '';
  @property({ type: String }) iconLeading = '';
  @property({ type: String }) iconTrailing = '';
  @property({ type: Number }) maxlength = '';
  @property({ type: Number }) minlength = '';
  @property({ type: Boolean }) showValue = false;
  @property({ type: Boolean }) isDisabled = false;
  @property({ type: Boolean }) isInvalid = false;
  @property({ type: Boolean }) isRequired = false;
  @property({ type: Boolean }) isCompact = false;
  @property({ type: Boolean }) isReadonly = false;

  @query('[part="base"]') inputWrapperEl!: HTMLDivElement;
  @query('[part="input"]') inputEl!: HTMLInputElement;
  @query('[part="icon-password"]') iconPasswordEl!: Icon;

  protected override firstUpdated() {
    if (this.type === 'password') {
      this.inputEl.type = this.showValue ? 'text' : 'password';
    }
  }

  private _handleClick = (): void => {
    this.inputEl.focus();
  };

  handleInputEvent(event: Event) {
    this.value = this.inputEl.value;
  }

  private _togglePassword = (): void => {
    if (this.showValue) {
      this.inputEl.type = 'password';
      this.iconPasswordEl.name = 'view';
    } else {
      this.inputEl.type = 'text';
      this.iconPasswordEl.name = 'viewOff';
    }
    this.showValue = !this.showValue;
  };

  protected render = (): TemplateResult => {
    return html`
      <div
        part="base"
        @click="${this._handleClick}"
        class="${classMap({
          disabled: this.isDisabled,
          compact: this.isCompact,
          invalid: this.isInvalid,
          [this.appearance]: true,
        })}"
      >
        ${this.iconLeading ? html`<eds-icon part="icon-leading" name="${this.iconLeading}"></eds-icon>` : ''}
        <input
          part="input"
          type="${this.type}"
          id="${this.id}"
          value="${this.value}"
          placeholder="${this.placeholder}"
          ?disabled="${this.isDisabled}"
          ?required="${this.isRequired}"
          ?readonly="${this.isReadonly}"
          maxlength="${ifDefined(this.maxlength || undefined)}"
          minlength="${ifDefined(this.minlength || undefined)}"
          aria-invalid="${this.isInvalid}"
          @input="${this.handleInputEvent}"
          @change="${this.handleInputEvent}"
          @focus="${this.handleInputEvent}"
          @blur="${this.handleInputEvent}"
        />
        ${this.type === 'password'
          ? html`<eds-icon
              @click="${this._togglePassword}"
              role="button"
              part="icon-password"
              name="${this.showValue ? 'viewOff' : 'view'}"
            ></eds-icon>`
          : ''}
        ${this.iconTrailing ? html`<eds-icon part="icon-trailing" name="${this.iconTrailing}"></eds-icon>` : ''}
      </div>
    `;
  };
}

if (!customElements.get('eds-text-field')) {
  customElements.define('eds-text-field', TextField);
}

declare global {
  interface HTMLElementTagNameMap {
    'eds-text-field': TextField;
  }
}

export { TextField };
