import { LitElement, html, type TemplateResult, type CSSResultGroup, PropertyValues } from 'lit';
import { customElement, property } from 'lit/decorators.js';
import { styles } from '../styles';
import AirDatepicker, { type AirDatepickerOptions } from 'air-datepicker';
import { locales } from './locales';
import { datePickerStyle } from './date-picker.style';

@customElement('eds-date-picker')
class DatePicker extends LitElement {
  static styles: CSSResultGroup = [styles, datePickerStyle];

  @property({ type: String }) id = crypto.randomUUID() as string;
  @property({ type: String }) value = '';
  @property({ type: String }) placeholder = '';
  @property({ type: Boolean }) isDisabled = false;
  @property({ type: Boolean }) isInvalid = false;
  @property({ type: Boolean }) isRequired = false;
  @property({ type: Object }) options: AirDatepickerOptions<HTMLElement> = {};
  @property({ type: String }) iconTrailing = '';
  private datepicker: AirDatepicker<HTMLElement> | null = null;

  protected render(): TemplateResult {
    return html`
      <div part="date-picker" class="date-picker-container" style="position: relative;">
        <eds-text-field
          id="${this.id}"
          value="${this.value}"
          placeholder="${this.placeholder}"
          iconTrailing="${this.iconTrailing}"
          ?isDisabled="${this.isDisabled}"
          ?isInvalid="${this.isInvalid}"
          ?isRequired="${this.isRequired}"
          @input="${this.handleInputEvent}"
          @change="${this.handleInputEvent}"
          @focus="${this.handleFocusEvent}"
          @blur="${this.handleBlurEvent}"
        ></eds-text-field>
      </div>
    `;
  }

  private getPrimaryDateFormat(): string {
    const originalDateFormat = this.datepickerOptions.dateFormat;
    if (typeof originalDateFormat === 'string') {
      return originalDateFormat;
    }
    if (
      Array.isArray(originalDateFormat) &&
      originalDateFormat.length > 0 &&
      typeof originalDateFormat[0] === 'string'
    ) {
      return originalDateFormat[0];
    }
    return 'dd.MM.yyyy';
  }

  private _parseAndValidateDate(dateString: string): Date | undefined {
    if (dateString.trim() === '') {
      return undefined;
    }

    const dateFormat = this.getPrimaryDateFormat();

    const dpOptionsForTemp: AirDatepickerOptions<HTMLInputElement> = {
      ...this.datepickerOptions,
      locale: this.datepickerOptions.locale ?? locales['en'],
      dateFormat: dateFormat,
      container: undefined,
      onSelect: undefined,
    };

    const tempInput = document.createElement('input');
    const tempDp = new AirDatepicker(tempInput, dpOptionsForTemp);

    tempDp.selectDate(dateString, { silent: true });

    let validDate: Date | undefined = undefined;

    if (tempDp.selectedDates.length > 0) {
      const parsedDate = tempDp.selectedDates[0];

      let dateIsValidAndInRange = true;
      const minDateOption = dpOptionsForTemp.minDate;
      const maxDateOption = dpOptionsForTemp.maxDate;

      if (minDateOption && parsedDate < new Date(minDateOption as Date | string | number)) {
        dateIsValidAndInRange = false;
      }
      if (maxDateOption && parsedDate > new Date(maxDateOption as Date | string | number)) {
        dateIsValidAndInRange = false;
      }

      if (dateIsValidAndInRange) {
        const formattedDate = tempDp.formatDate(parsedDate, dateFormat) as string;
        if (dateString.trim().toLowerCase() === formattedDate.trim().toLowerCase()) {
          validDate = parsedDate;
        }
      }
    }

    tempDp.destroy();
    return validDate;
  }

  private handleInputEvent = (e: Event) => {
    const inputElement = e.target as HTMLInputElement;
    let newInputValue = inputElement.value;
    const activeDateFormat = this.getPrimaryDateFormat();

    if (this.value.length === activeDateFormat.length && newInputValue.length > this.value.length) {
      inputElement.value = this.value;
      return;
    }

    if (newInputValue.length > activeDateFormat.length) {
      newInputValue = newInputValue.substring(0, activeDateFormat.length);
      inputElement.value = newInputValue;
    }

    this.value = newInputValue;

    if (!this.datepicker) {
      this.dispatchEvent(new CustomEvent(e.type, { detail: { date: undefined, formattedDate: this.value } }));
      return;
    }

    const newDateValue = this._parseAndValidateDate(this.value);

    if (newDateValue) {
      this.datepicker.selectDate(newDateValue, { silent: true, updateInput: false } as any);
      this.datepicker.setViewDate(newDateValue);
    } else {
      this.datepicker.selectDate([], { silent: true, updateInput: false } as any);
    }

    this.dispatchEvent(new CustomEvent(e.type, { detail: { date: newDateValue, formattedDate: this.value } }));
  };

  private handleFocusEvent = (e: Event) => {
    this.dispatchEvent(new CustomEvent('focus', { detail: e }));
  };

  private handleBlurEvent = (e: Event) => {
    this.dispatchEvent(new CustomEvent('blur', { detail: e }));
  };

  get datepickerOptions(): AirDatepickerOptions<HTMLElement> {
    const locale = this.options.locale ?? locales['en'];

    return {
      ...this.options,
      locale: (locale as any).default ?? locale,
      container: this.shadowRoot?.querySelector('.date-picker-container') as HTMLDivElement,
      onSelect: ({ date, formattedDate }) => this.handleDateSelect(date as Date, formattedDate as string),
    };
  }

  private handleDateSelect(selectedDate: Date | undefined, formattedDate: string | undefined): void {
    if (selectedDate && formattedDate) {
      this.value = formattedDate;
      this.dispatchEvent(new CustomEvent('change', { detail: { date: selectedDate, formattedDate: this.value } }));
    }
  }

  private _createDatepicker(): void {
    if (this.datepicker) {
      this.datepicker.destroy();
      this.datepicker = null;
    }

    setTimeout(() => {
      const inputElement = this.shadowRoot
        ?.getElementById(this.id)
        ?.shadowRoot?.querySelector('input') as HTMLInputElement;
      if (inputElement) {
        this.datepicker = new AirDatepicker(inputElement, this.datepickerOptions);

        if (this.value) {
          const initialDate = this._parseAndValidateDate(this.value);
          if (initialDate && this.datepicker) {
            this.datepicker.setViewDate(initialDate);
            this.datepicker.selectDate(initialDate, { silent: true, updateInput: false } as any);
          }
        }
      }
    });
  }

  updated(changedProperties: PropertyValues) {
    if (changedProperties.has('options')) {
      this._createDatepicker();
    }
  }

  firstUpdated(): void {
    this._createDatepicker();
  }

  disconnectedCallback(): void {
    super.disconnectedCallback();
    if (this.datepicker) {
      this.datepicker.destroy();
    }
  }
}

declare global {
  interface HTMLElementTagNameMap {
    'eds-date-picker': DatePicker;
  }
}

export { DatePicker };
