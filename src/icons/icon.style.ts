import { css, type CSSResultGroup } from 'lit';

export const iconStyle: CSSResultGroup =
  css`
    :host {
      --eds-loading-icon-animation: var(--loading-icon-animation, var(--eds-animation-name-spin) var(--eds-animation-duration-base) var(--eds-animation-timing-function-base) var(--eds-animation-direction-infinite));
    
      flex-shrink: 0;
    }

    svg {
      width: 100%;
      height: 100%;
      display: flex;
      align-items: center;
      justify-content: center;
    }

    .icon.loading svg {
      animation: var(--eds-loading-icon-animation);
    }
  `;