import { LitElement, html, type TemplateResult, type CSSResultGroup } from 'lit';
import { property } from 'lit/decorators.js';
import { classMap } from 'lit/directives/class-map.js';
import { styles } from '../styles';
import { iconStyle } from './icon.style';
import { Icons } from './icons';

class Icon extends LitElement {
  static styles: CSSResultGroup = [styles, iconStyle];

  @property({ type: String }) name = 'mail';

  protected render = (): TemplateResult => {
    return html`
      <span part="base"
            .name=${this.name}
            class=${classMap({
              'icon': true,
              'loading': this.name === 'loading'
            })}>
        ${Icons[this.name as keyof typeof Icons]}
      </span>
    `;
  }
}

if (!customElements.get('eds-icon')) {
  customElements.define('eds-icon', Icon);
}

declare global {
  interface HTMLElementTagNameMap {
    "eds-icon": Icon;
  }
}

export { Icon, Icons };