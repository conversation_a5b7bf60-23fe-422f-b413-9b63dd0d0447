import { LitElement, html, type TemplateResult, type CSSResultGroup } from 'lit';
import { property } from 'lit/decorators.js';
import { styles } from '../styles';
import { alertStyle } from './alert.style';
import { AlertAppearanceValues, AlertAction } from './types';
import '../button';
import '../heading';
import '../text';

class Alert extends LitElement {
  static styles: CSSResultGroup = [styles, alertStyle];

  @property({ type: String }) id = '';
  @property({ type: String }) appearance: AlertAppearanceValues = 'info';
  @property({ type: String }) title = '';
  @property({ type: String }) description = '';
  @property({ type: Boolean }) showIcon = false;
  @property({ type: String }) iconName = '';
  @property({ type: Boolean }) dismissible = false;
  @property({ type: Array }) actions: AlertAction[] = [];
  @property({ type: String }) descriptionWeight: 'regular' | 'medium' = 'regular';

  private _renderActions = (): TemplateResult => {
    return html`
      <div part="actions">
        ${this.actions.map(
          (item: AlertAction): TemplateResult => html`
            <eds-button
              part="action-item"
              appearance="link"
              ?href="${item.href}"
              target="${item.target}"
              ?isDisabled="${item.isDisabled}"
              @button-click="${item.onClick}"
            >
              ${item.content}
            </eds-button>
          `,
        )}
      </div>
    `;
  };

  protected render = (): TemplateResult => {
    return html`
      <div part="base" role="alert" class="${this.appearance}" id="${this.id}">
        <div part="wrapper">
          <div part="head">
            <div part="head-content">
              ${this.showIcon ? html`<eds-icon part="icon" name="${this.iconName}"></eds-icon>` : ''}
              ${this.title?.length > 0
                ? html`<eds-heading as="h3" size="sm" part="title" text="${this.title}"></eds-heading>`
                : ''}
            </div>
            ${this.dismissible
              ? html`<eds-icon part="icon" class="close" name="cancel" @click="${this.close}"></eds-icon>`
              : ''}
          </div>
          <div part="content">
            ${this.description?.length > 0
              ? html`<eds-text
                  as="p"
                  size="md"
                  weight="${this.descriptionWeight}"
                  part="description"
                  text="${this.description}"
                ></eds-text>`
              : ''}
            ${this.actions?.length > 0 ? this._renderActions() : ''}
          </div>
        </div>
      </div>
    `;
  };

  close(): void {
      this.dispatchEvent(new CustomEvent('alert-dismiss'));
  }
}

if (!customElements.get('eds-alert')) {
  customElements.define('eds-alert', Alert);
}

declare global {
  interface HTMLElementTagNameMap {
    'eds-alert': Alert;
  }
}

export { Alert };
