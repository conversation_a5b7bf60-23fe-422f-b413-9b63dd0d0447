import { css, type CSSResultGroup } from 'lit';

export const alertStyle: CSSResultGroup = css`
  :host {
    --eds-alert-box-align-items: var(--alert-box-align-items, flex-start);
    --eds-alert-box-justify-content: var(--alert-box-justify-content, space-between);
    --eds-alert-box-background-color: var(--alert-box-background-color, var(--eds-colors-info-lighter));
    --eds-alert-box-padding: var(--alert-box-padding, var(--eds-spacing-200));
    --eds-alert-box-border-properties: var(--alert-box-border, none);
    --eds-alert-box-border-radius: var(--alert-box-border-radius, var(--eds-radius-200));
    --eds-alert-box-width: var(--alert-box-width, 100%);

    --eds-alert-box-content-align-items: var(--alert-box-content-align-items, flex-start);
    --eds-alert-box-content-gap: var(--alert-box-content-gap, var(--eds-spacing-200));
    --eds-alert-box-content-icon-size: var(--alert-box-content-icon-size, var(--eds-sizing-700));

    --eds-alert-box-icon-color: var(--alert-box-icon-color, var(--eds-colors-info-default));
    --eds-alert-box-title-color: var(--alert-box-title-color, var(--eds-colors-text-dark));
    --eds-alert-box-description-color: var(--alert-box-description-color, var(--eds-colors-text-dark));

    --eds-alert-box-actions-gap: var(--alert-box-actions-gap, var(--eds-spacing-300));
  }

  [part='base'] {
    display: flex;
    align-items: var(--eds-alert-box-align-items);
    justify-content: var(--eds-alert-box-justify-content);
    gap: var(--eds-alert-box-content-gap);
    width: var(--eds-alert-box-width);
    padding: var(--eds-alert-box-padding);
    border: var(--eds-alert-box-border-properties);
    border-radius: var(--eds-alert-box-border-radius);
    background-color: var(--eds-alert-box-background-color);
  }

  [part='wrapper'] {
    display: flex;
    flex-direction: column;
    align-items: var(--eds-alert-box-content-align-items);
    gap: var(--eds-alert-box-content-gap);
  }

  [part='icon'] {
    width: var(--eds-alert-box-content-icon-size);
    height: var(--eds-alert-box-content-icon-size);
    color: var(--eds-alert-box-icon-color);
  }

  [part='content'] {
    display: flex;
    flex-direction: column;
    gap: var(--eds-alert-box-content-gap);
  }

  [part='title'] {
    color: var(--eds-alert-box-title-color);
  }

  [part='head'] {
    display: flex;
    align-items: center;
    justify-content: space-between;
    gap: var(--eds-alert-box-content-gap);
    width: 100%;
  }

  [part='head-content'] {
    display: flex;
    align-items: center;
    gap: var(--eds-alert-box-content-gap);
  }

  [part='description'] {
    color: var(--eds-alert-box-description-color);
  }

  [part='actions'] {
    display: flex;
    column-gap: var(--eds-alert-box-actions-gap);
  }

  [part='action-item']::part(base) {
    padding: 0;
  }

  eds-icon {
    flex-shrink: 0;
  }

  [part='base'].info {
    --eds-alert-box-background-color: var(--alert-box-background-color, var(--eds-colors-info-lighter));
    --eds-alert-box-title-color: var(--alert-box-text-color, var(--eds-colors-info-dark));
    --eds-alert-box-icon-color: var(--alert-box-icon-color, var(--eds-colors-info-default));
    --eds-alert-box-border-properties: var(
      --alert-box-border,
      var(--eds-stroke-025) var(--eds-border-style-base) var(--eds-colors-info-light)
    );
  }

  [part='base'].success {
    --eds-alert-box-background-color: var(--alert-box-background-color, var(--eds-colors-success-lighter));
    --eds-alert-box-title-color: var(--alert-box-text-color, var(--eds-colors-success-dark));
    --eds-alert-box-icon-color: var(--alert-box-icon-color, var(--eds-colors-success-default));
    --eds-alert-box-border-properties: var(
      --alert-box-border,
      var(--eds-stroke-025) var(--eds-border-style-base) var(--eds-colors-success-light)
    );
  }

  [part='base'].warning {
    --eds-alert-box-background-color: var(--alert-box-background-color, var(--eds-colors-warning-lighter));
    --eds-alert-box-title-color: var(--alert-box-text-color, var(--eds-colors-warning-dark));
    --eds-alert-box-icon-color: var(--alert-box-icon-color, var(--eds-colors-warning-default));
    --eds-alert-box-border-properties: var(
      --alert-box-border,
      var(--eds-stroke-025) var(--eds-border-style-base) var(--eds-colors-warning-light)
    );
  }

  [part='base'].error {
    --eds-alert-box-background-color: var(--alert-box-background-color, var(--eds-colors-danger-lighter));
    --eds-alert-box-title-color: var(--alert-box-text-color, var(--eds-colors-danger-dark));
    --eds-alert-box-icon-color: var(--alert-box-icon-color, var(--eds-colors-danger-default));
    --eds-alert-box-border-properties: var(
      --alert-box-border,
      var(--eds-stroke-025) var(--eds-border-style-base) var(--eds-colors-danger-light)
    );
  }

  .close {
    width: var(--eds-sizing-400);
    height: var(--eds-sizing-400);
    cursor: pointer;
  }
`;
