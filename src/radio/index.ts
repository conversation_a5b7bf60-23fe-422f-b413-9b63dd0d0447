import {LitElement, html, type TemplateResult, type CSSResultGroup, PropertyValues} from 'lit';
import { property, query } from 'lit/decorators.js';
import { styles } from '../styles';
import { radioStyles } from './radio.style';

class Radio extends LitElement {
  static styles: CSSResultGroup = [styles, radioStyles];

  @property({ type: String }) id = 'label1';
  @property({ type: String }) name = 'label1';
  @property({ type: String }) value = 'label1';
  @property({ type: String }) label = 'Label';
  @property({ type: Boolean, reflect: true }) isChecked = false;
  @property({ type: Boolean, reflect: true }) isDisabled = false;
  @property({ type: Boolean, reflect: true }) isInvalid = false;
  @property({ type: Boolean, reflect: true }) isRequired = false;
  
  @query('slot') slotElement!: HTMLSlotElement;
  @query('input') inputElement!: HTMLInputElement;

  private _internalChange = false;

  private _handleChange = () => {
    this.isChecked = this.inputElement.checked;
    
    this.dispatchEvent(new CustomEvent('radio-change', {
      bubbles: true,
      composed: true,
      detail: {
        target: this
      },
    }));
  }
  
  updated(changedProperties: PropertyValues) {
    if (changedProperties.has('isChecked') && !this._internalChange) {
      if (this.inputElement) {
        this.inputElement.checked = this.isChecked;
      }
    }
  }

  protected firstUpdated() {
    this._addLabelListeners();
    if (this.isChecked) {
      this._handleChange();
    }
  }

  private _addLabelListeners = () => {
    const assignedNodes = this.slotElement?.assignedNodes({ flatten: true }) || [];
    assignedNodes.forEach((node) => {
      if (node instanceof HTMLLabelElement) {
        node.addEventListener('click', this._handleLabelClick);
      }
    });
  };
  
  private _handleLabelClick = (event: MouseEvent) => {
    const label = event.currentTarget as HTMLLabelElement;
    const forId = label.getAttribute('for');
    if (forId) {
      const input = this.shadowRoot?.querySelector(`#${forId}`) as HTMLInputElement;
      if (input) {
        input.click();
      }
    }
  };
  
  private _renderCheckbox = (): TemplateResult => {
    return html`
      <div part="radio">
        <input type="radio"
         part="radio-item"
         id="${this.id}"
         name="${this.name}"
         value="${this.value}"
         ?checked=${this.isChecked}
         ?disabled="${this.isDisabled}"
         ?required="${this.isRequired}"
         ?aria-invalid="${this.isInvalid}"
         @change="${this._handleChange}">
        <div part="checkmark"></div>
      </div>
    `;
  }

  protected render = (): TemplateResult => {
    return html`
      <div part="base">
        ${this._renderCheckbox()}
        <div part="content">
            <slot></slot>
            ${this.isRequired ? html`<span part="required">*</span>` : ''}
        </div>
      </div>`
  }
}

if (!customElements.get('eds-radio')) {
    customElements.define('eds-radio', Radio);
}

declare global {
  interface HTMLElementTagNameMap {
    "eds-radio": Radio;
  }
}

export {Radio};