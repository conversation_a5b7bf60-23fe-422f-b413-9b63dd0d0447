import { css } from 'lit';

export const radioStyles = css`
  :host {
    --eds-radio-gap: var(--radio-gap, var(--eds-spacing-200));
    --eds-radio-wrapper-font-size: var(--radio-wrapper-font-size, var(--eds-font-size-body-md));
    --eds-radio-wrapper-line-height: var(--radio-wrapper-line-height, var(--eds-line-height-body-md));
    --eds-radio-height: var(--radio-height, var(--eds-sizing-400));
    --eds-radio-width: var(--radio-width, var(--eds-sizing-400));
    --eds-radio-padding: var(--radio-padding, var(--eds-spacing-050));
    --eds-radio-text-color: var(--radio-text-color, var(--eds-colors-primary-default));
    --eds-radio-checkmark-border-properties: var(--radio-checkmark-border-properties, var(--eds-stroke-025) var(--eds-border-style-base) var(--eds-border-color-default));
    --eds-radio-checkmark-border-radius: var(--radio-checkmark-border-radius, var(--eds-radius-full));

    --eds-radio-checkmark-focus-border-properties: var(--radio-checkmark-focus-border-properties, var(--eds-stroke-025) var(--eds-border-style-base) var(--eds-border-color-dark));
    
    --eds-radio-checkmark-hover-background-color: var(--radio-checkmark-hover-background-color, var(--eds-colors-primary-lighter));
    --eds-radio-checkmark-hover-border-properties: var(--radio-checkmark-hover-border-properties, var(--eds-stroke-025) var(--eds-border-style-base) var(--eds-border-color-dark));

    --eds-radio-checkmark-checked-width: var(--radio-checkmark-checked-width, var(--eds-sizing-200));
    --eds-radio-checkmark-checked-height: var(--radio-checkmark-checked-height, var(--eds-sizing-200));
    --eds-radio-checkmark-checked-top: var(--radio-checkmark-checked-height, 50%);
    --eds-radio-checkmark-checked-left: var(--radio-checkmark-checked-height, 50%);
    --eds-radio-checkmark-checked-transform: var(--radio-checkmark-checked-height, translate(-50%, -50%));
    --eds-radio-checkmark-checked-background-color: var(--radio-checkmark-checked-background-color, var(--eds-colors-primary-default));
    --eds-radio-checkmark-checked-border-properties: var(--radio-checkmark-checked-border-properties, var(--eds-stroke-025) var(--eds-border-style-base) var(--eds-colors-primary-default));
    
    --eds-radio-checkmark-disabled-background-color: var(--radio-checkmark-disabled-background-color, var(--eds-colors-primary-light));
    --eds-radio-checkmark-disabled-checked-background-color: var(--radio-checkmark-disabled-background-color, var(--eds-colors-surface-default));
    --eds-radio-checkmark-disabled-checked-after-background-color: var(--radio-checkmark-disabled-background-color, var(--eds-colors-primary-light));
    --eds-radio-checkmark-disabled-border-properties: var(--radio-checkmark-disabled-border-properties, var(--eds-stroke-025) var(--eds-border-style-base) var(--eds-border-color-light));
    --eds-radio-checkmark-disabled-text-color: var(--radio-checkmark-disabled-text-color, var(--eds-colors-text-disabled));
    
    --eds-radio-checkmark-invalid-checked-background-color: var(--radio-checkmark-invalid-background-color, var(--eds-colors-surface-default));
    --eds-radio-checkmark-invalid-checked-after-background-color: var(--radio-checkmark-invalid-background-color, var(--eds-colors-danger-default));
    --eds-radio-checkmark-invalid-border-properties: var(--radio-checkmark-invalid-border-properties, var(--eds-stroke-025) var(--eds-border-style-base) var(--eds-colors-danger-default));
    
    --eds-radio-checkmark-required-text-color: var(--radio-checkmark-required-text-color, var(--eds-colors-danger-default));
  }
  
  [part=base] {
    display: flex;
    gap: var(--eds-radio-gap);
    position: relative;
    font-size: var(--eds-radio-wrapper-font-size);
    line-height: var(--eds-radio-wrapper-line-height);
  }
  
  [part=radio] {
    display: grid;
    place-items: center;
    position: relative;
    cursor: pointer;
    height: var(--eds-radio-height);
    width: var(--eds-radio-width);
    padding: var(--eds-radio-padding);
    box-sizing: content-box;
  }
  
  [part=radio-item] {
    width: 100%;
    height: 100%;
    margin: 0;
    opacity: 0;
    z-index: 1;
    grid-area: 1 / 1 / 2 / 2;
    cursor: pointer;
  }
  
  [part=checkmark] {
    height: var(--eds-radio-height);
    width: var(--eds-radio-width);
    border: var(--eds-radio-checkmark-border-properties);
    border-radius: var(--eds-radio-checkmark-border-radius);
    grid-area: 1 / 1 / 2 / 2;
  }

  [part=content] {
      display: flex;
  }
  
  [part=base],
  ::slotted(*) {
    color: var(--eds-radio-text-color);
    cursor: pointer;
  }

  [part=base]:focus [part=radio-item]:not(:checked) ~ [part=checkmark] {
    border: var(--eds-radio-checkmark-focus-border-properties);
  }
  
  [part=base]:hover [part=radio-item] ~ [part=checkmark] {
    background-color: var(--eds-radio-checkmark-hover-background-color);
    border: var(--eds-radio-checkmark-hover-border-properties);
  }

  [part=base] [part=radio-item]:checked ~ [part=checkmark] {
    border: var(--eds-radio-checkmark-checked-border-properties);
  }

  [part=base] [part=radio-item] ~ [part=checkmark]:after {
    content: "";
    display: block;
    border-radius: var(--eds-radio-checkmark-border-radius);
    width: var(--eds-radio-checkmark-checked-width);
    height: var(--eds-radio-checkmark-checked-height);
    position: relative;
    top: var(--eds-radio-checkmark-checked-top);
    left: var(--eds-radio-checkmark-checked-left);
    transform: var(--eds-radio-checkmark-checked-transform);
  }
  
  [part=base] [part=radio-item]:checked ~ [part=checkmark]:after {
    background-color: var(--eds-radio-checkmark-checked-background-color);
  }

  [part=base] [part=radio-item][disabled] ~ [part=checkmark] {
    border: var(--eds-radio-checkmark-disabled-border-properties);
    background-color: var(--eds-radio-checkmark-disabled-background-color);
  }

  [part=base] [part=radio-item][disabled]:checked ~ [part=checkmark] {
    border: var(--eds-radio-checkmark-disabled-border-properties);
    background-color: var(--eds-radio-checkmark-disabled-checked-background-color);
  }

  [part=base] [part=radio-item][disabled]:checked ~ [part=checkmark]:after {
    background-color: var(--eds-radio-checkmark-disabled-checked-after-background-color);
  }
  
  [part=base]:has(input[disabled]),
  [part=base]:has(input[disabled]) ::slotted(*) {
    color: var(--eds-radio-checkmark-disabled-text-color);
  }

  [part=base] [part=radio-item][aria-invalid]:checked ~ [part=checkmark] {
    border: var(--eds-radio-checkmark-invalid-border-properties);
    background-color: var(--eds-radio-checkmark-invalid-checked-background-color);
  }

  [part=base] [part=radio-item][aria-invalid]:checked ~ [part=checkmark]:after {
    background-color: var(--eds-radio-checkmark-invalid-checked-after-background-color);
  }
  
  [part=base] [part=required] {
      color: var(--eds-radio-checkmark-required-text-color);
  }
`;
