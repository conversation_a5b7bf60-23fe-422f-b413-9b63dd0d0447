import { css } from "lit";

export const formFieldStyle = css`
  :host {
    --eds-form-field-gap: var(--form-field-gap, var(--eds-spacing-100));
    --eds-form-field-label-color: var(--form-field-label-color, var(--eds-colors-text-dark));
    --eds-form-field-label-font-size: var(--form-field-label-font-size, var(--eds-font-size-body-sm));
    --eds-form-field-label-font-weight: var(--form-field-label-font-weight, var(--eds-font-weight-medium));
    --eds-form-field-label-line-height: var(--form-field-label-line-height, var(--eds-font-line-height-body-sm));
    --eds-form-field-helper-text-color: var(--form-field-helper-text-color, var(--eds-colors-text-default));
    --eds-form-field-error-message-color: var(--form-field-error-message-color, var(--eds-colors-danger-default));
    --eds-form-field-success-message-color: var(--form-field-success-message-color, var(--eds-colors-success-default));
    --eds-form-field-message-align-items: var(--form-field-message-align-items, flex-start);
    --eds-form-field-message-gap: var(--form-field-message-gap, var(--eds-spacing-100));
    --eds-form-field-message-icon-size: var(--form-field-message-icon-size, var(--eds-sizing-400));
  }
  
  [part="base"] {
    display: flex;
    flex-direction: column;
    gap: var(--eds-form-field-gap);
  }
  
  [part="label"] {
    display: flex;
    font-size: var(--eds-form-field-label-font-size);
    font-weight: var(--eds-form-field-label-font-weight);
    line-height: var(--eds-form-field-label-line-height);
    color: var(--eds-form-field-label-color);
  }

  [part="required"] {
    color: var(--eds-colors-danger-default);
  }

  [part="helper-text"] {
    --eds-text-color: var(--eds-form-field-helper-text-color);
  }
  
  [part="error"],
  [part="success"] {
    display: flex;
    align-items: var(--eds-form-field-message-align-items);
    gap: var(--eds-form-field-message-gap);
  }

  [part="error-message"] {
    --eds-text-color: var(--eds-form-field-error-message-color);
  }

  [part="success-message"] {
    --eds-text-color: var(--eds-form-field-success-message-color);
  }

  [part="error-icon"],
  [part="success-icon"] {
    width: var(--eds-form-field-message-icon-size);
    height: var(--eds-form-field-message-icon-size);
    flex-shrink: 0;
  }

  [part="error-icon"] {
    color: var(--eds-form-field-error-message-color);
  }
  
  [part="success-icon"] {
    color: var(--eds-form-field-success-message-color);
  }
`;