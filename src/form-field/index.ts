import { LitElement, html, type TemplateResult, type CSSResultGroup } from 'lit';
import { property, queryAssignedElements, state } from 'lit/decorators.js';
import { styles } from '../styles';
import { formFieldStyle } from './form-field.style';
import '../text-field';
import '../text';
import '../icons';
import { PhoneNumber } from '../phone-number';
import { DatePicker } from '../date-picker';
import { FormFieldElement } from './types';

class FormField extends LitElement {
  static styles: CSSResultGroup = [styles, formFieldStyle];

  @property({ type: String }) label = '';
  @property({ type: String }) helperText = '';
  @property({ type: String }) errorMessage = '';
  @property({ type: String }) successMessage = '';

  @queryAssignedElements({ slot: '', flatten: true })
  private slottedEls!: Array<FormFieldElement>;

  @state() private slottedEl: FormFieldElement = this.slottedEls[0];
  @state() private slottedElId: string = '';
  @state() private isRequired: boolean = false;
  @state() private isInvalid: boolean = false;

  protected firstUpdated() {
    if (!this.slottedEls.length) return;

    [this.slottedEl] = this.slottedEls;
    this.slottedElId = this.slottedEl.id;
    this.isRequired = this.slottedEl.isRequired;
    this.isInvalid = this.slottedEl.isInvalid;
  }

  private _renderErrorMessage = (): TemplateResult => {
    return html` <div part="error">
      <eds-icon part="error-icon" name="alertCircle"></eds-icon>
      <eds-text part="error-message" as="p" text="${this.errorMessage}" size="sm" weight="regular"></eds-text>
    </div>`;
  };

  private _renderSuccessMessage = (): TemplateResult => {
    return html` <div part="success">
      <eds-icon part="success-icon" name="checkmarkCircle"></eds-icon>
      <eds-text part="success-message" as="p" text="${this.successMessage}" size="sm" weight="regular"></eds-text>
    </div>`;
  };

  private _handleLabelClick = (): void => {
    if (!this.slottedElId || !this.slottedEl.shadowRoot) return;

    const targetEl =
      this.slottedEl instanceof PhoneNumber || this.slottedEl instanceof DatePicker
        ? (this.slottedEl.shadowRoot.querySelector(`#${this.slottedElId}`)?.shadowRoot as ShadowRoot)
        : (this.slottedEl.shadowRoot as ShadowRoot);

    const input = targetEl.querySelector(`#${this.slottedElId}`) as HTMLInputElement;
    input.focus();
  };

  protected render = (): TemplateResult => {
    return html`
      <div part="base">
        ${this.label
          ? html`<label @click="${this._handleLabelClick}" for="${this.slottedElId}" part="label">
              ${this.label}
              ${this.isRequired
                ? html`<eds-text
                    aria-hidden="true"
                    part="required"
                    text="*"
                    as="p"
                    weight="regular"
                    size="sm"
                  ></eds-text>`
                : ''}
            </label>`
          : ''}
        <slot></slot>
        ${this.helperText
          ? html`<eds-text part="helper-text" as="p" text="${this.helperText}" size="sm" weight="regular"></eds-text>`
          : ''}
        ${this.errorMessage && this.isInvalid ? this._renderErrorMessage() : ''}
        ${this.successMessage && !this.isInvalid ? this._renderSuccessMessage() : ''}
      </div>
    `;
  };
}

if (!customElements.get('eds-form-field')) {
  customElements.define('eds-form-field', FormField);
}

declare global {
  interface HTMLElementTagNameMap {
    'eds-form-field': FormField;
  }
}

export { FormField };
