import { LitElement, html, type TemplateResult, type CSSResultGroup } from "lit";
import { property, queryAssignedElements } from "lit/decorators.js";
import { styles } from '../styles';;
import { segmentedControlStyle } from "./segmented-control.style";
import { type SegmentedButton } from "./segmented-button";
import { SegmentedOption } from './types';
import "./segmented-button";

class SegmentedControl extends LitElement {
  static styles: CSSResultGroup = [styles, segmentedControlStyle];

  @property({ type: Boolean }) isDisabled = false;  
  @property({ type: Array }) options: SegmentedOption[] = [];

  @queryAssignedElements({ selector: "eds-segmented-button", flatten: true })
  segmentedButtonsEl!: SegmentedButton[];

  private _renderOptions = (): TemplateResult[] => {
    return this.options.map(
      (item) => html`
        <eds-segmented-button
          part="option"
          .text="${item.text}"
          .iconName="${item.iconName}"
          .ariaControls="${item.ariaControls}"
          ?onlyIcon="${item.onlyIcon}"
          ?isDisabled="${item.isDisabled}"
          ?isSelected="${item.isSelected}"
          ?defaultSelected="${item.defaultSelected}"
        ></eds-segmented-button>
      `
    );
  };

  updated = (): void => {
    if (!this.segmentedButtonsEl.length) {
      this.remove();
      return;
    }

    const hasDefaultSelected: boolean = this.segmentedButtonsEl.some(
      (option) => option.defaultSelected
    );

    if (!hasDefaultSelected) {
      this.segmentedButtonsEl.forEach((button) => {
        button.defaultSelected = false;
        button.isSelected = false;
      });
      this.segmentedButtonsEl[0].defaultSelected = true;
      this.segmentedButtonsEl[0].isSelected = true;
    } else {
      this.segmentedButtonsEl.forEach((button) => {
        if (button.defaultSelected) {
          button.isSelected = true;
        }
      });
    }
  };

  private _handleSegmentClick = (e: CustomEvent): void => {
    this.segmentedButtonsEl.forEach((button) => {
      button.defaultSelected = false;
      button.isSelected =
        button.ariaControls === (e.detail as { target: SegmentedOption }).target.ariaControls;
    });
  };

  protected render = (): TemplateResult => {
    return html`
      <div
        part="base"
        @segment-selected="${(e: CustomEvent) => {
          this._handleSegmentClick(e);
        }}"
      >
        <slot part="options"> ${this._renderOptions()} </slot>
      </div>
    `;
  };
}

if (!customElements.get('eds-segmented-control')) {
  customElements.define('eds-segmented-control', SegmentedControl);
}

declare global {
  interface HTMLElementTagNameMap {
    "eds-segmented-control": SegmentedControl;
  }
}

export { SegmentedControl };
