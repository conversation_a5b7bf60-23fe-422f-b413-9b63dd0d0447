import { css } from 'lit';

export const segmentedControlStyle = css`
  :host {
    --eds-segmented-control-padding: var(--segmented-control-padding, var(--eds-spacing-100));
    --eds-segmented-button-padding: var(--segmented-button-padding, var(--eds-spacing-200));
    --eds-segmented-control-gap: var(--segmented-control-gap, var(--eds-spacing-200));
    --eds-segmented-control-border: var( --segmented-control-border, var(--eds-stroke-025) var(--eds-border-style-base) var(--eds-border-color-default));
    --eds-segmented-control-background: var(--segmented-control-background, var(--eds-colors-surface-default));
    --eds-segmented-control-border-radius: var(--segmented-control-border-radius, var(--eds-radius-full));
    --eds-segmented-button-selected-background-color: var( --segmented-button-selected-background-color, var(--eds-colors-primary-default));
    --eds-segmented-button-selected-text-color: var( --segmented-button-selected-text-color, var(--eds-colors-text-white));
    --eds-segmented-button-text-color: var(--segmented-button-text-color, var(--eds-colors-text-default));
    --eds-segmented-button-font-size: var(--segmented-button-font-size, var(--eds-font-size-body-md));
    --eds-segmented-button-border: var(--segmented-button-border, none);
    --eds-segmented-button-gap: var(--segmented-button-gap, var(--eds-spacing-200));
    --eds-segmented-button-box-shadow: var(--segmented-button-box-shadow, 0 2px 3px 0 hsla(0, 0%, 0%, .12));
    --eds-segmented-button-transition: var( --segmented-button-transition, var(--eds-transition-property-background) var(--eds-transition-duration-base) var(--eds-transition-timing-function-base) );
    --eds-segmented-button-icon-size: var(--segmented-button-icon-size, var(--eds-sizing-400));
    --eds-segmented-button-background-color: var(--segmented-button-background-color, var(--eds-color-transparent));
    --eds-segmented-button-disabled-text-color: var(--segmented-button-disabled-text-color, var(--eds-colors-text-disabled));
    --eds-segmented-button-disabled-background-color: var(--segmented-button-disabled-background-color, var(--eds-colors-surface-disabled));
  }

  [part="base"] {
    display: flex;
    align-items: center;
    gap: var(--eds-segmented-control-gap);
    padding: var(--eds-segmented-control-padding);
    border: var(--eds-segmented-control-border);
    border-radius: var(--eds-segmented-control-border-radius);
    background-color: var(--eds-segmented-control-background);
    width: 100%;
  }

  ::slotted(eds-segmented-button),
  eds-segmented-button,
  [part="button"] {
    flex: 1;
    color: var(--eds-segmented-button-text-color);
  }

  [isselected]::part(button) {
    background-color: var(--eds-segmented-button-selected-background-color);
    color: var(--eds-segmented-button-selected-text-color);
    box-shadow: var(--eds-segmented-button-box-shadow);
  }

  [part="text"] {
    font-size: var(--eds-segmented-button-font-size);
  }

  [part="button"] {
    display: flex;
    align-items: center;
    justify-content: center;
    padding: var(--eds-segmented-button-padding);
    border-radius: calc(var(--eds-segmented-control-border-radius) - var(--eds-segmented-control-padding));
    border: var(--eds-segmented-button-border);
    gap: var(--eds-segmented-button-gap);
    cursor: pointer;
    background-color: var(--eds-segmented-button-background-color);
    transition: var(--eds-segmented-button-transition);
    width: 100%;
  }

  [disabled] {
    pointer-events: none;
    color: var(--eds-segmented-button-disabled-text-color);
    background-color: var(--eds-segmented-button-disabled-background-color);
  }

  eds-icon,
  ::slotted(eds-icon) {
    flex-shrink: 0;
    width: var(--eds-segmented-button-icon-size);
    height: var(--eds-segmented-button-icon-size);
    color: currentColor
  }
`;