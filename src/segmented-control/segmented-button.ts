import { LitElement, html, type TemplateResult, type CSSResultGroup } from "lit";
import { property } from "lit/decorators.js";
import { styles } from '../styles';;
import { segmentedControlStyle } from "./segmented-control.style";
import "../icons";

class SegmentedButton extends LitElement {
  static styles: CSSResultGroup = [styles, segmentedControlStyle];

  @property({ type: String }) text = "";
  @property({ type: String }) iconName = "";
  @property({ type: String }) ariaControls = "";
  @property({ type: Boolean }) onlyIcon = false;
  @property({ type: Boolean }) defaultSelected = false;
  @property({ type: Boolean }) isDisabled = false;
  @property({ type: Boolean, reflect: true }) isSelected = false;

  firstUpdated = (): void => {
    if (this.defaultSelected) this.isSelected = true;
  };

  private _renderIcon = (): TemplateResult => {
    return this.iconName
      ? html`<eds-icon name=${this.iconName}></eds-icon>`
      : html`<slot part="icon" name="icon"></slot>`;
  };

  private _renderLabel = (): TemplateResult => {
    return this.onlyIcon
      ? html``
      : html`<slot name="text" part="text">${this.text}</slot>`;
  };

  protected render = (): TemplateResult => {
    return html`
      <button
        part="button"
        ?disabled=${this.isDisabled}
        aria-selected="${this.isSelected}"
        aria-controls="${this.ariaControls}"
        @click=${this._onClick}
      >
        ${this._renderIcon()} 
        ${this._renderLabel()}
      </button>
    `;
  };

  private _onClick = (): void => {
    if (!this.isDisabled) {
      this.isSelected = true;
      this.dispatchEvent(
        new CustomEvent("segment-selected", {
          bubbles: true,
          composed: true,
          detail: { target: this },
        })
      );
    }
  };
}

if (!customElements.get('eds-segmented-button')) {
  customElements.define('eds-segmented-button', SegmentedButton);
}

declare global {
  interface HTMLElementTagNameMap {
    "eds-segmented-button": SegmentedButton;
  }
}

export { SegmentedButton };
