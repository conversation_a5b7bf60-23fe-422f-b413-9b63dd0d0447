import { css } from "lit";

export const checkoutCardStyle = css`
  :host {
    --eds-checkout-card-border-color: var(--checkout-card-border-color, var(--eds-border-color-default));
    --eds-checkout-card-border-radius: var(--checkout-card-border-radius, var(--eds-radius-300));

    --eds-checkout-card-header-background-color: var(--checkout-card-header-background-color, var(--eds-colors-surface-level-2));
    --eds-checkout-card-header-padding: var(--checkout-card-header-padding, var(--eds-spacing-400) var(--eds-spacing-600));
    --eds-checkout-card-header-gap: var(--checkout-card-header-gap, var(--eds-spacing-300));

    --eds-checkout-card-title-gap: var(--checkout-card-title-gap, var(--eds-spacing-300));

    --eds-checkout-card-content-border-top: var(--checkout-card-content-border-top, var(--eds-stroke-025) var(--eds-border-style-base) var(--eds-border-color-default));
    --eds-checkout-card-content-padding: var(--checkout-card-content-padding, var(--eds-spacing-600));

    --eds-checkout-card-completed-background-color: var(--checkout-card-completed-background-color, var(--eds-colors-surface-default));

    --eds-checkout-card-inactive-border-color: var(--checkout-card-inactive-border-color, var(--eds-border-color-default));
    --eds-checkout-card-inactive-background-color: var(--checkout-card-inactive-background-color, var(--eds-colors-surface-disabled));
    --eds-checkout-card-inactive-text-color: var(--checkout-card-inactive-text-color, var(--eds-colors-text-disabled));
    --eds-checkout-card-inactive-badge-border: var(--checkout-card-inactive-badge-border, var(--eds-stroke-025) var(--eds-border-style-base) var(--eds-border-color-default));
  }

  [part="base"] {
    display: flex;
    flex-direction: column;
    border: 1px solid var(--eds-checkout-card-border-color);
    border-radius: var(--eds-checkout-card-border-radius);
    overflow: hidden;
  }

  [part="header"] {
    display: flex;
    align-items: center;
    justify-content: space-between;
    gap: var(--eds-checkout-card-header-gap);
    padding: var(--eds-checkout-card-header-padding);
    background-color: var(--eds-checkout-card-header-background-color);
  }

  [part="title"] {
    display: flex;
    align-items: center;
    gap: var(--eds-checkout-card-title-gap);
  }

  [part="content"] {
    border-top: 1px solid var(--eds-border-color-default);
    padding: var(--eds-checkout-card-content-padding);
  }

  eds-heading {
    color: var(--eds-checkout-card-title-color);
  }

  eds-icon {
    color: var(--eds-checkout-card-icon-color);
  }

  .completed [part="header"],
  .inactive [part="header"] {
    background-color: var(--eds-checkout-card-completed-background-color);
  }

  .inactive {
    border-color: var(--eds-checkout-card-inactive-border-color);
  }
`;
