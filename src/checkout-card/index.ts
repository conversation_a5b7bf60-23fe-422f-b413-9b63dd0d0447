import { LitElement, html, type CSSResultGroup } from 'lit';
import { property } from 'lit/decorators.js';
import { CheckoutCardType } from './types';
import { styles } from '../styles';
import { checkoutCardStyle } from './checkout-card.style';
import '../badge';
import '../heading';

class CheckoutCard extends LitElement {
  static styles: CSSResultGroup = [styles, checkoutCardStyle];

  @property({ type: String }) type: CheckoutCardType = "active";
  @property({ type: String }) itemAmount = "1";
  @property({ type: String }) headerIcon = "router";
  @property({ type: String }) label = "Mobile";

  render() {
    return html`
      <div part="base" class=${this.type}>
        <div part="header">
          <div part="title">
            <eds-icon name=${this.headerIcon}></eds-icon>
            <eds-heading as="h3" size="md" text=${this.label}></eds-heading>
            <eds-badge type="circle" size="medium" appearance="secondary" label=${this.itemAmount}></eds-badge>
          </div>
          ${this.type === 'completed' ? html`
            <eds-tag appearance="green" content="COMPLETED"></eds-tag>
          `: ''}
        </div>
        ${this.type === 'inactive' ? `` : html`
          <div part="content">
            <slot></slot>
          </div>`
        }
      </div>
    `;
  }
}

if (!customElements.get('eds-checkout-card')) {
  customElements.define('eds-checkout-card', CheckoutCard);
}

export { CheckoutCard };
