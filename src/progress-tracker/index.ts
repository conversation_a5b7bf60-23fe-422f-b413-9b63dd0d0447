import { LitElement, html, type TemplateResult, type CSSResultGroup } from 'lit';
import { property } from 'lit/decorators.js';
import { styles } from '../styles';
import { progressTrackerStyle } from './progress-tracker.style';
import './progress-tracker-item';
import { type ProgressTrackerItem } from './progress-tracker-item'
import { ProgressTrackerOrientationValues } from './types'

class ProgressTracker extends LitElement {
  static styles: CSSResultGroup = [styles, progressTrackerStyle];

  @property({ type: String }) orientation: ProgressTrackerOrientationValues = 'horizontal';
  @property({ type: Array }) items: ProgressTrackerItem[] = [];

  protected override render = (): TemplateResult => {
    return html`
    <div part="base" class="${this.orientation}">
      ${this.items.map((item: ProgressTrackerItem) => {
        return html`
          <eds-progress-tracker-item
            part="item"
            title=${item.title}
            subTitle=${item.subTitle}
            description=${item.description}
            status=${item.status}
            href=${item.href}
            iconName=${item.iconName}
            ?disabled=${item.isDisabled}
          ></eds-progress-tracker-item>
        `;
      })}
    </div>`;
  }
}

if (!customElements.get('eds-progress-tracker')) {
  customElements.define('eds-progress-tracker', ProgressTracker);
}

declare global {
  interface HTMLElementTagNameMap {
    "eds-progress-tracker": ProgressTracker;
  }
}

export { ProgressTracker };