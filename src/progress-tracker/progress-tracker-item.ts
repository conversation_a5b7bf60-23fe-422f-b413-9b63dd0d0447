import { LitElement, html, type TemplateResult, type CSSResultGroup } from 'lit';
import { property } from 'lit/decorators.js';
import { classMap } from 'lit/directives/class-map.js';
import { styles } from '../styles';
import { progressTrackerStyle } from './progress-tracker.style';
import '../icons';
import '../heading';
import '../text';
import '../button';
class ProgressTrackerItem extends LitElement {
  static styles: CSSResultGroup = [styles, progressTrackerStyle];

  @property({ type: String }) title = '';
  @property({ type: String }) subTitle = '';
  @property({ type: String }) description = '';
  @property({ type: String }) status = '';
  @property({ type: Boolean }) isDisabled = false;
  @property({ type: String }) href = '';
  @property({ type: String }) iconName = '';

  private get appearance(): string {
    switch (this.status) {
      case 'active':
        return 'primary';
      case 'completed':
        return 'subtle';
      default:
        return 'default';
    }
  }

  protected override render = (): TemplateResult => {
    return html`
      <eds-button part="item-base" iconLeading="${this.iconName}" appearance="${this.appearance}" size="compact" href="${this.href}" class=${classMap({
        [this.status]: true,
        'disabled': this.isDisabled
      })}>
        <div part="content">
          <eds-heading part="subTitle" as="h4" size="xs" text="${this.subTitle}"></eds-heading>
          <eds-heading part="title" as="h3" size="sm" text="${this.title}"></eds-heading>
          <eds-text part="description" as="p" size="sm" weight="regular" text="${this.description}"></eds-text>
        </div>
      </eds-button>
    `;
  }
}

if (!customElements.get('eds-progress-tracker-item')) {
  customElements.define('eds-progress-tracker-item', ProgressTrackerItem);
}

declare global {
  interface HTMLElementTagNameMap {
    "eds-progress-tracker-item": ProgressTrackerItem;
  }
}

export { ProgressTrackerItem };