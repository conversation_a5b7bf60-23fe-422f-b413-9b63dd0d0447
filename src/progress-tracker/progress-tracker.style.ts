import { css, type CSSResultGroup } from "lit";

export const progressTrackerStyle: CSSResultGroup = css`
  :host {
    --eds-progress-tracker-gap: var(--progress-tracker-gap, var(--eds-spacing-400));
    --eds-progress-tracker-item-gap: var(--progress-tracker-item-gap, var(--eds-spacing-200));
    --eds-progress-tracker-item-color-primary: var(--progress-tracker-item-color-primary, var(--eds-colors-primary-dark));
    --eds-progress-tracker-item-color-secondary: var(--progress-tracker-item-color-secondary, var(--eds-colors-secondary-default));
    --eds-progress-tracker-line-thickness: var(--progress-tracker-line-thickness, var(--eds-sizing-050));
    --eds-progress-tracker-line-color: var(--progress-tracker-line-color, var(--eds-colors-primary-dark));
    --eds-progress-tracker-line-color-secondary: var(--progress-tracker-line-color-secondary, var(--eds-colors-secondary-default));
  }

  [part="base"] {
    display: flex;
    align-items: center;
    gap: var(--eds-progress-tracker-gap);
  }

  .vertical {
    flex-direction: column;
    align-items: flex-start;
    width: fit-content;    
  }

  eds-progress-tracker-item:not(:last-child) {
    flex: 1;
  }

  [part="item-base"] {
    position: relative;
    display: flex;
    align-items: center;
    justify-content: space-between;
    gap: var(--eds-progress-tracker-item-gap);
    text-decoration: none;
  }

  .vertical eds-progress-tracker-item::part(item-base) {
    flex-direction: column;
    width: fit-content;
  }

  [part="item-base"]::after {
    content: '';
    background: var(--eds-progress-tracker-line-color);
    display: block;
    flex: 1;
    height: var(--eds-progress-tracker-line-thickness);
    width: unset;
    min-width: var(--eds-sizing-800);
    min-height: unset;
  }

  .vertical eds-progress-tracker-item::part(item-base)::after {
    width: var(--eds-progress-tracker-line-thickness);
    min-height: var(--eds-sizing-800);
    min-width: unset;
    height: unset;
  }

  eds-progress-tracker-item:last-of-type::part(item-base)::after {
    display: none;
  }

  .primary eds-progress-tracker-item::part(base) {
    color: var(--eds-progress-tracker-item-color-primary);
  }

  eds-button::part(base) {
    height: unset;
  }

  eds-heading, eds-text {
    color: currentColor;
  }
`;

