import { LitElement, html, type TemplateResult, type CSSResultGroup } from 'lit';
import { property } from 'lit/decorators.js';
import { classMap } from 'lit/directives/class-map.js';
import { styles } from '../styles';
import { accordionStyle } from './accordion.style';
import { AccordionAppearance } from './types';
import '../icons';
import '../text';


class Accordion extends LitElement {
  static styles: CSSResultGroup = [styles, accordionStyle];

  @property({ type: String }) id = '';
  @property({ type: String }) appearance: AccordionAppearance = 'default';
  @property({ type: String }) caption = '';
  @property({ type: Boolean, reflect: true }) isOpen = false;
  @property({ type: Boolean, reflect: true }) isDisabled = false;

  private static idCounter = 0;
  private readonly uniqueId: string;

  constructor() {
    super();
    this.uniqueId = `accordion-${Accordion.idCounter++}`;
  }

  get accordionContentId(): string {
    return this.id ? `${this.id}-content` : `${this.uniqueId}-content`;
  }

  private _toggleAccordion = (): void => {
    this.isOpen = !this.isOpen;

    this.dispatchEvent(new CustomEvent('accordion-click', {
      bubbles: true,
      composed: true,
      detail: {
        target: this
      },
    }))
  }

  render = (): TemplateResult => {
    return html`
      <div part="base" 
           id="${this.id}" 
           class="${classMap({
            'disabled': this.isDisabled,
            [this.appearance]: true,
      })}">
        <div
          part="header"
          @click=${this._toggleAccordion}
          aria-expanded=${this.isOpen}
          aria-controls=${this.accordionContentId}
        >
          <slot name="header">
            <eds-icon part="icon" name="arrowDown"></eds-icon>
            <eds-text part="caption" as="p" text="${this.caption}" size="lg"></eds-text>
          </slot>
        </div>
        <div
          id=${this.accordionContentId}
          part="content-wrapper"
          aria-hidden=${!this.isOpen}
        >
          <div part="content">
            <slot>Accordion Content</slot>
          </div>
        </div>
      </div>
    `;
  }
}

if (!customElements.get('eds-accordion')) {
  customElements.define('eds-accordion', Accordion);
}

declare global {
  interface HTMLElementTagNameMap {
    'eds-accordion': Accordion;
  }
}

export { Accordion };