import { css, type CSSResultGroup } from 'lit';

export const accordionStyle: CSSResultGroup = css`
  :host {
    --eds-accordion-background-color: var(--accordion-background-color, var(--eds-white));
    --eds-accordion-border-properties: var(
      --accordion-border-properties,
      var(--eds-stroke-025) var(--eds-border-style-base) var(--eds-border-color-default)
    );
    --eds-accordion-border-radius: var(--accordion-border-radius, var(--eds-radius-300));
    --eds-accordion-disabled-background-color: var(
      --accordion-disabled-background-color,
      var(--eds-colors-surface-disabled)
    );

    --eds-accordion-expanded-background-color: var(
      --accordion-expanded-background-color,
      var(--eds-colors-primary-lighter)
    );

    --eds-accordion-hover-background-color: var(--accordion-hover-background-color, var(--eds-colors-primary-lighter));

    --eds-accordion-header-align-items: var(--accordion-header-align-items, center);
    --eds-accordion-header-gap: var(--accordion-header-gap, var(--eds-spacing-200));
    --eds-accordion-header-padding: var(--accordion-header-padding, var(--eds-spacing-300) var(--eds-spacing-400));
    --eds-accordion-header-icon-size: var(--accordion-header-icon-size, var(--eds-sizing-600));
    --eds-accordion-header-icon-color: var(--accordion-header-icon-color, var(--eds-black));
    --eds-accordion-header-transition-properties: var(
      --accordion-header-transition-properties,
      var(--eds-transition-property-base) var(--eds-transition-property-base) var(--eds-transition-timing-function-base)
    );
    --eds-accordion-header-text-color: var(--accordion-header-text-color, var(--eds-black));
    --eds-accordion-header-text-decoration: var(--accordion-header-text-decoration, none);
    --eds-accordion-header-expanded-text-color: var(--accordion-header-expanded-text-color, var(--eds-black));
    --eds-accordion-header-expanded-icon-color: var(--accordion-header-expanded-icon-color, var(--eds-black));

    --eds-accordion-content-transition-properties: var(
      --accordion-content-transition-properties,
      var(--eds-transition-property-base) var(--eds-transition-duration-base) var(--eds-transition-timing-function-base)
    );
    --eds-accordion-content-padding: var(--accordion-content-padding, var(--eds-spacing-400));

    --eds-accordion-secondary-gap: var(--accordion-secondary-gap, var(--eds-spacing-200));
    --eds-accordion-secondary-border-properties: var(--accordion-secondary-border-properties, 0);
    --eds-accordion-secondary-background-color: var(--accordion-secondary-background-color, transparent);
    --eds-accordion-secondary-header-padding: var(--accordion-secondary-header-padding, unset);
    --eds-accordion-secondary-hover-background-color: var(--accordion-secondary-hover-background-color, transparent);
    --eds-accordion-secondary-expanded-background-color: var(
      --accordion-secondary-expanded-background-color,
      transparent
    );
    --eds-accordion-secondary-border-radius: var(--accordion-secondary-border-radius, unset);
    --eds-accordion-secondary-content-padding: var(--accordion-secondary-content-padding, unset);
  }

  [part='base'] {
    display: grid;
    grid-template-rows: repeat(2, minmax(0, auto));
    border: var(--eds-accordion-border-properties);
    border-radius: var(--eds-accordion-border-radius);
    overflow: hidden;
    background-color: var(--eds-accordion-background-color);
  }

  [part='base']:hover {
    background-color: var(--eds-accordion-hover-background-color);
  }

  [part='base']:has([part='header'][aria-expanded='true']) {
    background-color: var(--eds-accordion-expanded-background-color);
  }

  [part='base'].disabled {
    cursor: not-allowed;
    background-color: var(--eds-accordion-disabled-background-color);

    [part='header'],
    [part='content-wrapper'] {
      pointer-events: none;
    }
  }

  [part='header'] {
    display: flex;
    align-items: var(--eds-accordion-header-align-items);
    gap: var(--eds-accordion-header-gap);
    cursor: pointer;
    width: 100%;
    padding: var(--eds-accordion-header-padding);
    transition: var(--eds-accordion-header-transition-properties);
  }

  [part='header'] eds-text {
    --eds-text-color: var(--eds-accordion-header-text-color);
    --eds-text-decoration: var(--eds-accordion-header-text-decoration);
  }

  [part='header'][aria-expanded='true'] eds-text {
    --eds-text-color: var(--eds-accordion-header-expanded-text-color);
  }

  [part='header'] eds-icon,
  [part='header']::slotted(eds-icon) {
    width: var(--eds-accordion-header-icon-size);
    height: var(--eds-accordion-header-icon-size);
    flex-shrink: 0;
    transition: var(--eds-rotate-icon-transition-properties);
  }

  [part='header'][aria-expanded='true'] eds-icon {
    transform: var(--eds-rotate-icon-base);
    color: var(--eds-accordion-header-expanded-icon-color);
  }

  [part='content-wrapper'] {
    display: grid;
    grid-template-rows: 0fr;
    transition: grid-template-rows var(--eds-transition-duration-base) var(--eds-transition-timing-function-base);
    max-height: 100%;
  }

  [part='header'][aria-expanded='true'] + [part='content-wrapper'] {
    grid-template-rows: minmax(0, 100%);
  }

  [part='content'] {
    min-height: 0;
    overflow: hidden;
    padding: 0 var(--eds-accordion-content-padding);
    transition: all var(--eds-transition-duration-base) var(--eds-transition-timing-function-base);
  }

  [part='header'][aria-expanded='true'] + [part='content-wrapper'] [part='content'] {
    padding: var(--eds-accordion-content-padding);
  }

  [part='base'].secondary {
    display: grid;
    border: var(--eds-accordion-secondary-border-properties);
    background-color: var(--eds-accordion-secondary-background-color);
    border-radius: var(--eds-accordion-secondary-border-radius);

    [part='header'] {
      flex-direction: row-reverse;
      justify-content: space-between;
      padding: var(--eds-accordion-secondary-header-padding);
    }

    [part='content'] {
      padding: 0 var(--eds-accordion-secondary-content-padding);
    }

    [part='header'][aria-expanded='true'] + [part='content-wrapper'] [part='content'] {
      padding: var(--eds-accordion-secondary-content-padding);
    }
  }

  [part='base'].secondary:hover {
    background-color: var(--eds-accordion-secondary-hover-background-color);
  }

  [part='base'].secondary:has([part='header'][aria-expanded='true']) {
    background-color: var(--eds-accordion-secondary-expanded-background-color);
    gap: var(--eds-accordion-secondary-gap);
  }
`;
