import { LitElement, html, type TemplateResult, type CSSResultGroup } from 'lit';
import { property, query } from 'lit/decorators.js';
import { styles } from '../styles';
import { imageStyle } from './image.style';

class Image extends LitElement {
  static styles: CSSResultGroup = [styles, imageStyle];

  @property({ type: String }) src = '';
  @property({ type: String }) alt = '';
  @property({ type: String }) width = '';
  @property({ type: String }) height = '';
  @property({ type: String }) fallbackSrc = '';

  @query('img') imgElement!: HTMLImageElement;
  
  private handleError = (): void => {
    if (this.fallbackSrc) {
      this.imgElement.src = this.fallbackSrc;
    }
  }

  protected render = (): TemplateResult => {
    return html`
      <img part="image"
        src=${this.src}
        alt=${this.alt}
        width=${this.width}
        height=${this.height}
        @error=${this.handleError}
      />`;
  }
}

if (!customElements.get('eds-image')) {
  customElements.define('eds-image', Image);
}

declare global {
  interface HTMLElementTagNameMap {
    "eds-image": Image;
  }
}

export { Image };