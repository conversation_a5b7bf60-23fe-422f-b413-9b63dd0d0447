import { css, type CSSResultGroup } from "lit";

export const tagStyle: CSSResultGroup = css`
  :host {
    --eds-tag-display: var(--tag-display, flex);
    --eds-tag-align-items: var(--tag-align-items, center);
    --eds-tag-justify-content: var(--tag-justify-content, center);
    --eds-tag-gap: var(--tag-gap, var(--eds-spacing-100));
    --eds-tag-padding: var(--tag-padding, var(--eds-spacing-100) var(--eds-spacing-200));
    --eds-tag-text-color: var(--tag-text-color, var(--eds-colors-text-disabled));
    --eds-tag-border: var(--tag-border, var(--eds-stroke-025) var(--eds-border-style-base) var(--eds-border-color-default));
    --eds-tag-border-radius: var(--tag-border-radius, var(--eds-radius-200));
    --eds-tag-background-color: var(--tag-background-color, var(--eds-colors-surface-default));
    
    --eds-tag-icon-size: var(--tag-icon-size, var(--eds-sizing-400));
    --eds-tag-icon-color: var(--tag-icon-color, var(--eds-colors-base-black));
  }
  
  [part="base"] {
    display: var(--eds-tag-display);
    align-items: var(--eds-tag-align-items);
    justify-content: var(--eds-tag-justify-content);
    gap: var(--eds-tag-gap);
    padding: var(--eds-tag-padding);
    border: var(--eds-tag-border);
    border-radius: var(--eds-tag-border-radius);
    background-color: var(--eds-tag-background-color);
  }

  eds-text {
    --eds-text-color: var(--eds-tag-text-color);
  }

  [part="base"].blue {
    --eds-tag-border: var(--tag-border, var(--eds-stroke-025) var(--eds-border-style-base) var(--eds-colors-info-light));
    --eds-tag-background-color: var(--tag-background-color, var(--eds-colors-info-lighter));
    --eds-tag-text-color: var(--tag-text-color, var(--eds-colors-info-darker));
    --eds-tag-icon-color: var(--tag-icon-color, var(--eds-colors-info-darker));
  }

  [part="base"].green {
    --eds-tag-border: var(--tag-border, var(--eds-stroke-025) var(--eds-border-style-base) var(--eds-colors-success-light));
    --eds-tag-background-color: var(--tag-background-color, var(--eds-colors-success-lighter));
    --eds-tag-text-color: var(--tag-text-color, var(--eds-colors-success-darker));
    --eds-tag-icon-color: var(--tag-icon-color, var(--eds-colors-success-darker));
  }

  [part="base"].orange {
    --eds-tag-border: var(--tag-border, var(--eds-stroke-025) var(--eds-border-style-base) var(--eds-colors-warning-light));
    --eds-tag-background-color: var(--tag-background-color, var(--eds-colors-warning-lighter));
    --eds-tag-text-color: var(--tag-text-color, var(--eds-colors-warning-darker));
    --eds-tag-icon-color: var(--tag-icon-color, var(--eds-colors-warning-darker));
  }

  [part="base"].red {
    --eds-tag-border: var(--tag-border, var(--eds-stroke-025) var(--eds-border-style-base) var(--eds-colors-danger-light));
    --eds-tag-background-color: var(--tag-background-color, var(--eds-colors-danger-lighter));
    --eds-tag-text-color: var(--tag-text-color, var(--eds-colors-danger-darker));
    --eds-tag-icon-color: var(--tag-icon-color, var(--eds-colors-danger-darker));
  }
`;
