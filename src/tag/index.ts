import { LitElement, html, type TemplateResult, type CSSResultGroup } from "lit";
import { property } from "lit/decorators.js";
import { styles } from '../styles';
import { tagStyle } from "./tag.style";
import type { TagAppearances } from './types'
import type { TextSizeValues } from '../text/types';

class Tag extends LitElement {
  static styles: CSSResultGroup = [styles, tagStyle];

  @property({ type: String }) content = "";
  @property({ type: String}) contentSize: TextSizeValues = 'sm';
  @property({ type: String }) appearance: TagAppearances = 'grey';
  @property({ type: Number }) maxLines = 1;

  protected render = (): TemplateResult => {
    return html`
      <div part="base" class="${this.appearance}">
        <eds-text part="text" as="p" size="${this.contentSize}" weight="medium" maxLines="${this.maxLines}" text="${this.content}"></eds-text>
      </div>
    `;
  }
}

if (!customElements.get('eds-tag')) {
  customElements.define('eds-tag', Tag);
}

declare global {
  interface HTMLElementTagNameMap {
    "eds-tag": Tag;
  }
}

export { Tag };
