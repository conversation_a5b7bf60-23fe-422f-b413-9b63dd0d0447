import { css } from 'lit';

export const buttonStyles = css`
  :host {
    /* Base button styles */
    --eds-button-display: var(--button-display, flex);
    --eds-button-align-items: var(--button-align-items, center);
    --eds-button-justify-content: var(--button-justify-content, center);
    --eds-button-padding: var(--button-padding, var(--eds-spacing-200) var(--eds-spacing-400));
    --eds-button-gap: var(--button-gap, var(--eds-spacing-200));
    --eds-button-border-radius: var(--button-border-radius, var(--eds-radius-200));
    --eds-button-border: var(--button-border, var(--eds-stroke-025) var(--eds-border-style-base) transparent);
    --eds-button-transition: var(
      --button-transition,
      var(--eds-transition-property-base) var(--eds-transition-duration-base) var(--eds-transition-timing-function-base)
    );

    /* Primary appearance */
    --eds-button-primary-background-color: var(--button-primary-background-color, var(--eds-colors-primary-default));
    --eds-button-primary-text-color: var(--button-primary-text-color, var(--eds-colors-text-white));
    --eds-button-primary-border-color: var(--button-primary-border-color, var(--eds-colors-primary-default));
    --eds-button-primary-hover-background-color: var(
      --button-primary-hover-background-color,
      var(--eds-colors-primary-dark)
    );
    --eds-button-primary-hover-border-color: var(--button-primary-hover-border-color, var(--eds-colors-primary-dark));
    --eds-button-primary-active-background-color: var(
      --button-primary-active-background-color,
      var(--eds-colors-primary-darker)
    );
    --eds-button-primary-active-border-color: var(--button-primary-active-border-color, var(--eds-colors-primary-darker));

    /* Secondary appearance */
    --eds-button-secondary-background-color: var(
      --button-secondary-background-color,
      var(--eds-colors-secondary-default)
    );
    --eds-button-secondary-text-color: var(--button-secondary-text-color, var(--eds-colors-text-white));
    --eds-button-secondary-border-color: var(--button-secondary-border-color, var(--eds-colors-secondary-default));
    --eds-button-secondary-hover-background-color: var(
      --button-secondary-hover-background-color,
      var(--eds-colors-secondary-dark)
    );
    --eds-button-secondary-hover-border-color: var(
      --button-secondary-hover-border-color,
      var(--eds-colors-secondary-dark)
    );
    --eds-button-secondary-active-background-color: var(
      --button-secondary-active-background-color,
      var(--eds-colors-secondary-darker)
    );
    --eds-button-secondary-active-border-color: var(
      --button-secondary-active-border-color,
      var(--eds-colors-secondary-darker)
    );

    /* Default appearance */
    --eds-button-default-background-color: var(--button-default-background-color, var(--eds-colors-primary-lighter));
    --eds-button-default-text-color: var(--button-default-text-color, var(--eds-colors-text-default));
    --eds-button-default-border-color: var(--button-default-border-color, var(--eds-colors-primary-lighter));
    --eds-button-default-hover-background-color: var(
      --button-default-hover-background-color,
      var(--eds-colors-primary-light)
    );
    --eds-button-default-hover-border-color: var(--button-default-hover-border-color, var(--eds-colors-primary-light));
    --eds-button-default-active-background-color: var(--button-default-active-background-color, var(--eds-primary-300));
    --eds-button-default-active-border-color: var(--button-default-active-border-color, var(--eds-primary-300));

    /* Subtle appearance */
    --eds-button-subtle-background-color: var(--button-subtle-background-color, transparent);
    --eds-button-subtle-text-color: var(--button-subtle-text-color, var(--eds-colors-text-default));
    --eds-button-subtle-border-color: var(--button-subtle-border-color, transparent);
    --eds-button-subtle-hover-background-color: var(
      --button-subtle-hover-background-color,
      var(--eds-colors-primary-light)
    );
    --eds-button-subtle-hover-border-color: var(--button-subtle-hover-border-color, var(--eds-colors-primary-light));
    --eds-button-subtle-active-background-color: var(--button-subtle-active-background-color, var(--eds-primary-200));
    --eds-button-subtle-active-border-color: var(--button-subtle-active-border-color, var(--eds-primary-200));

    /* Link appearance */
    --eds-button-link-height: var(--button-link-height, unset);
    --eds-button-link-padding: var(--button-link-padding, 0);
    --eds-button-link-text-color: var(--button-link-text-color, var(--eds-colors-link));
    --eds-button-link-decoration: var(--button-link-decoration, var(--eds-font-decoration-underline));
    --eds-button-link-hover-decoration: var(--button-link-hover-decoration, none);

    /* Destructive appearance */
    --eds-button-destructive-background-color: var(--button-destructive-background-color, var(--eds-red-50));
    --eds-button-destructive-text-color: var(--button-destructive-text-color, var(--eds-colors-danger-default));
    --eds-button-destructive-border-color: var(--button-destructive-border-color, var(--eds-red-50));
    --eds-button-destructive-hover-background-color: var(
      --button-destructive-hover-background-color,
      var(--eds-red-100)
    );
    --eds-button-destructive-hover-border-color: var(--button-destructive-hover-border-color, var(--eds-red-100));
    --eds-button-destructive-active-background-color: var(
      --button-destructive-active-background-color,
      var(--eds-red-200)
    );
    --eds-button-destructive-active-border-color: var(--button-destructive-active-border-color, var(--eds-red-200));

    /* Default Size */
    --eds-button-default-height: var(--button-default-height, var(--eds-sizing-800));
    --eds-button-default-padding: var(--button-default-padding, var(--eds-spacing-200) var(--eds-spacing-400));
    --eds-button-default-font-size: var(--button-default-font-size, var(--eds-font-size-body-md));
    --eds-button-default-font-weight: var(--button-default-font-weight, var(--eds-font-weight-medium));
    --eds-button-default-line-height: var(--button-default-line-height, var(--eds-line-height-body-md));

    /* Compact Size */
    --eds-button-compact-height: var(--button-compact-height, var(--eds-sizing-700));
    --eds-button-compact-padding: var(--button-compact-padding, var(--eds-spacing-150) var(--eds-spacing-300));
    --eds-button-compact-font-size: var(--button-compact-font-size, var(--eds-font-size-body-sm));
    --eds-button-compact-font-weight: var(--button-compact-font-weight, var(--eds-font-weight-medium));
    --eds-button-compact-line-height: var(--button-compact-line-height, var(--eds-line-height-body-sm));

    /* Line Height Size */
    --eds-button-line-height-height: var(--button-line-height-height, unset);
    --eds-button-line-height-padding: var(--button-line-height-padding, 0);

    /* Icon only */
    --eds-button-icon-only-size: var(--button-icon-only-size, var(--eds-sizing-800));
    --eds-button-icon-only-padding: var(--button-icon-only-padding, var(--eds-spacing-200));
    --eds-button-icon-only-compact-size: var(--button-icon-only-compact-size, var(--eds-sizing-600));
    --eds-button-icon-only-compact-padding: var(--button-icon-only-compact-padding, var(--eds-spacing-150));

    /* Circle */
    --eds-button-circle-size: var(--button-circle-size, var(--eds-sizing-800));
    --eds-button-circle-padding: var(--button-circle-padding, var(--eds-spacing-200));
    --eds-button-circle-icon-size: var(--button-circle-icon-size, var(--eds-sizing-400));
    --eds-button-circle-compact-size: var(--button-circle-compact-size, var(--eds-sizing-600));
    --eds-button-circle-compact-icon-size: var(--button-circle-compact-icon-size, var(--eds-sizing-300));

    /* Icon sizes */
    --eds-button-icon-only-icon-size: var(--button-icon-only-icon-size, var(--eds-sizing-600));
    --eds-button-icon-size: var(--button-icon-size, var(--eds-sizing-600));

    /* States */
    --eds-button-disabled-background-color: var(--button-disabled-background-color, var(--eds-colors-surface-disabled));
    --eds-button-disabled-text-color: var(--button-disabled-text-color, var(--eds-colors-text-disabled));
    --eds-button-disabled-border-color: var(--button-disabled-border-color, var(--eds-colors-surface-disabled));
  }

  /* Base button/link styles */
  [part='base'] {
    position: relative;
    display: var(--eds-button-display);
    align-items: var(--eds-button-align-items);
    justify-content: var(--eds-button-justify-content);
    border-radius: var(--eds-button-border-radius);
    border: var(--eds-button-border);
    transition: var(--eds-button-transition);
    cursor: pointer;
    text-decoration: var(--eds-button-text-decoration);
    height: var(--eds-button-height);
    padding: var(--eds-button-padding);
    font-size: var(--eds-button-font-size);
    font-weight: var(--eds-button-font-weight);
    line-height: var(--eds-button-line-height);
  }

  [part='wrapper'] {
    display: var(--eds-button-display);
    align-items: var(--eds-button-align-items);
    justify-content: var(--eds-button-justify-content);
    gap: var(--eds-button-gap);
    color: inherit;
  }

  /* Default size */
  .size-default {
    --eds-button-height: var(--eds-button-default-height);
    --eds-button-padding: var(--eds-button-default-padding);
    --eds-button-font-size: var(--eds-button-default-font-size);
    --eds-button-font-weight: var(--eds-button-default-font-weight);
    --eds-button-line-height: var(--eds-button-default-line-height);
  }

  /* Compact size */
  .size-compact {
    --eds-button-height: var(--eds-button-compact-height);
    --eds-button-padding: var(--eds-button-compact-padding);
    --eds-button-font-size: var(--eds-button-compact-font-size);
    --eds-button-font-weight: var(--eds-button-compact-font-weight);
    --eds-button-line-height: var(--eds-button-compact-line-height);
  }

  /* Line Height size */
  .size-lineHeight {
    --eds-button-height: var(--eds-button-line-height-height);
    --eds-button-padding: var(--eds-button-line-height-padding);
  }

  /* Primary appearance */
  .appearance-primary {
    background-color: var(--eds-button-primary-background-color);
    color: var(--eds-button-primary-text-color);
    border-color: var(--eds-button-primary-border-color);
  }

  .appearance-primary:hover {
    background-color: var(--eds-button-primary-hover-background-color);
    border-color: var(--eds-button-primary-hover-border-color);
  }

  .appearance-primary:active {
    background-color: var(--eds-button-primary-active-background-color);
    border-color: var(--eds-button-primary-active-border-color);
  }

  /* default appearance */
  .appearance-default {
    background-color: var(--eds-button-default-background-color);
    color: var(--eds-button-default-text-color);
    border-color: var(--eds-button-default-border-color);
  }

  .appearance-default:hover {
    background-color: var(--eds-button-default-hover-background-color);
    border-color: var(--eds-button-default-hover-border-color);
  }

  .appearance-default:active {
    border-color: var(--eds-button-default-active-border-color);
    color: var(--eds-button-default-active-text-color);
    background-color: var(--eds-button-default-active-background-color);
  }

  /* subtle appearance */
  .appearance-subtle {
    background-color: var(--eds-button-subtle-background-color);
    color: var(--eds-button-subtle-text-color);
    border-color: var(--eds-button-subtle-border-color);
  }

  /* subtle appearance */
  .appearance-subtle:hover {
    background-color: var(--eds-button-subtle-hover-background-color);
    border-color: var(--eds-button-subtle-hover-border-color);
  }

  .appearance-subtle:active {
    background-color: var(--eds-button-subtle-active-background-color);
    border-color: var(--eds-button-subtle-active-border-color);
  }

  /* link appearance */
  .appearance-link {
    color: var(--eds-button-link-text-color);
    text-decoration: var(--eds-button-link-decoration);
  }

  .appearance-link:hover {
    text-decoration: var(--eds-button-link-hover-decoration);
  }

  /* destructive appearance */

  .appearance-destructive {
    background-color: var(--eds-button-destructive-background-color);
    color: var(--eds-button-destructive-text-color);
    border-color: var(--eds-button-destructive-border-color);
  }

  .appearance-destructive:hover {
    background-color: var(--eds-button-destructive-hover-background-color);
    border-color: var(--eds-button-destructive-hover-border-color);
  }

  .appearance-destructive:active {
    background-color: var(--eds-button-destructive-active-background-color);
    border-color: var(--eds-button-destructive-active-border-color);
  }

  /* secondary appearance */
  .appearance-secondary {
    background-color: var(--eds-button-secondary-background-color);
    color: var(--eds-button-secondary-text-color);
    border-color: var(--eds-button-secondary-border-color);
  }

  .appearance-secondary:hover {
    background-color: var(--eds-button-secondary-hover-background-color);
    border-color: var(--eds-button-secondary-hover-border-color);
  }

  .appearance-secondary:active {
    background-color: var(--eds-button-secondary-active-background-color);
    border-color: var(--eds-button-secondary-active-border-color);
  }

  /* Full width */
  .fit {
    width: 100%;
  }

  /* States */
  :host([disabled]) [part='base'] {
    background-color: var(--eds-button-disabled-background-color);
    color: var(--eds-button-disabled-text-color);
    border-color: var(--eds-button-disabled-border-color);
    pointer-events: none;
  }

  :host([loading]) {
    pointer-events: none;

    [part='wrapper'] {
      opacity: 0;
    }

    [name='loading'] {
      position: absolute;
      top: 50%;
      left: 50%;
      transform: translate(-50%, -50%);
    }
  }

  eds-icon {
    display: grid;
    place-items: center;
    width: var(--eds-button-icon-size);
    height: var(--eds-button-icon-size);
    flex-shrink: 0;
    color: currentColor;
  }

  /* Icon only appearance */
  .icon-only {
    --eds-button-padding: var(--eds-button-icon-only-padding);
    --eds-button-height: var(--eds-button-icon-only-size);
    width: var(--eds-button-icon-only-size);
  }

  .icon-only.size-compact {
    --eds-button-padding: var(--eds-button-icon-only-compact-padding);
    --eds-button-height: var(--eds-button-icon-only-compact-size);
    width: var(--eds-button-icon-only-compact-size);
  }

  .icon-only eds-icon {
    height: var(--eds-button-icon-only-icon-size);
    width: var(--eds-button-icon-only-icon-size);
  }

  /* Circle appearance */
  .circle {
    --eds-button-padding: var(--eds-button-circle-padding);
    --eds-button-height: var(--eds-button-circle-size);
    --eds-button-width: var(--eds-button-circle-size);
    --eds-button-border-radius: var(--eds-radius-full);
  }

  .circle eds-icon {
    height: var(--eds-button-circle-icon-size);
    width: var(--eds-button-circle-icon-size);
  }

  .circle.size-compact {
    --eds-button-padding: var(--eds-button-circle-compact-padding);
    --eds-button-height: var(--eds-button-circle-compact-size);
    --eds-button-width: var(--eds-button-circle-compact-size);
  }

  .circle.size-compact eds-icon {
    height: var(--eds-button-circle-compact-icon-size);
    width: var(--eds-button-circle-compact-icon-size);
  }
  eds-link {
    color: inherit;
  }

  eds-link::part(base) {
    color: inherit;
    text-decoration: none;
  }
`;
