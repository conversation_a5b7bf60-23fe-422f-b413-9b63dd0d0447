import { LitElement, html, type TemplateResult, type CSSResultGroup } from 'lit';
import { property, query } from 'lit/decorators.js';
import { classMap } from 'lit/directives/class-map.js';
import { styles } from '../styles';
import { buttonStyles } from './button.style';
import "../icons";
import "../link";
import { ButtonAppearanceValues, ButtonSizeValues } from "./types";


class Button extends LitElement {
  static styles: CSSResultGroup = [styles, buttonStyles];

  @property({ type: String }) label = '';
  @property({ type: String }) href = '';
  @property({ type: String }) target: HTMLAnchorElement['target'] = '';
  @property({ type: String }) type: HTMLButtonElement['type'] = 'button';
  @property({ type: String }) appearance: ButtonAppearanceValues = 'primary';
  @property({ type: String }) size: ButtonSizeValues = 'default';
  @property({ type: Boolean }) shouldFitContainer = false;
  @property({ type: String }) iconLeading = '';
  @property({ type: String }) iconTrailing = '';
  @property({ type: Boolean }) iconOnly = false;
  @property({ type: Bo<PERSON>an }) circle = false;
  @property({ type: Boolean, attribute: 'disabled', reflect: true }) disabled = false;
  @property({ type: Boolean, attribute: 'loading', reflect: true }) loading = false;

  @query('button') button!: HTMLButtonElement;
  @query('eds-link') link!: HTMLElement;
  
  protected render = (): TemplateResult => {
    return html`
      <button part="base"
        type="${this.type}"
        @click=${this._onClick}
        ?loading=${this.loading}
        ?disabled=${this.disabled}
        class=${classMap({
          [`appearance-${this.appearance}`]: this.href ? 'appearance-link' : true,
          [`size-${this.size}`]: true,
          'fit': this.shouldFitContainer,
          'icon-only': this.iconOnly,
          'circle': this.circle
        })}
      >
        <div part="wrapper">
          ${this.href ? html`
            <eds-link href=${this.href} target=${this.target}>
              ${this.circle || this.iconOnly ? html`
                <eds-icon part="icon" name=${this.iconLeading}></eds-icon>` : html`
                ${this.iconLeading ? html`
                  <eds-icon part="icon" name=${this.iconLeading}></eds-icon>
                ` : ''}
                <slot part="label"></slot>
                ${this.iconTrailing ? html`
                  <eds-icon part="icon" name=${this.iconTrailing}></eds-icon>
                ` : ''}
              `}
            </eds-link>` : html`
            ${this.circle || this.iconOnly ? html`
                <eds-icon part="icon" name=${this.iconLeading}></eds-icon>` : html`
                ${this.iconLeading ? html`
                  <eds-icon part="icon" name=${this.iconLeading}></eds-icon>
                ` : ''}
                <slot part="label"></slot>
                ${this.iconTrailing ? html`
                  <eds-icon part="icon" name=${this.iconTrailing}></eds-icon>
                ` : ''}
            `}
          `}
        </div>
        ${this.loading ? html`<eds-icon part="icon" name="loading"></eds-icon>` : ''}
      </button>
    `
  }

  private _onClick = (): void => {
    if (this.loading || this.disabled) return;
    
    if (this.href) {
      const linkElement = this.link.shadowRoot?.querySelector('a');
      if (linkElement) {
        linkElement.click();
      }
    } else {
      super.click();
    }
    
    this.dispatchEvent(new CustomEvent('button-click', { 
      bubbles: true,
      composed: true
    }));
  }
}

if (!customElements.get('eds-button')) {
  customElements.define('eds-button', Button);
}

declare global {
  interface HTMLElementTagNameMap {
    "eds-button": Button;
  }
}

export { Button };