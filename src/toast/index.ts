import { LitElement, html, type TemplateResult, type CSSResultGroup } from 'lit';
import { property, query, state } from 'lit/decorators.js';
import { styles } from '../styles';
import { toastStyle } from './toast.style';
import { ToastOptions, ToastPlacementValues } from './types';
import { Alert } from '../alert';

class Toast extends LitElement {
  static styles: CSSResultGroup = [styles, toastStyle];

  @property({ type: String }) placement: ToastPlacementValues = 'top-right';
  @property({ type: Number }) maxStack = 5;
  
  @state() private alertCount = 0;

  @query('[part="base"]') private toastElement!: HTMLDivElement;

  show(options: ToastOptions): Alert {
    const alert = this.createAlert(options);
    
    if (this.toastElement.firstChild) {
      this.toastElement.insertBefore(alert, this.toastElement.firstChild);
    } else {
      this.toastElement.appendChild(alert);
    }
    
    this.alertCount++;
    this.updateAlertOpacity();
    
    // Remove oldest alerts if we exceed a hard limit
    const maxAlerts = this.maxStack + 5;
    while (this.toastElement.children.length > maxAlerts) {
      this.toastElement.lastChild?.remove();
    }
    
    return alert;
  }

  private updateAlertOpacity(): void {
    const alerts = Array.from(this.toastElement.children) as Alert[];
    
    alerts.forEach((alert, index) => {
      if (index >= this.maxStack) {
        const overflowIndex = index - this.maxStack;
        // Calculate decreasing opacity: 0.8, 0.6, 0.4, 0.2
        const opacity = Math.max(0.4, 1 - (overflowIndex + 1) * 0.4);
        alert.style.setProperty('--alert-opacity', opacity.toString());
      } else {
        alert.style.removeProperty('--alert-opacity');
      }
    });
  }

  private createAlert(options: ToastOptions): Alert {
    const { title, description, icon, dismissible = true, duration, onClose, appearance = 'info' } = options;
    const alert = new Alert();
    alert.title = title;
    alert.description = description || '';
    alert.appearance = appearance;
    alert.showIcon = !!icon;
    alert.iconName = icon || '';
    alert.actions = [];
    
    setTimeout(() => {
      alert.setAttribute('style', 'transform: translateX(0);');
    }, 1);
    
    if (duration) {
      alert.setAttribute('data-duration', duration.toString());
      setTimeout(() => {
        alert.removeAttribute('style');
      }, duration * 1000);
      setTimeout(() => {
        alert.remove();
        this.alertCount--;
        this.updateAlertOpacity();
        onClose?.();
      }, duration * 1000 + 444);
    } else {
      alert.dismissible = dismissible;
    }

    alert.addEventListener('alert-dismiss', () => {
      alert.removeAttribute('style');
      setTimeout(() => {
        alert.remove();
        this.alertCount--;
        this.updateAlertOpacity();
        onClose?.();
      }, 444);
    });
    return alert;
  }

  protected render(): TemplateResult {
    return html` <div part="base" class="${this.placement}"></div> `;
  }
}

if (!customElements.get('eds-toast')) {
  customElements.define('eds-toast', Toast);
}

declare global {
  interface HTMLElementTagNameMap {
    'eds-toast': Toast;
  }
}

export { Toast };
