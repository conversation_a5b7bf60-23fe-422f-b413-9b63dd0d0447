import { css, type CSSResultGroup } from 'lit';

export const toastStyle: CSSResultGroup = css`
  :host {
    display: contents;
  }

  [part='base'] {
    display: flex;
    justify-content: flex-end;
    gap: 0.5rem;
    padding: var(--eds-spacing-200);
    z-index: 9999;

    &.top-left, &.bottom-left {
      align-items: flex-start;
    }

    &.top-right, &.bottom-right {
      align-items: flex-end;
    }
  }

  eds-alert {
    --data-duration: attr(data-duration s);
    --alert-opacity: 1;
    transition: all 444ms ease-in-out;
    border-radius: var(--eds-radius-200);
    overflow: hidden;
    opacity: var(--alert-opacity);
    

    &[data-duration]::part(base):before {
      content: '';
      position: absolute;
      bottom: 0;
      right: 0;
      width: 100%;
      height: var(--eds-size-multiplier);
      background-color: var(--eds-colors-primary-default);
      animation: var(--data-duration) linear var(--eds-animation-name-auto-dismiss-slider);
    }
  }

  .bottom-left, .top-left, .bottom-right, .top-right {
    position: fixed;
  }

  .bottom-left {
    flex-direction: column-reverse;
    bottom: 0;
    left: 0;
  }

  .top-left {
    flex-direction: column;
    top: 0;
    left: 0;
  }

  .bottom-right {
    flex-direction: column-reverse;
    bottom: 0;
    right: 0;
  }

  .top-right {
    flex-direction: column;
    top: 0;
    right: 0;
  }

  .bottom-left, .top-left {
    eds-alert {
      transform: translateX(calc(-100% - var(--eds-spacing-200)));
    }
  }

  .bottom-right, .top-right {
    eds-alert {
      transform: translateX(calc(100% + var(--eds-spacing-200)));
    }
  }
`;
