import { LitElement, html, type TemplateResult, type CSSResultGroup } from 'lit';
import { property } from 'lit/decorators.js';
import { unsafeHTML } from 'lit/directives/unsafe-html.js';
import { styles } from '../styles';
import { headingStyle } from './heading.style';
import { HeadingAsValues } from "./types";

class Heading extends LitElement {
  static styles: CSSResultGroup = [styles, headingStyle];

  @property({ type: String }) as: HeadingAsValues = 'h1';
  @property({ type: String }) text = '';
  @property({ type: String }) size = '';

  private tag = (): HeadingAsValues => {
    return this.as;
  }

  private template = (): string => {
    const tag = this.tag();
    return `
      <${tag} 
        part="base" 
        class="${this.size}"
        ${tag.startsWith('h') ? `aria-level="${tag.slice(-1)}"` : ''} 
        ${!tag.startsWith('h') ? 'role="heading"' : ''}
      >
        ${this.text}
      </${tag}>
    `
  }

  protected render = (): TemplateResult => {
    return html`${unsafeHTML(this.template())}`;
  }
}

if (!customElements.get('eds-heading')) {
  customElements.define('eds-heading', Heading);
}

declare global {
  interface HTMLElementTagNameMap {
    "eds-heading": Heading;
  }
}

export { Heading };