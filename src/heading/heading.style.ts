import { css, type CSSResultGroup } from "lit";

export const headingStyle: CSSResultGroup = 
  css`
    :host {
      --eds-heading-text-color: var(--heading-text-color, currentColor);
      --eds-heading-font-weight: var(--heading-font-weight, var(--eds-font-weight-medium));
      --eds-heading-text-decoration: var(--heading-text-decoration, none);
    }
    
    h1, h2, h3, h4, h5, h6, span, div {
      color: var(--eds-heading-text-color);
      text-decoration: var(--eds-heading-text-decoration);
      font-weight: var(--eds-heading-font-weight);
    }

    .xl {
      font-size: var(--eds-font-size-heading-xl);
      line-height: var(--eds-font-line-height-heading-xl);
    }

    .lg {
      font-size: var(--eds-font-size-heading-lg);
      line-height: var(--eds-font-line-height-heading-lg);
    }

    .md {
      font-size: var(--eds-font-size-heading-md);
      line-height: var(--eds-font-line-height-heading-md);
    }

    .sm {
      font-size: var(--eds-font-size-heading-sm);
      line-height: var(--eds-font-line-height-heading-sm);
    }

    .xs {
      font-size: var(--eds-font-size-heading-xs);
      line-height: var(--eds-font-line-height-heading-xs);
    }
  `;
