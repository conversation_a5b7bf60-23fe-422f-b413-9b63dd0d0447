import { css } from "lit";

export const textStyle = css`
  :host {
    --eds-text-color: var(--text-color, currentColor);
    --eds-text-decoration: var(--text-decoration, none);
  }
  
  [part="base"] {
    color: var(--eds-text-color);
    text-decoration: var(--eds-text-decoration);
  }

  .lg {
    font-size: var(--eds-font-size-body-lg);
    line-height: var(--eds-line-height-body-lg);
  }

  .md {
    font-size: var(--eds-font-size-body-md);
    line-height: var(--eds-line-height-body-md);
  }

  .sm {
    font-size: var(--eds-font-size-body-sm);
    line-height: var(--eds-line-height-body-sm);
  }

  .regular {
    font-weight: var(--eds-font-weight-regular);
  }

  .medium {
    font-weight: var(--eds-font-weight-medium);
  }
  
  .line-clamp {
    display: -webkit-box;
    -webkit-box-orient: vertical;
    overflow: hidden;
  }
`;