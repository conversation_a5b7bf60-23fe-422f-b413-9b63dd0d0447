import { LitElement, html, type TemplateResult, type CSSResultGroup } from 'lit';
import { property } from 'lit/decorators.js';
import { unsafeHTML } from 'lit/directives/unsafe-html.js';
import { styles } from '../styles';
import { textStyle } from './text.style';
import type { TextAsValues, TextSizeValues, TextWeightValues } from './types';

class Text extends LitElement {
  static styles: CSSResultGroup = [styles, textStyle];

  @property({ type: String }) id = ''
  @property({ type: String }) as: TextAsValues = 'span';
  @property({ type: String }) size: TextSizeValues = 'md';
  @property({ type: String }) weight: TextWeightValues = 'regular';
  @property({ type: String }) text = '';
  @property({ type: String }) maxLines? = '';

  private tag = (): TextAsValues => {
    return this.as;
  };

  private lineClampRule = (): string => {
    return this.maxLines ? `style="-webkit-line-clamp: ${this.maxLines};"`: "";
  };

  private template = (): string => {
    return `
      <${this.tag()} part="base" id="${this.id}" 
        class="${this.maxLines ? 'line-clamp' : ''} ${this.size} ${this.weight}"
        ${this.lineClampRule()}>
        ${this.text}
      </${this.tag()}>
    `;
  };

  protected render = (): TemplateResult => {
    return html`${unsafeHTML(this.template())}`;
  }
}

if (!customElements.get('eds-text')) {
  customElements.define('eds-text', Text);
}

declare global {
  interface HTMLElementTagNameMap {
    "eds-text": Text;
  }
}

export { Text };