import { LitElement, html, type TemplateResult, type CSSResultGroup } from 'lit';
import { property } from 'lit/decorators.js';
import { classMap } from "lit/directives/class-map.js";
import { styles } from '../styles';
import { dataListStyle } from './data-list.style';
import { DataListItem } from './types';
import "../text";
import "../icons";
import "../button";

class DataList extends LitElement {
  static styles: CSSResultGroup = [styles, dataListStyle];

  @property({ type: Number }) itemsSize = 5;
  @property({ type: Boolean }) trim = false;
  @property({ type: Boolean }) isExpanded = false;
  @property({ type: String }) expandedText = '';
  @property({ type: String }) unexpandedText = '';
  @property({ type: Array }) items: DataListItem[] = [];

  private _toggleShowMore = (): void => {
    this.isExpanded = !this.isExpanded;
  }

  private _renderShowMoreButton = (): TemplateResult => {
    return html`
      <eds-button 
        part="show-more" 
        appearance="link" 
        class=${classMap({
          'list-show-more': true,
          'open': this.isExpanded
        })}
        iconTrailing="arrowDown"
        @button-click="${this._toggleShowMore}"
      >
        <span>${this.isExpanded ? this.expandedText : this.unexpandedText}</span>
      </eds-button>
    `;
  }

  private _renderItems = (): TemplateResult[] => {
    const displayedItems: DataListItem[] = this.isExpanded || !this.trim ? this.items : this.items.slice(0, this.itemsSize);

    return displayedItems.map((item: DataListItem) => {
      return html`
        <div part="item" class="list-item">
          <eds-text part="key" text="${item.key}" as="span" size="lg" weight="regular"></eds-text>
          <eds-text part="value" text="${item.value}" as="span" size="lg" weight="medium"></eds-text>
        </div>
      `;
    });
  }

  protected render = (): TemplateResult => {
    return html`
      <div part="base" class="list-wrapper">
        <div part="wrapper" 
             class=${classMap({
              'list-items-wrapper': true,
              'open': this.isExpanded
            })}>
          ${this._renderItems()}
        </div>
        
        ${this.trim && this.items.length > this.itemsSize ? this._renderShowMoreButton() : html``}
      </div>
    `;
  };
}

if (!customElements.get('eds-data-list')) {
  customElements.define('eds-data-list', DataList);
}

declare global {
  interface HTMLElementTagNameMap {
    'eds-data-list': DataList;
  }
}

export { DataList };