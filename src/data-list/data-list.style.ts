import { css, type CSSResultGroup } from "lit";

export const dataListStyle: CSSResultGroup = css`
  :host {
    --eds-data-list-wrapper-gap: var(--data-list-wrapper-gap, var(--eds-spacing-400));
    --eds-data-list-wrapper-align-items: var(--data-list-wrapper-align-items, center);

    --eds-data-list-item-display: var(--data-list-item-display, flex);
    --eds-data-list-item-align-items: var(--data-list-item-align-items, center);
    --eds-data-list-item-justify-content: var(--data-list-item-justify-content, space-between);
    --eds-data-list-item-gap: var(--data-list-item-gap, var(--eds-spacing-400));
    --eds-data-list-item-padding: var(--data-list-item-padding, var(--eds-spacing-400) 0);
    --eds-data-list-item-border: var(--data-list-item-border, var(--eds-stroke-025) var(--eds-border-style-base) var(--eds-border-color-light));

    --eds-data-list-item-key-text-color: var(--data-list-item-key-text-color, var(--eds-colors-text-default));
    --eds-data-list-item-value-text-color: var(--data-list-item-value-text-color, var(--eds-colors-text-dark));
    --eds-data-list-item-key-text-decoration: var(--data-list-item-key-text-decoration, none);
    --eds-data-list-item-value-text-decoration: var(--data-list-item-value-text-decoration, none);
  }

  .list-item {
    display: var(--eds-data-list-item-display);
    align-items: var(--eds-data-list-item-align-items);
    justify-content: var(--eds-data-list-item-justify-content);
    column-gap: var(--eds-data-list-item-gap);
    padding: var(--eds-data-list-item-padding);
  }

  .list-item:not(:last-child) {
    border-bottom: var(--eds-data-list-item-border);
  }

  .list-item-key {
    color: var(--eds-data-list-item-key-text-color);
    text-decoration: var(--eds-data-list-item-key-text-decoration);
  }

  .list-item-value {
    color: var(--eds-data-list-item-value-text-color);
    text-decoration: var(--eds-data-list-item-value-text-decoration);
  }

  .list-wrapper {
    display: flex;
    align-items: var(--eds-data-list-wrapper-align-items);
    flex-direction: column;
    gap: var(--eds-data-list-wrapper-gap);
  }

  .list-items-wrapper {
    width: 100%;
  }

  .list-show-more::part(icon) {
    transition: var(--eds-rotate-icon-transition-properties);
  }

  .list-show-more.open::part(icon) {
    transform: var(--eds-rotate-icon-base);
  }

  eds-button {
    --eds-button-link-height: var(--eds-data-list-show-more-button-height);
  }
`;
