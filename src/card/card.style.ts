import { css, type CSSResultGroup } from "lit";

export const cardStyle: CSSResultGroup = css`
  :host {
    --eds-card-gap: var(--card-gap, var(--eds-spacing-400));
    --eds-card-border: var(--card-border, var(--eds-stroke-025) var(--eds-border-style-base) var(--eds-border-color-default));
    --eds-card-border-hover: var(--card-border-hover, var(--eds-stroke-025) var(--eds-border-style-base) var(--eds-card-border-hover-color, var(--eds-border-color-default)));
    --eds-card-padding: var(--card-padding, var(--eds-spacing-400));
    --eds-card-border-radius: var(--card-border-radius, var(--eds-radius-500));
    --eds-card-background-color: var(--card-background-color, var(--eds-colors-surface-default));
    --eds-card-header-gap: var(--card-header-gap, var(--eds-spacing-100));
    --eds-card-header-link-color: var(--card-header-link-color, var(--eds-colors-text-dark));
    --eds-card-header-link-gap: var(--card-header-link-gap, var(--eds-spacing-200));
    --eds-card-header-link-font-size: var(--card-header-link-font-size, var(--eds-font-size-body-md));
    --eds-card-box-shadow: var(--card-box-shadow, var(--eds-shadow-sm));
    --eds-card-heading-font-size: var(--card-heading-font-size, var(--eds-font-size-heading-md));
    --eds-card-overflow: var(--card-overflow, hidden);
  }

  @media (min-width: 600px) {
    :host {
      --eds-card-padding: var(--card-padding, var(--eds-spacing-500));
    }
  }

  @media (min-width: 1200px) {
    :host {
      --eds-card-padding: var(--card-padding, var(--eds-spacing-600));
    }
  }

  .card-box {
    position: relative;
    display: flex;
    flex-direction: column;
    gap: var(--eds-card-gap);
    width: 100%;
    padding: var(--eds-card-padding);
    border: var(--eds-card-border);
    border-radius: var(--eds-card-border-radius);
    background: var(--eds-card-background-color);
    box-shadow: var(--eds-card-box-shadow);
    overflow: var(--eds-card-overflow);

    &:hover {
      border: var(--eds-card-border-hover);
    }
  }

  .with-tags {
    overflow: visible;
  }

  .card-box-header {
    display: flex;
    flex-direction: column;
    gap: var(--eds-card-header-gap);
  }

  .card-box-header-content {
    display: flex;
    justify-content: space-between;
    align-items: center;
    gap: var(--eds-card-header-gap);
    flex-wrap: wrap;
  }

  .tags {
    position: absolute;
    top: 0;
    left: var(--eds-card-padding);
    right: var(--eds-card-padding);
    transform: translateY(-50%);
    display: flex;
    overflow-x: auto;
    gap: var(--eds-spacing-200);

    &::-webkit-scrollbar {
      display: none;
    }
  }
`;
