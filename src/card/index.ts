import { LitElement, html, type TemplateResult, type CSSResultGroup } from 'lit';
import { customElement, property } from 'lit/decorators.js';
import { classMap } from 'lit/directives/class-map.js';
import { styles } from '../styles';
import { cardStyle } from './card.style';
import { CardTag } from './types';
import '../heading';
import '../button';
import type { TextSizeValues } from '../text/types';

@customElement('eds-card')
class Card extends LitElement {
  static styles: CSSResultGroup = [styles, cardStyle];

  @property({ type: String }) title = '';
  @property({ type: String }) titleSize: TextSizeValues = 'md';
  @property({ type: String }) linkText = '';
  @property({ type: String }) linkURL = '';
  @property({ type: String }) linkIcon = '';
  @property({ type: String }) description = '';
  @property({ type: String }) hoverBorderColor = '';
  @property({ type: Array }) tags: CardTag[] = [];

  firstUpdated(): void {
    if (this.hoverBorderColor) {
      this.style.setProperty('--eds-card-border-hover-color', this.hoverBorderColor);
    }
  }

  protected render = (): TemplateResult => {
    return html`
      <div
        part="base"
        class="${classMap({
          'card-box': true,
          'with-tags': this.tags?.length > 0,
        })}"
      >
        ${this.title || this.linkText
          ? html`
              <div class="card-box-header">
                <div class="card-box-header-content">
                  ${this.title
                    ? html`
                        <eds-heading part="heading" size="${this.titleSize}" as="h3" text="${this.title}"></eds-heading>
                      `
                    : ''}
                  <slot name="actionButton">
                    ${this.linkText
                      ? html`
                          <eds-button
                            part="link"
                            size="compact"
                            appearance="subtle"
                            iconTrailing="${this.linkIcon}"
                            href="${this.linkURL}"
                            target="_self"
                          >
                            ${this.linkText}
                          </eds-button>
                        `
                      : ''}
                  </slot>
                </div>
                ${this.description
                  ? html` <eds-text part="description" size="md" text="${this.description}"></eds-text> `
                  : ''}
              </div>
            `
          : ''}
        <slot> </slot>
        ${this.tags
          ? html`
              <div class="tags">
                ${this.tags.map(
                  (tag) => html`
                    <eds-tag
                      part="tag"
                      size="md"
                      content="${tag.text}"
                      maxLines="1"
                      appearance="${tag.appearance}"
                    ></eds-tag>
                  `,
                )}
              </div>
            `
          : ''}
      </div>
    `;
  };
}

declare global {
  interface HTMLElementTagNameMap {
    'eds-card': Card;
  }
}

export { Card };
