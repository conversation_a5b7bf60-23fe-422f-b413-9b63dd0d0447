import { css, type CSSResultGroup } from 'lit';

const mediaObjectStyle: CSSResultGroup = css`
  :host {
    --eds-media-object-align-items: var(--media-object-align-items, center);
    --eds-media-object-gap: var(--media-object-gap, var(--eds-spacing-400));
    --eds-media-object-image-border: var(--media-object-image-border, none);
    --eds-media-object-image-border-radius: var(--media-object-image-border-radius, none);
    --eds-media-object-image-background-color: var(--media-object-image-background-color, transparent);
    --eds-media-object-image-min-width: var(--media-object-image-min-width, var(--eds-sizing-900));
    --eds-media-object-image-min-height: var(--media-object-image-min-height, var(--eds-sizing-900));
    --eds-media-object-icon-size: var(--media-object-icon-size, var(--eds-sizing-600));
    --eds-media-object-icon-color: var(--media-object-icon-color, var(--eds-colors-primary-darker));
    --eds-media-object-content-gap: var(--media-object-content-gap, 0);
  }
  
  [part="base"] {
    display: flex;
    align-items: var(--eds-media-object-align-items);
    gap: var(--eds-media-object-gap);
  }

  [part="image-wrapper"] {
    display: flex;
    align-items: center;
    justify-content: center;
    flex-shrink: 0;
    overflow: hidden;
    border: var(--eds-media-object-image-border);
    border-radius: var(--eds-media-object-image-border-radius);
    background-color: var(--eds-media-object-image-background-color);
    min-width: var(--eds-media-object-image-min-width);
    min-height: var(--eds-media-object-image-min-height);
  }
  
  [part="icon"] {
    width: var(--eds-media-object-icon-size);
    height: var(--eds-media-object-icon-size);
    color: var(--eds-media-object-icon-color);
    flex-shrink: 0;
  }

  [part="content"] {
    display: flex;
    flex-direction: column;
    gap: var(--eds-media-object-content-gap);
  }
`

export { mediaObjectStyle };