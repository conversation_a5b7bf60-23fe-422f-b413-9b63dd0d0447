import { LitElement, html, type TemplateResult, type CSSResultGroup } from 'lit';
import { property } from 'lit/decorators.js';
import { styles } from '../styles';
import { mediaObjectStyle } from './media-object.style';
import "../image";
import "../text";
import "../icons";

class MediaObject extends LitElement {
  static styles: CSSResultGroup = [styles, mediaObjectStyle];

  @property({ type: String }) src = '';
  @property({ type: String }) upperText = '';
  @property({ type: String }) text = '';
  @property({ type: String }) description = '';
  @property({ type: String }) iconName = '';
  @property({ type: String }) width = '';
  @property({ type: String }) height = '';

  private _renderMedia = (): TemplateResult => {
    if(this.iconName) {
      return html`<eds-icon part="icon" name="${this.iconName}"></eds-icon>`;
    }

    return html`
      <div part="image-wrapper">
          ${this.src ? html`
            <eds-image part="image" src="${this.src}" width="${this.width}" height="${this.height}"></eds-image>` 
            : html`<slot part="image" name="image"></slot>`}
      </div>
    `;
  }

  protected override render = (): TemplateResult => {
    return html`
      <div part="base">
        ${this._renderMedia()}
        <div part="content">
          ${this.upperText ? html`<eds-text part="upperText" as="p" size="md" weight="regular" text="${this.upperText}"></eds-text>` : ''}
          ${this.text ? html`<eds-text part="text" as="p" size="lg" weight="regular" text="${this.text}"></eds-text>` : ''}
          ${this.description ? html`<eds-text part="description" as="p" size="md" weight="regular" text="${this.description}"></eds-text>` : ''}
        </div>
      </div>
    `;
  }
}

if (!customElements.get('eds-media-object')) {
  customElements.define('eds-media-object', MediaObject);
}

declare global {
  interface HTMLElementTagNameMap {
    'eds-media-object': MediaObject;
  }
}

export { MediaObject };