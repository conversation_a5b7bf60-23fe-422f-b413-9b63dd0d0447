
/* Box sizing rules */
:host *,
:host *::before,
:host *::after,
:root *,
:root *::before,
:root *::after {
  box-sizing: border-box;
}

/* Make images easier to work with */
img,
picture,
:host img,
:host picture {
  max-width: 100%;
  display: block;
}

/* Make sure textareas without a rows attribute are not tiny */
textarea:not([rows]),
:host textarea:not([rows]) {
  min-height: 10em;
}

/* Anything that has been anchored to should have extra scroll margin */
:target,
:host :target {
  scroll-margin-block: 5ex;
}

/* Button Reset */
button {
  all: unset;
}

:host,
  :root {
    --eds-font-sans: var(--font-sans, -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Helvetica, Arial, sans-serif, 'Apple Color Emoji', 'Segoe UI Emoji', 'Segoe UI Symbol');
    --eds-font-mono: var(--font-mono, SFMono-Regular, Consolas, 'Liberation Mono', Menlo, monospace);
    --eds-font-serif: var(--font-serif, Georgia, 'Times New Roman', serif);

    font-family: var(--eds-font-sans);

    /* Prevent font size inflation */
    --text-size-adjust-base: none;
    -moz-text-size-adjust: var(--text-size-adjust-base);
    -webkit-text-size-adjust: var(--text-size-adjust-base);
    text-size-adjust: var(--text-size-adjust-base);
  }

  :host,
  body {
    font-size: var(--eds-font-size-body-md);
    color: var(--eds-colors-text-default);
  }

  /* Remove default margin in favour of better control in authored CSS */
  body, h1, h2, h3, h4, h5, h6, p,
  figure, blockquote, dl, dd,
  :host, :host h1, :host h2, :host h3, :host h4, :host h5, :host h6, :host p,
  :host figure, :host blockquote, :host dl, :host dd {
    --margin-block-start: 0;
    --margin-block-end: 0;
    margin-block-start: var(--margin-block-start);
    margin-block-end: var(--margin-block-end);
  }

  /* Set core body defaults */
  body {
    min-height: 100svh;
  }

  /* Balance text wrapping on headings */
  h1, h2, h3, h4, h5, h6,
  :host h1, :host h2,
  :host h3, :host h4, :host h5, :host h6 {
    --eds-typography-text-wrap-base: var(--typography-text-wrap-base, balance);
    text-wrap: var(--eds-typography-text-wrap-base);
  }

  /* Remove list styles on ul, ol elements with a list role, which suggests default styling will be removed */
  ul[role='list'],
  ol[role='list'],
  :host ul[role='list'],
  :host ol[role='list'] {
    --typography-list-style-base: none;
    list-style: var(--typography-list-style-base);
  }

  /* Inherit fonts for inputs and buttons */
  input, button,
  textarea, select,
  :host input, :host button,
  :host textarea, :host select {
    font-family: inherit;
    font-size: inherit;
  }

  /* A elements that don't have a class get default styles */
  a:not([class]),
  :host a:not([class]) {
    text-decoration-skip-ink: auto;
  }

  del {
    color: var(--eds-colors-text-light);
  }
