:root,
:host {
  --eds-colors-body: var(--color-body, var(--eds-primary-50));

  --eds-colors-primary-darker: var(--color-primary-darker, var(--eds-primary-950));
  --eds-colors-primary-dark: var(--color-primary-dark, var(--eds-primary-800));
  --eds-colors-primary-default: var(--color-primary-default, var(--eds-primary-700));
  --eds-colors-primary-light: var(--color-primary-light, var(--eds-primary-100));
  --eds-colors-primary-lighter: var(--color-primary-lighter, var(--eds-primary-50));

  --eds-colors-secondary-darker: var(--color-secondary-darker, var(--eds-secondary-900));
  --eds-colors-secondary-dark: var(--color-secondary-dark, var(--eds-secondary-700));
  --eds-colors-secondary-default: var(--color-secondary-default, var(--eds-secondary-500));
  --eds-colors-secondary-light: var(--color-secondary-light, var(--eds-secondary-400));
  --eds-colors-secondary-lighter: var(--color-secondary-lighter, var(--eds-secondary-50));

  --eds-colors-surface-level-2: var(--color-surface-level-2, var(--eds-primary-100));
  --eds-colors-surface-level-1: var(--color-surface-level-1, var(--eds-primary-50));
  --eds-colors-surface-default: var(--color-surface-default, var(--eds-white));
  --eds-colors-surface-disabled: var(--color-surface-disabled, var(--eds-primary-100));

  --eds-colors-link: var(--color-link, var(--eds-blue-500));
  --eds-colors-link-hover: var(--color-link-hover, var(--eds-blue-400));

  --eds-colors-text-dark: var(--color-text-dark, var(--eds-primary-900));
  --eds-colors-text-default: var(--color-text-default, var(--eds-primary-700));
  --eds-colors-text-light: var(--color-text-light, var(--eds-primary-500));
  --eds-colors-text-disabled: var(--color-text-disabled, var(--eds-primary-400));
  --eds-colors-text-white: var(--color-text-white, var(--eds-white));

  --eds-colors-info-darker: var(--color-info-darker, var(--eds-blue-900));
  --eds-colors-info-dark: var(--color-info-dark, var(--eds-blue-500));
  --eds-colors-info-default: var(--color-info-default, var(--eds-blue-300));
  --eds-colors-info-light: var(--color-info-light, var(--eds-blue-200));
  --eds-colors-info-lighter: var(--color-info-lighter, var(--eds-blue-50));

  --eds-colors-success-darker: var(--color-success-darker, var(--eds-green-900));
  --eds-colors-success-dark: var(--color-success-dark, var(--eds-green-700));
  --eds-colors-success-default: var(--color-success-default, var(--eds-green-500));
  --eds-colors-success-light: var(--color-success-light, var(--eds-green-200));
  --eds-colors-success-lighter: var(--color-success-lighter, var(--eds-green-50));

  --eds-colors-warning-darker: var(--color-warning-darker, var(--eds-orange-900));
  --eds-colors-warning-dark: var(--color-warning-dark, var(--eds-orange-700));
  --eds-colors-warning-default: var(--color-warning-default, var(--eds-orange-500));
  --eds-colors-warning-light: var(--color-warning-light, var(--eds-orange-200));
  --eds-colors-warning-lighter: var(--color-warning-lighter, var(--eds-orange-50));

  --eds-colors-danger-darker: var(--color-danger-darker, var(--eds-red-900));
  --eds-colors-danger-dark: var(--color-danger-dark, var(--eds-red-700));
  --eds-colors-danger-default: var(--color-danger-default, var(--eds-red-500));
  --eds-colors-danger-light: var(--color-danger-light, var(--eds-red-200));
  --eds-colors-danger-lighter: var(--color-danger-lighter, var(--eds-red-50));

  --eds-colors-icon-dark: var(--color-icon-dark, var(--eds-primary-900));
  --eds-colors-icon-default: var(--color-icon-default, var(--eds-primary-700));
  --eds-colors-icon-light: var(--color-icon-light, var(--eds-primary-400));
  --eds-colors-icon-disabled: var(--color-icon-disabled, var(--eds-primary-400));
  --eds-colors-icon-white: var(--color-icon-white, var(--eds-white));

  --eds-sizing-900: var(--sizing-900, calc(var(--eds-size-multiplier) * 12));
  --eds-sizing-800: var(--sizing-800, calc(var(--eds-size-multiplier) * 10));
  --eds-sizing-700: var(--sizing-700, calc(var(--eds-size-multiplier) * 8));
  --eds-sizing-600: var(--sizing-600, calc(var(--eds-size-multiplier) * 6));
  --eds-sizing-500: var(--sizing-500, calc(var(--eds-size-multiplier) * 5));
  --eds-sizing-400: var(--sizing-400, calc(var(--eds-size-multiplier) * 4));
  --eds-sizing-300: var(--sizing-300, calc(var(--eds-size-multiplier) * 3));
  --eds-sizing-200: var(--sizing-200, calc(var(--eds-size-multiplier) * 2));
  --eds-sizing-150: var(--sizing-150, calc(var(--eds-size-multiplier) * 1.5));
  --eds-sizing-100: var(--sizing-100, calc(var(--eds-size-multiplier) * 1));
  --eds-sizing-050: var(--sizing-050, calc(var(--eds-size-multiplier) * .5));
  --eds-sizing-025: var(--sizing-025, calc(var(--eds-size-multiplier) * .25));
  --eds-sizing-0: var(--sizing-0, calc(var(--eds-size-multiplier) * 0));

  --eds-radius-full: var(--radius-full, 9999px);
  --eds-radius-600: var(--radius-600, calc(var(--eds-size-multiplier) * 6));
  --eds-radius-500: var(--radius-500, calc(var(--eds-size-multiplier) * 5));
  --eds-radius-400: var(--radius-400, calc(var(--eds-size-multiplier) * 4));
  --eds-radius-300: var(--radius-300, calc(var(--eds-size-multiplier) * 3));
  --eds-radius-200: var(--radius-200, calc(var(--eds-size-multiplier) * 2));
  --eds-radius-150: var(--radius-150, calc(var(--eds-size-multiplier) * 1.5));
  --eds-radius-100: var(--radius-100, calc(var(--eds-size-multiplier) * 1));
  --eds-radius-050: var(--radius-050, calc(var(--eds-size-multiplier) * 0.5));
  --eds-radius-0: var(--radius-0, calc(var(--eds-size-multiplier) * 0));

  --eds-spacing-900: var(--spacing-900, calc(var(--eds-size-multiplier) * 12));
  --eds-spacing-800: var(--spacing-800, calc(var(--eds-size-multiplier) * 10));
  --eds-spacing-700: var(--spacing-700, calc(var(--eds-size-multiplier) * 8));
  --eds-spacing-600: var(--spacing-600, calc(var(--eds-size-multiplier) * 6));
  --eds-spacing-500: var(--spacing-500, calc(var(--eds-size-multiplier) * 5));
  --eds-spacing-400: var(--spacing-400, calc(var(--eds-size-multiplier) * 4));
  --eds-spacing-300: var(--spacing-300, calc(var(--eds-size-multiplier) * 3));
  --eds-spacing-200: var(--spacing-200, calc(var(--eds-size-multiplier) * 2));
  --eds-spacing-150: var(--spacing-150, calc(var(--eds-size-multiplier) * 1.5));
  --eds-spacing-100: var(--spacing-100, calc(var(--eds-size-multiplier) * 1));
  --eds-spacing-050: var(--spacing-050, calc(var(--eds-size-multiplier) * .5));
  --eds-spacing-025: var(--spacing-025, calc(var(--eds-size-multiplier) * .25));
  --eds-spacing-0: var(--spacing-0, calc(var(--eds-size-multiplier) * 0));

  --eds-stroke-050: var(--stroke-050, calc(var(--eds-size-multiplier) * .5));
  --eds-stroke-025: var(--stroke-025, calc(var(--eds-size-multiplier) * .25));

  --eds-border-color-dark: var(--border-color-dark, var(--eds-primary-300));
  --eds-border-color-default: var(--border-color-default, var(--eds-primary-200));
  --eds-border-color-light: var(--border-color-light, var(--eds-primary-100));

  --eds-border-style-base: var(--border-style-base, solid);

  --eds-shadow-sm: var(--box-shadow-sm, 0 calc(var(--eds-size-multiplier) * .75) 0 0 oklch(0 0 0 / .04));
  --eds-shadow-md: var(--box-shadow-md, 0 var(--eds-sizing-100) var(--eds-sizing-400) 0 oklch(0 0 0 / .16));

  --eds-ring-default: var(--ring-default, 0 0 0 var(--eds-spread-default) var(--eds-primary-200));
  --eds-ring-sm: var(--ring-sm, 0 0 0 var(--eds-spread-sm) var(--eds-primary-200));

  --eds-font-size-heading-xl: var(--font-size-heading-xl, calc(var(--eds-size-multiplier) * 6));
  --eds-font-size-heading-lg: var(--font-size-heading-lg, calc(var(--eds-size-multiplier) * 5));
  --eds-font-size-heading-md: var(--font-size-heading-md, calc(var(--eds-size-multiplier) * 5));
  --eds-font-size-heading-sm: var(--font-size-heading-sm, calc(var(--eds-size-multiplier) * 4.5));
  --eds-font-size-heading-xs: var(--font-size-heading-xs, calc(var(--eds-size-multiplier) * 3));

  --eds-line-height-heading-xl: var(--line-height-heading-xl, calc(var(--eds-size-multiplier) * 7));
  --eds-line-height-heading-lg: var(--line-height-heading-lg, calc(var(--eds-size-multiplier) * 6));
  --eds-line-height-heading-md: var(--line-height-heading-md, calc(var(--eds-size-multiplier) * 6));
  --eds-line-height-heading-sm: var(--line-height-heading-sm, calc(var(--eds-size-multiplier) * 5));
  --eds-line-height-heading-xs: var(--line-height-heading-xs, calc(var(--eds-size-multiplier) * 4));

  --eds-font-size-body-lg: var(--font-size-body-lg, calc(var(--eds-size-multiplier) * 4));
  --eds-font-size-body-md: var(--font-size-body-md, calc(var(--eds-size-multiplier) * 3.5));
  --eds-font-size-body-sm: var(--font-size-body-sm, calc(var(--eds-size-multiplier) * 3));

  --eds-line-height-body-lg: var(--line-height-body-lg, calc(var(--eds-size-multiplier) * 6));
  --eds-line-height-body-md: var(--line-height-body-md, calc(var(--eds-size-multiplier) * 5));
  --eds-line-height-body-sm: var(--line-height-body-sm, calc(var(--eds-size-multiplier) * 4));

  --eds-font-weight-regular: var(--font-weight-regular, 400);
  --eds-font-weight-medium: var(--font-weight-medium, 500);

  --eds-font-decoration-underline: var(--font-decoration-underline, underline);

  --eds-transition-property-base: var(--transition-property-all, all);
  --eds-transition-duration-base: var(--transition-duration-base, .1s);
  --eds-transition-timing-function-base: var(--transition-timing-function-base, ease-in-out);
  --eds-transition-timing-function-cubic-bezier-base: var(--transition-timing-function-cubic-bezier-base, cubic-bezier(0.43, 0.41, 0.22, 0.91));

  --eds-rotate-icon-base: var(--rotate-icon-base, rotate(180deg));
  --eds-rotate-icon-transition-properties: var(--rotate-icon-transition-properties, all var(--eds-transition-duration-base) var(--eds-transition-timing-function-base));

  --eds-animation-name-spin: var(--animation-name-spin, spin);
  --eds-animation-name-auto-dismiss-slider: var(--animation-name-auto-dismiss-slider, autoDismissSlider);
  --eds-animation-duration-base: var(--animation-duration-base, .5s);
  --eds-animation-timing-function-base: var(--animation-timing-function-base, linear);
  --eds-animation-timing-function-step-start: var(--animation-timing-function-step-start, step-start);
  --eds-animation-direction-base: var(--animation-direction-base, normal);
  --eds-animation-direction-infinite: var(--animation-direction-infinite, infinite);

  --eds-pointer-events-base: var(--pointer-events-base, auto);
  --eds-pointer-events-disabled: var(--pointer-events-disabled, none);
}

@media (min-width: 600px) {
  :host {
    --eds-font-size-heading-xl: var(--font-size-heading-xl, calc(var(--eds-size-multiplier) * 7));
    --eds-font-size-heading-lg: var(--font-size-heading-lg, calc(var(--eds-size-multiplier) * 6));

    --eds-line-height-heading-xl: var(--line-height-heading-xl, calc(var(--eds-size-multiplier) * 8));
    --eds-line-height-heading-lg: var(--line-height-heading-lg, calc(var(--eds-size-multiplier) * 7));
  }
}

@media (min-width: 1200px) {
  :host {
    --eds-font-size-heading-xl: var(--font-size-heading-xl, calc(var(--eds-size-multiplier) * 8));

    --eds-line-height-heading-xl: var(--line-height-heading-xl, calc(var(--eds-size-multiplier) * 9));
  }
}