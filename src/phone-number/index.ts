import { LitElement, html, type TemplateResult, type CSSResultGroup } from 'lit';
import { property, state, query } from 'lit/decorators.js';
import { styles } from '../styles';
import { phoneNumberStyle } from './phone-number.style';
import '../select';
import '../text-field';
import { type TextField } from '../text-field';
import { CountryOption, PhoneNumberAppearanceValues } from './types';
import IMask from 'imask';

class PhoneNumber extends LitElement {
  static styles: CSSResultGroup = [styles, phoneNumberStyle];

  @property({ type: String }) id = '';
  @property({ type: String }) appearance: PhoneNumberAppearanceValues = 'default';
  @property({ type: { phone: String, countryCode: String } }) value: { phone: string; countryCode: string } = {
    phone: '',
    countryCode: '',
  };
  @property({ type: Boolean }) isInvalid = false;
  @property({ type: Boolean }) isDisabled = false;
  @property({ type: Boolean }) isReadonly = false;
  @property({ type: Boolean }) isRequired = false;
  @property({ type: Boolean }) isCompact = false;
  @property({ type: String }) placeholder = '';
  @property({ type: Array }) countryOptions: CountryOption[] = [];

  @state() private mask?: string;

  @query('eds-text-field') textFieldEl!: TextField;
  private input!: HTMLInputElement;

  protected override firstUpdated = async (): Promise<void> => {
    await this.updateComplete;

    this.input = this.textFieldEl.shadowRoot?.querySelector('input') as HTMLInputElement;
    if (!this.input) return;
    this.setMask();
  };

  private setMask = (): void => {
    const country = this.countryOptions.find((option) => option.value === this.value.countryCode);
    if (country) {
      this.mask = country.mask;
    }
    if (this.input && this.mask) {
      IMask(this.input, {
        mask: this.mask,
      });
    }
  };

  private _handleCountryChange = (e: CustomEvent<{ selectedOption: { value: string } }>): void => {
    if (e.detail.selectedOption) {
      this.value = { phone: this.value?.phone, countryCode: e.detail.selectedOption.value };
      this.input?.focus();
      this.setMask();
    }
  };

  private _handlePhoneChange = (e: Event): void => {
    this.value = { phone: (e.target as HTMLInputElement)?.value, countryCode: this.value?.countryCode };
  };

  protected override render(): TemplateResult {
    return html`
      <div part="base">
        <eds-select
          part="country-code"
          id="${this.id}"
          appearance="${this.appearance}"
          value="${this.value.countryCode}"
          ?isInvalid="${this.isInvalid}"
          ?isDisabled="${this.isDisabled}"
          ?isReadonly="${this.isReadonly}"
          ?isRequired="${this.isRequired}"
          ?isCompact="${this.isCompact}"
          @input="${this._handleCountryChange}"
          @change="${this._handleCountryChange}"
          @focus="${this._handleCountryChange}"
          @blur="${this._handleCountryChange}"
        >
          ${this.countryOptions.map(
            (item) =>
              html`<eds-select-option
                ?isSelected="${item.isSelected}"
                value="${item.value}"
                label="${item.label}"
              ></eds-select-option>`,
          )}
        </eds-select>
        <eds-text-field
          part="phone-number"
          type="tel"
          appearance="${this.appearance}"
          value="${this.value.phone}"
          ?isInvalid="${this.isInvalid}"
          ?isDisabled="${this.isDisabled}"
          ?isReadonly="${this.isReadonly}"
          ?isRequired="${this.isRequired}"
          ?isCompact="${this.isCompact}"
          placeholder="${this.placeholder}"
          @input="${this._handlePhoneChange}"
          @change="${this._handlePhoneChange}"
          @focus="${this._handlePhoneChange}"
          @blur="${this._handlePhoneChange}"
        ></eds-text-field>
      </div>
    `;
  }
}

if (!customElements.get('eds-phone-number')) {
  customElements.define('eds-phone-number', PhoneNumber);
}

declare global {
  interface HTMLElementTagNameMap {
    'eds-phone-number': PhoneNumber;
  }
}

export { PhoneNumber };
