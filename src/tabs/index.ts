import { LitElement, html, type TemplateResult, type CSSResultGroup } from 'lit';
import { customElement, property, queryAssignedElements, query } from 'lit/decorators.js';
import { styles } from '../styles';
import { tabsStyle } from './tabs.style';
import { Tab } from './tab';
import { TabPanel } from './tab-panel';
import { classMap } from 'lit/directives/class-map.js';

@customElement('eds-tabs')
class Tabs extends LitElement {
  static styles: CSSResultGroup = [styles, tabsStyle];
  
  @property({ type: String }) appearance: 'default' | 'line' = 'default';
  @property({ type: Boolean, reflect: true }) isBlock = false;

  @query('.tabs') tabListEl!: HTMLDivElement;
  
  @queryAssignedElements({ selector: 'eds-tab', slot: 'tabs', flatten: true })
  private tabElements!: Tab[];
  
  @queryAssignedElements({ selector: 'eds-tab-panel', flatten: true })
  private tabPanelElements!: TabPanel[];

  protected firstUpdated(): void {
    this.initializeTabs();
  }

  private initializeTabs(): void {
    if (!this.tabElements.length || !this.tabPanelElements.length) {
      return;
    }
    
    let selectedTabIndex = this.tabElements.findIndex(tab => tab.selected);
    if (selectedTabIndex < 0) {
      selectedTabIndex = 0;
      this.tabElements[selectedTabIndex].selected = true;
    }

    const selectedTab = this.tabElements[selectedTabIndex];
    selectedTab.setAttribute('tabindex', '0');
    
    this.showSelectedTabPanel(selectedTab);
    
    this.setupTabPanelRelationships();
    
    this.updateComplete.then(() => {
      this.scrollToSelectedTab(selectedTab, 'instant');
    });
  }

  private setupTabPanelRelationships(): void {
    this.tabElements.forEach(tab => {
      const relatedTabPanel = this.tabPanelElements.find(panel => tab.id === panel.tab);
      if (relatedTabPanel) {
        tab.setAttribute('aria-controls', relatedTabPanel.id);
        relatedTabPanel.setAttribute('aria-labelledby', tab.id);
      }
    });
  }

  private showSelectedTabPanel(selectedTab: Tab): void {
    this.tabPanelElements.forEach(panel => {
      if (panel.tab === selectedTab.id) {
        panel.hidden = false;
      } else {
        panel.hidden = true;
      }
    });
  }

  private scrollToSelectedTab(selectedTab: Tab, behavior: ScrollBehavior = 'smooth'): void {
    if (!this.tabListEl) return;
    
    const tabListWidth = this.tabListEl.offsetWidth;
    const selectedTabLeft = selectedTab.offsetLeft;
    const selectedTabWidth = selectedTab.offsetWidth;
    const tabListLeft = this.tabListEl.scrollLeft;

    if (tabListWidth + tabListLeft < selectedTabLeft + selectedTabWidth * 2) {
      const scrollLeft = selectedTabLeft - (tabListWidth / 4);
      this.tabListEl.scrollTo({ left: scrollLeft, behavior });
    }

    else if (tabListLeft * 2 >= selectedTabLeft) {
      const scrollLeft = selectedTabLeft - (tabListWidth / 4);
      this.tabListEl.scrollTo({ left: scrollLeft, behavior });
    }
  }

  private handleTabClick = (e: CustomEvent<{ selectedTab: Tab }>): void => {
    const selectedTab = e.detail.selectedTab;
    
    this.tabElements.forEach(tab => {
      if (tab.id === selectedTab.id) {
        tab.selected = true;
      } else {
        tab.selected = false;
      }
    });

    this.showSelectedTabPanel(selectedTab);
    
    this.scrollToSelectedTab(selectedTab);
  }

  protected render = (): TemplateResult => {
    return html`
      <div part="base" class="base" @tab-click="${this.handleTabClick}">
        <div part="tab-list" role="tablist" class="${classMap({
          'tabs': true,
          'block': this.isBlock,
          [this.appearance]: true
        })}">
          <slot name="tabs"></slot>
        </div>
        <div part="tab-panels" class="tab-panels">
          <slot></slot>
        </div>
      </div>
    `;
  }
}

declare global {
  interface HTMLElementTagNameMap {
    'eds-tabs': Tabs;
  }
}

export { Tabs, Tab, TabPanel };