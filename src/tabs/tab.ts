import { LitElement, html, type TemplateResult, type CSSResultGroup } from 'lit';
import { customElement, property, queryAssignedElements, state } from 'lit/decorators.js';
import { classMap } from "lit/directives/class-map.js";
import { styles } from '../styles';
import { tabsStyle } from './tabs.style';

@customElement('eds-tab')
export class Tab extends LitElement {
  static styles: CSSResultGroup = [styles, tabsStyle];

  @property({ type: String }) id = '';
  @property({ type: Boolean, reflect: true }) selected = false;
  @property({ type: Boolean, reflect: true }) isDisabled = false;
  
  @state() isBlock = false;

  @queryAssignedElements({ slot: 'icon', flatten: true })
  private tabIconElements!: HTMLElement[];

  firstUpdated(): void {
    this.setAttribute('tabindex', '-1');
    this.setAttribute('role', 'tab');

    if (!this.id) {
      this.id = `tab-${Math.random().toString(36).substring(2, 11)}`;
    }

    setTimeout(() => {
      if (this.parentElement && this.parentElement.tagName === 'eds-tabs') {
        // @ts-ignore - we know it's an 'eds-tabs' element with isBlock property
        this.isBlock = this.parentElement.isBlock;
        this.requestUpdate();
      }
    }, 0);
  }

  private _handleClick = (e: Event): void => {
    if (this.isDisabled) {
      e.preventDefault();
      return;
    }
    
    this.dispatchEvent(new CustomEvent('tab-click', {
      bubbles: true,
      composed: true,
      detail: {
        selectedTab: this,
      }
    }));
  }

  updated(changedProperties: Map<string, unknown>): void {
    super.updated(changedProperties);
    
    if (changedProperties.has('selected')) {
      this.setAttribute('aria-selected', this.selected.toString());
      this.setAttribute('tabindex', this.selected ? '0' : '-1');
    }
  }

  protected render = (): TemplateResult => {
    return html`
      <button 
        part="base" 
        ?disabled="${this.isDisabled}" 
        @click="${this._handleClick}" 
        class="${classMap({
          'tab': true,
          'selected': this.selected,
          'disabled': this.isDisabled,
          'block': this.isBlock
        })}"
        id="${this.id}"
      >
        <slot name="icon"></slot>
        <span part="text">
          <slot></slot>
        </span>
        <slot name="badge"></slot>
      </button>
    `;
  }
}

declare global {
  interface HTMLElementTagNameMap {
    'eds-tab': Tab;
  }
}