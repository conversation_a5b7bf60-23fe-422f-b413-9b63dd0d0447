import { css, type CSSResultGroup } from 'lit';

export const tabsStyle: CSSResultGroup = css`
  :host {
    --eds-tabs-icon-size: var(--tabs-icon-size, var(--eds-sizing-600));
    
    --eds-tabs-disabled-text-color: var(--tabs-disabled-text-color, var(--eds-colors-text-disabled));

    --eds-tabs-scrollbar-display: var(--tabs-scrollbar-display, none);
  }

  .base {
    display: grid;
    place-items: center;
    gap: var(--eds-spacing-400);
  }

  .base:has(.block) {
    place-items: unset;
  }

  .default {
    --eds-tab-background-color: var(--eds-colors-primary-default);
    --eds-tab-text-selected-color: var(--eds-colors-text-white);
    --eds-tab-text-hover-color: var(--eds-colors-secondary-default);
    --eds-tab-border-radius: calc(var(--eds-radius-400) - var(--eds-spacing-100));
    --eds-tab-outline-properties: 0;

    padding: var(--eds-spacing-100);
    border: 1px solid var(--eds-border-color-default);
    border-radius: var(--eds-radius-400);
  }

  .line {
    --eds-tab-background-color: transparent;
    --eds-tab-text-selected-color: var(--eds-colors-text-default);
    --eds-tab-text-hover-color: var(--eds-colors-text-default);
    --eds-tab-border-radius: 0;
    --eds-tab-outline-properties: inset 0 -4px 0 -2px var(--eds-colors-primary-default);
  
    border-bottom: 1px solid var(--eds-border-color-default);
  }

  .tab {
    display: flex;
    align-items: center;
    justify-content: center;
    padding: var(--eds-spacing-200) var(--eds-spacing-400);
    gap: var(--eds-spacing-200);
    border-radius: var(--eds-tab-border-radius);
    width: var(--eds-tab-block);

    [part="text"] {
      color: var(--eds-tab-text-color);
    }
  }

  .tab:hover {
    [part="text"],
    ::slotted(eds-icon),
    ::slotted(svg) {
      color: var(--eds-tab-text-hover-color);
    }
  }

  .tab.selected {
    background-color: var(--eds-tab-background-color);
    box-shadow: var(--eds-tab-outline-properties);
    
    [part="text"],
    ::slotted(eds-icon),
    ::slotted(svg) {
      color: var(--eds-tab-text-selected-color);
    }
  }

  .tabs {
    display: flex;
    align-items: baseline;
    overflow: auto;
  }

  .tabs::-webkit-scrollbar {
    display: var(--eds-tabs-scrollbar-display);
  }

  .tabs.block {
    --eds-tab-block: 100%;

    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(0, 1fr));
  }

  eds-tab-panel[hidden] {
    display: none;
  }
  
  .tab-panels {
    width: 100%;
    padding: var(--eds-tabs-panel-y-padding) var(--eds-tabs-panel-x-padding);
  }

  

  ::slotted(eds-icon),
  ::slotted(svg) {
    width: var(--eds-tabs-icon-size);
    height: var(--eds-tabs-icon-size);
    flex-shrink: 0;
  }

  :host(eds-tab[isDisabled]) ::slotted(eds-icon),
  :host(eds-tab[isDisabled]) ::slotted(svg),
  :host(eds-tab[isDisabled]) [part="text"] {
    color: var(--eds-colors-text-disabled);
  }
`;