import { LitElement, html, type TemplateResult, type CSSResultGroup } from 'lit';
import { customElement, property } from 'lit/decorators.js';
import { styles } from '../styles';
import { tabsStyle } from './tabs.style';

@customElement('eds-tab-panel')
export class TabPanel extends LitElement {
  static styles: CSSResultGroup = [styles, tabsStyle];

  @property({ type: String }) id = '';
  @property({ type: String }) tab = '';
  @property({ type: Boolean, reflect: true }) hidden = true;

  constructor() {
    super();
    // Generate a unique ID if none provided
    if (!this.id) {
      this.id = `panel-${Math.random().toString(36).substring(2, 11)}`;
    }
  }

  protected render = (): TemplateResult => {
    return html`
      <div 
        part="base"
        class="tab-panel" 
        id="${this.id}" 
        aria-labelledby="${this.tab}"
        role="tabpanel"
      >
        <slot></slot>
      </div>
    `;
  }
}

declare global {
  interface HTMLElementTagNameMap {
    'eds-tab-panel': TabPanel;
  }
}