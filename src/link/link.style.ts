import { css } from 'lit';

export const linkStyles = css`
  :host {
    --eds-link-display: var(--link-display, flex);
    --eds-link-align-items: var(--link-align-items, center);
    --eds-link-justify-content: var(--link-justify-content, center);
    --eds-link-gap: var(--link-gap, var(--eds-spacing-200));
    --eds-link-font-size: var(--link-font-size, inherit);
    --eds-link-height: var(--link-height, unset);
    --eds-link-padding: var(--link-padding, 0);
    --eds-link-text-color: var(--link-text-color, var(--eds-colors-link));
    --eds-link-decoration: var(--link-decoration, var(--eds-font-decoration-underline));

    --eds-link-hover-text-color: var(--link-hover-text-color, var(--eds-colors-link-hover));
    --eds-link-hover-decoration: var(--link-hover-decoration, none);

    --eds-link-disabled-text-color: var(--link-disabled-text-color, var(--eds-colors-text-disabled));

    font-size: var(--eds-link-font-size);
  }

  [part="base"] {
    display: var(--eds-link-display);
    align-items: var(--eds-link-align-items);
    justify-content: var(--eds-link-justify-content);
    gap: var(--eds-link-gap);
    padding: var(--eds-link-padding);
    color: var(--eds-link-text-color);
    text-decoration: var(--eds-link-decoration);
    cursor: pointer;
  }

  [part="base"]:hover {
    color: var(--eds-link-hover-text-color);
    text-decoration: var(--eds-link-hover-decoration);
  }

  :host([isDisabled]) [part="base"] {
    color: var(--eds-link-disabled-text-color);
    pointer-events: none;
  }
`;
