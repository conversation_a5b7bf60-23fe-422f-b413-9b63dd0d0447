import { LitElement, html, type TemplateResult, type CSSResultGroup } from 'lit';
import { property, query } from 'lit/decorators.js';
import { styles } from '../styles';
import { linkStyles } from './link.style';

class Link extends LitElement {
  static styles: CSSResultGroup = [styles, linkStyles];

  @property({ type: String }) href = '';
  @property({ type: String }) target: HTMLAnchorElement['target'] = '_self';
  @property({ type: Boolean, attribute: 'disabled', reflect: true }) isDisabled = false;

  @query('a') link!: HTMLAnchorElement;

  protected render = (): TemplateResult => {
    return html `
      <a part="base"
        href="${this.href}"
        target="${this.target}"
        ?disabled=${this.isDisabled}
      >
        <slot part="label"></slot>
      </a>
    `
  }
}

if (!customElements.get('eds-link')) {
  customElements.define('eds-link', Link);
}

declare global {
  interface HTMLElementTagNameMap {
    "eds-link": Link;
  }
}

export { Link };