import { LitElement, html, type TemplateResult, type CSSResultGroup, type PropertyValues } from 'lit';
import { property, state, query } from 'lit/decorators.js';
import { classMap } from 'lit/directives/class-map.js';
import { styles } from '../styles';
import { tooltipStyles } from './tooltip.style';
import { TooltipPlacementValues, TooltipTriggerValues, TooltipPosition, TooltipBounds } from './types';

class Tooltip extends LitElement {
  static styles: CSSResultGroup = [styles, tooltipStyles];

  @property({ type: String }) content = '';
  @property({ type: String }) placement: TooltipPlacementValues = 'top';
  @property({ type: Number }) delay = 100;
  @property({ type: Number }) hideDelay = 0;
  @property({ type: Boolean }) disabled = false;
  @property({ type: Boolean }) arrow = true;
  @property({ type: String }) trigger: TooltipTriggerValues = 'hover';
  @property({ type: Number }) offset = 8;

  @state() private isVisible = false;
  @state() private triggerElement: HTMLElement | null = null;
  @state() private computedPlacement: TooltipPlacementValues = 'top';

  @query('[part="tooltip"]') tooltipElement!: HTMLDivElement;
  @query('[part="arrow"]') arrowElement!: HTMLDivElement;
  @query('slot[name="trigger"]') triggerSlot!: HTMLSlotElement;

  private showTimeout: number | null = null;
  private hideTimeout: number | null = null;

  connectedCallback(): void {
    super.connectedCallback();
    this.setAttribute('role', 'tooltip');
  }

  disconnectedCallback(): void {
    super.disconnectedCallback();
    this._cleanupEventListeners();
    this._clearTimeouts();
  }

  firstUpdated(): void {
    this._setupTriggerElement();
    
    // Listen for slot changes
    this.triggerSlot?.addEventListener('slotchange', () => {
      this._setupTriggerElement();
    });
  }

  updated(changedProperties: PropertyValues): void {
    super.updated(changedProperties);
    
    // Re-setup trigger element if relevant properties changed
    if (changedProperties.has('disabled') || changedProperties.has('trigger')) {
      this._setupTriggerElement();
    }
  }

  private _setupTriggerElement(): void {
    this._cleanupEventListeners();
    
    // Get the slotted trigger element
    const slottedElements = this.triggerSlot?.assignedElements();
    this.triggerElement = slottedElements?.[0] as HTMLElement || null;
    
    if (this.triggerElement && !this.disabled) {
      this._addEventListeners();
      
      // Set up accessibility
      const tooltipId = this._generateId();
      this.id = tooltipId;
      this.triggerElement.setAttribute('aria-describedby', tooltipId);
    }
  }

  private _addEventListeners(): void {
    if (!this.triggerElement) return;

    if (this.trigger === 'hover' || this.trigger === 'both') {
      this.triggerElement.addEventListener('mouseenter', this._handleMouseEnter);
      this.triggerElement.addEventListener('mouseleave', this._handleMouseLeave);
      this.addEventListener('mouseenter', this._handleTooltipMouseEnter);
      this.addEventListener('mouseleave', this._handleTooltipMouseLeave);
    }

    if (this.trigger === 'focus' || this.trigger === 'both') {
      this.triggerElement.addEventListener('focusin', this._handleFocusIn);
      this.triggerElement.addEventListener('focusout', this._handleFocusOut);
    }

    if (this.trigger === 'click') {
      this.triggerElement.addEventListener('click', this._handleClick);
      document.addEventListener('click', this._handleDocumentClick);
    }
  }

  private _cleanupEventListeners(): void {
    if (!this.triggerElement) return;

    this.triggerElement.removeEventListener('mouseenter', this._handleMouseEnter);
    this.triggerElement.removeEventListener('mouseleave', this._handleMouseLeave);
    this.triggerElement.removeEventListener('focusin', this._handleFocusIn);
    this.triggerElement.removeEventListener('focusout', this._handleFocusOut);
    this.triggerElement.removeEventListener('click', this._handleClick);
    this.removeEventListener('mouseenter', this._handleTooltipMouseEnter);
    this.removeEventListener('mouseleave', this._handleTooltipMouseLeave);
    document.removeEventListener('click', this._handleDocumentClick);
  }

  private _handleMouseEnter = (): void => {
    this._clearTimeouts();
    this.showTimeout = window.setTimeout(() => {
      this._show();
    }, this.delay);
  };

  private _handleMouseLeave = (): void => {
    this._clearTimeouts();
    this.hideTimeout = window.setTimeout(() => {
      this._hide();
    }, this.hideDelay);
  };

  private _handleTooltipMouseEnter = (): void => {
    this._clearTimeouts();
  };

  private _handleTooltipMouseLeave = (): void => {
    this._clearTimeouts();
    this.hideTimeout = window.setTimeout(() => {
      this._hide();
    }, this.hideDelay);
  };

  private _handleFocusIn = (): void => {
    this._clearTimeouts();
    this._show();
  };

  private _handleFocusOut = (): void => {
    this._clearTimeouts();
    this._hide();
  };

  private _handleClick = (e: Event): void => {
    e.stopPropagation();
    this.isVisible ? this._hide() : this._show();
  };

  private _handleDocumentClick = (e: Event): void => {
    if (!this.contains(e.target as Node) && !this.triggerElement?.contains(e.target as Node)) {
      this._hide();
    }
  };

  private _show(): void {
    if (this.disabled || this.isVisible) return;
    
    this.isVisible = true;
    
    // Always calculate optimal position with fallback
    this._calculateOptimalPosition();
    
    this.dispatchEvent(new CustomEvent('tooltip-show', {
      bubbles: true,
      composed: true
    }));
  }

  private _hide(): void {
    if (!this.isVisible) return;
    
    this.isVisible = false;
    
    this.dispatchEvent(new CustomEvent('tooltip-hide', {
      bubbles: true,
      composed: true
    }));
  }

  private _clearTimeouts(): void {
    if (this.showTimeout) {
      clearTimeout(this.showTimeout);
      this.showTimeout = null;
    }
    if (this.hideTimeout) {
      clearTimeout(this.hideTimeout);
      this.hideTimeout = null;
    }
  }

  private _generateId(): string {
    return `tooltip-${Math.random().toString(36).substr(2, 9)}`;
  }

  private _calculateOptimalPosition(): void {
    if (!this.triggerElement || !this.tooltipElement) return;

    // Force tooltip to be visible but transparent for measurement
    this.tooltipElement.style.visibility = 'hidden';
    this.tooltipElement.style.opacity = '0';
    this.tooltipElement.style.display = 'block';

    // Get all possible positions and their fit status
    const positions = this._getAllPositionData();
    
    // Reset styles
    this.tooltipElement.style.visibility = '';
    this.tooltipElement.style.opacity = '';
    this.tooltipElement.style.display = '';

    // First, try the preferred placement
    const preferredPosition = positions.find(pos => pos.placement === this.placement);
    
    if (preferredPosition && preferredPosition.canFit) {
      // Use preferred placement if it fits
      this.computedPlacement = preferredPosition.placement;
    } else {
      // Fall back to the best available position
      // Priority order for fallback positioning
      const fallbackOrder: TooltipPlacementValues[] = ['top', 'bottom', 'right', 'left'];
      
      // Find the first position that fits
      let bestPosition = positions.find(pos => pos.canFit && fallbackOrder.includes(pos.placement));
      
      // If no position fits perfectly, choose the one with least spillover
      if (!bestPosition) {
        bestPosition = positions.reduce((best, current) => {
          const bestSpillover = Object.values(best.spillover).reduce((sum, val) => sum + Math.max(0, val), 0);
          const currentSpillover = Object.values(current.spillover).reduce((sum, val) => sum + Math.max(0, val), 0);
          return currentSpillover < bestSpillover ? current : best;
        });
      }

      this.computedPlacement = bestPosition.placement;
    }
  }

  private _getAllPositionData(): TooltipPosition[] {
    const positions: TooltipPlacementValues[] = ['top', 'bottom', 'left', 'right'];
    return positions.map(placement => this._getPositionData(placement));
  }

  private _getPositionData(placement: TooltipPlacementValues): TooltipPosition {
    if (!this.triggerElement || !this.tooltipElement) {
      return {
        placement,
        canFit: false,
        spillover: { top: 0, bottom: 0, left: 0, right: 0 }
      };
    }

    const triggerRect = this.triggerElement.getBoundingClientRect();
    const tooltipRect = this.tooltipElement.getBoundingClientRect();
    const viewport = {
      width: window.innerWidth,
      height: window.innerHeight,
      top: 0,
      left: 0
    };

    const arrowSize = this.arrow ? 6 : 0; // Arrow size from CSS
    const totalOffset = this.offset + arrowSize;

    // Calculate tooltip position based on placement
    let tooltipBounds: TooltipBounds;

    switch (placement) {
      case 'top':
        tooltipBounds = {
          left: triggerRect.left + (triggerRect.width - tooltipRect.width) / 2,
          top: triggerRect.top - tooltipRect.height - totalOffset,
          width: tooltipRect.width,
          height: tooltipRect.height,
          right: 0,
          bottom: 0
        };
        break;
      case 'bottom':
        tooltipBounds = {
          left: triggerRect.left + (triggerRect.width - tooltipRect.width) / 2,
          top: triggerRect.bottom + totalOffset,
          width: tooltipRect.width,
          height: tooltipRect.height,
          right: 0,
          bottom: 0
        };
        break;
      case 'left':
        tooltipBounds = {
          left: triggerRect.left - tooltipRect.width - totalOffset,
          top: triggerRect.top + (triggerRect.height - tooltipRect.height) / 2,
          width: tooltipRect.width,
          height: tooltipRect.height,
          right: 0,
          bottom: 0
        };
        break;
      case 'right':
        tooltipBounds = {
          left: triggerRect.right + totalOffset,
          top: triggerRect.top + (triggerRect.height - tooltipRect.height) / 2,
          width: tooltipRect.width,
          height: tooltipRect.height,
          right: 0,
          bottom: 0
        };
        break;
      default:
        tooltipBounds = {
          left: 0,
          top: 0,
          width: tooltipRect.width,
          height: tooltipRect.height,
          right: 0,
          bottom: 0
        };
    }

    tooltipBounds.right = tooltipBounds.left + tooltipBounds.width;
    tooltipBounds.bottom = tooltipBounds.top + tooltipBounds.height;

    // Calculate spillover
    const spillover = {
      top: Math.max(0, viewport.top - tooltipBounds.top),
      bottom: Math.max(0, tooltipBounds.bottom - viewport.height),
      left: Math.max(0, viewport.left - tooltipBounds.left),
      right: Math.max(0, tooltipBounds.right - viewport.width)
    };

    const canFit = Object.values(spillover).every(value => value === 0);

    return {
      placement,
      canFit,
      spillover
    };
  }

  protected render(): TemplateResult {
    if (!this.content && !this.querySelector('[slot="content"]')) {
      return html``;
    }

    return html`
      <div part="base">
        <slot name="trigger"></slot>
        <div 
          part="tooltip" 
          class=${classMap({
            'tooltip': true,
            'visible': this.isVisible,
            [`placement-${this.computedPlacement}`]: true,
            'has-arrow': this.arrow
          })}
          role="tooltip"
          aria-hidden=${!this.isVisible}
        >
          <div part="content" class="content">
            ${this.content ? this.content : html`<slot name="content"></slot>`}
          </div>
          ${this.arrow ? html`<div part="arrow" class="arrow"></div>` : ''}
        </div>
      </div>
    `;
  }
}

if (!customElements.get('eds-tooltip')) {
  customElements.define('eds-tooltip', Tooltip);
}

declare global {
  interface HTMLElementTagNameMap {
    "eds-tooltip": Tooltip;
  }
}

export { Tooltip }; 