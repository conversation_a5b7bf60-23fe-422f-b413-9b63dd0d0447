export type TooltipPlacementValues = 'top' | 'bottom' | 'left' | 'right';
export type TooltipTriggerValues = 'hover' | 'focus' | 'click' | 'both';

export interface TooltipShowEventDetail {
  target: HTMLElement;
}

export interface TooltipHideEventDetail {
  target: HTMLElement;
}

export interface TooltipPosition {
  placement: TooltipPlacementValues;
  canFit: boolean;
  spillover: {
    top: number;
    bottom: number;
    left: number;
    right: number;
  };
}

export interface TooltipBounds {
  top: number;
  left: number;
  right: number;
  bottom: number;
  width: number;
  height: number;
} 