import { css } from 'lit';

export const tooltipStyles = css`
  :host {
    /* Base tooltip styles */
    --eds-tooltip-background-color: var(--tooltip-background-color, var(--eds-colors-surface-level-1));
    --eds-tooltip-text-color: var(--tooltip-text-color, var(--eds-colors-text-light));
    --eds-tooltip-border-radius: var(--tooltip-border-radius, var(--eds-radius-200));
    --eds-tooltip-padding: var(--tooltip-padding, var(--eds-spacing-300) var(--eds-spacing-400));
    --eds-tooltip-width: var(--tooltip-width, clamp(120px, 100dvw, calc(100% + 32px)));
    --eds-tooltip-z-index: var(--tooltip-z-index, 1000);
    --eds-tooltip-box-shadow: var(--tooltip-box-shadow, var(--eds-shadow-md));

    /* Arrow styles */
    --eds-tooltip-arrow-size: var(--tooltip-arrow-size, var(--eds-sizing-300));
    --eds-tooltip-arrow-offset: var(--tooltip-arrow-offset, calc(var(--eds-tooltip-arrow-size) + 4px));

    /* Animation styles */
    --eds-tooltip-transition: var(
      --tooltip-transition,
      opacity var(--eds-transition-duration-base) var(--eds-transition-timing-function-base),
      transform var(--eds-transition-duration-base) var(--eds-transition-timing-function-base),
      visibility var(--eds-transition-duration-base) var(--eds-transition-timing-function-base)
    );

    /* States */
    --eds-tooltip-opacity-visible: var(--tooltip-opacity-visible, 1);
    --eds-tooltip-opacity-hidden: var(--tooltip-opacity-hidden, 0);

    position: relative;
    display: inline-block;
  }

  [part='base'] {
    position: relative;
    display: grid;
    place-items: center;
  }

  [part='tooltip'] {
    display: none;
    position: absolute;
    background-color: var(--eds-tooltip-background-color);
    color: var(--eds-tooltip-text-color);
    border: 1px solid var(--eds-border-color-default);
    border-radius: var(--eds-tooltip-border-radius);
    padding: var(--eds-tooltip-padding);
    font-size: var(--eds-tooltip-font-size);
    font-weight: var(--eds-tooltip-font-weight);
    line-height: var(--eds-tooltip-line-height);
    z-index: var(--eds-tooltip-z-index);
    box-shadow: var(--eds-tooltip-box-shadow);
    word-wrap: break-word;
    white-space: normal;
    pointer-events: none;
    width: clamp(120px, 100dvw, calc(100% + 32px));

    /* Initial hidden state */
    opacity: var(--eds-tooltip-opacity-hidden);
    visibility: hidden;
    transform: scale(0.8);
    transition: var(--eds-tooltip-transition);
  }

  [part='tooltip'].visible {
    display: block;
    opacity: var(--eds-tooltip-opacity-visible);
    visibility: visible;
    transform: scale(1);
  }

  [part='content'] {
    display: block;
    pointer-events: none;
  }

  /* Arrow positioning */
  [part='arrow'] {
    position: absolute;
    width: 0;
    height: 0;
    border: 1px solid var(--eds-border-color-default);
    border-radius: var(--eds-radius-050);
    pointer-events: none;
    width: var(--eds-sizing-300);
    height: var(--eds-sizing-300);
    background-color: var(--eds-tooltip-background-color);
    z-index: 1;
  }

  /* Top placement */
  .placement-top {
    bottom: 100%;
  }

  .placement-top.visible {
    transform: scale(1) translateY(calc(var(--eds-tooltip-arrow-offset) * -1));
  }

  .placement-top.has-arrow [part='arrow'] {
    top: 100%;
    left: 50%;
    transform: translate(-50%, -50%) rotate(45deg);
    border-top: none;
    border-left: none;
  }

  /* Bottom placement */
  .placement-bottom {
    transform-origin: top center;
    top: 100%;
  }

  .placement-bottom.visible {
    transform: scale(1) translateY(var(--eds-tooltip-arrow-offset));
  }

  .placement-bottom.has-arrow [part='arrow'] {
    bottom: 100%;
    left: 50%;
    transform: translate(-50%, 50%) rotate(45deg);
    border-bottom: none;
    border-right: none;
  }

  /* Left placement */
  .placement-left {
    transform-origin: center right;
    right: 100%;
  }

  .placement-left.visible {
    transform: scale(1) translateX(calc(var(--eds-tooltip-arrow-offset) * -1));
  }

  .placement-left.has-arrow [part='arrow'] {
    top: 50%;
    left: 100%;
    transform: translate(-50%, -50%) rotate(45deg);
    border-bottom: none;
    border-left: none;
  }

  /* Right placement */
  .placement-right {
    transform-origin: center left;
    left: 100%;
  }

  .placement-right.visible {
    transform: scale(1) translateX(var(--eds-tooltip-arrow-offset));
  }

  .placement-right.has-arrow [part='arrow'] {
    top: 50%;
    right: 100%;
    transform: translate(50%, -50%) rotate(45deg);
    border-top: none;
    border-right: none;
  }

  /* Hover state enhancement */
  .tooltip:hover {
    pointer-events: auto;
  }

  /* Animation enhancement for mobile */
  @media (hover: none) {
    [part='tooltip'] {
      transition-duration: calc(var(--eds-transition-duration-base) * 0.7);
    }
  }

  /* High contrast mode support */
  @media (prefers-contrast: high) {
    [part='tooltip'] {
      border: var(--eds-stroke-025) solid var(--eds-border-color-default);
    }
  }

  /* Reduced motion support */
  @media (prefers-reduced-motion: reduce) {
    [part='tooltip'] {
      transition: none;
    }

    .placement-top.visible,
    .placement-bottom.visible,
    .placement-left.visible,
    .placement-right.visible {
      transform: scale(1);
    }
  }

  /* Theme variants */
  :host([theme='dark']) {
    --eds-tooltip-background-color: var(--eds-colors-text-dark);
    --eds-tooltip-text-color: var(--eds-colors-text-white);
  }

  :host([theme='light']) {
    --eds-tooltip-background-color: var(--eds-colors-surface-default);
    --eds-tooltip-text-color: var(--eds-colors-text-dark);
    --eds-tooltip-box-shadow: var(--eds-shadow-md);
  }

  :host([theme='info']) {
    --eds-tooltip-background-color: var(--eds-colors-info-default);
    --eds-tooltip-text-color: var(--eds-colors-text-white);
  }

  :host([theme='success']) {
    --eds-tooltip-background-color: var(--eds-colors-success-default);
    --eds-tooltip-text-color: var(--eds-colors-text-white);
  }

  :host([theme='warning']) {
    --eds-tooltip-background-color: var(--eds-colors-warning-default);
    --eds-tooltip-text-color: var(--eds-colors-text-white);
  }

  :host([theme='danger']) {
    --eds-tooltip-background-color: var(--eds-colors-danger-default);
    --eds-tooltip-text-color: var(--eds-colors-text-white);
  }
`; 