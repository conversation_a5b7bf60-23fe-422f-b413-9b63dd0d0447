import { css } from 'lit';

export const checkboxGroupStyles = css`
  :host {
    --eds-checkbox-gap: var(--checkbox-gap, var(--eds-spacing-200));
    --eds-checkbox-wrapper-font-size: var(--checkbox-wrapper-font-size, var(--eds-colors-text-default));
    --eds-checkbox-wrapper-line-height: var(--checkbox-wrapper-line-height, var(--eds-line-height-heading-md));
  }
  
  [part=items] {
    display: flex;
    flex-direction: column;
    gap: var(--eds-checkbox-gap);
    font-size: var(--eds-checkbox-wrapper-font-size);
    line-height: var(--eds-checkbox-wrapper-line-height);
  }
`;