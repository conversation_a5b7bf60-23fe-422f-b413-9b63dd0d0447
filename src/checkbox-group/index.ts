import {LitElement, html, type TemplateResult, type CSSResultGroup, PropertyValues} from 'lit';
import { property, queryAssignedElements } from 'lit/decorators.js';
import { styles } from '../styles';
import { checkboxGroupStyles } from './checkbox-group.style';
import "../checkbox";
import { type Checkbox } from "../checkbox";

class CheckboxGroup extends LitElement {
  static styles: CSSResultGroup = [styles, checkboxGroupStyles];
  
  @property({ type: String }) name = 'checkbox';
  @property({ type: Boolean, reflect: true}) isRequired = false;
  @property({ type: Boolean, reflect: true}) isInvalid = false;
  @queryAssignedElements({ selector: 'eds-checkbox' }) checkboxElements!: Checkbox[];
  
  protected firstUpdated() {
    this.checkboxElements.forEach(item => {
      item.name = this.name;
    })
  }
  
  protected render = (): TemplateResult => {
    return html`
      <div part="base">
        <slot part="items">
        </slot>
      </div>`
  }
}

if (!customElements.get('eds-checkbox-group')) {
  customElements.define('eds-checkbox-group', CheckboxGroup);
}

declare global {
  interface HTMLElementTagNameMap {
    "eds-checkbox-group":CheckboxGroup;
  }
}

export {CheckboxGroup};