import { LitElement, html, type TemplateResult, type CSSResultGroup } from 'lit';
import { property } from 'lit/decorators.js';
import { styles } from '../styles';
import { actionItemStyle } from './action-item.style';
import "../text";
import "../icons";

class ActionItem extends LitElement {
  static styles: CSSResultGroup = [styles, actionItemStyle];
  @property({ type: String }) text = '';

  private handleClick = (): void => {
    this.dispatchEvent(new CustomEvent('action-click', {
      bubbles: true,
      composed: true,
      detail: { target: this, text: this.text }
    }));
  }

  protected render(): TemplateResult {
    return html`
      <button @click=${this.handleClick} part="base">
        <div part="label">
          <slot name="leading-icon"></slot>
          <eds-text as="p" text="${this.text}" size="lg" weight="medium"></eds-text>
        </div>
        <slot name="trailing-icon"></slot>
      </button>
    `;
  }
}

if (!customElements.get('eds-action-item')) {
  customElements.define('eds-action-item', ActionItem);
}

declare global {
  interface HTMLElementTagNameMap {
    'eds-action-item': ActionItem;
  }
}

export { ActionItem };
