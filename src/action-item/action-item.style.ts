import { css, type CSSResultGroup } from 'lit';

export const actionItemStyle: CSSResultGroup = css`
  :host {
    --eds-action-item-display: var(--action-item-display, flex);
    --eds-action-item-cursor: var(--action-item-cursor, pointer);
    --eds-action-item-align-items: var(--action-item-align-items, center);
    --eds-action-item-justify-content: var(--action-item-justify-content, space-between);
    --eds-action-item-gap: var(--action-item-gap, var(--eds-spacing-200));
    --eds-action-item-padding: var(--action-item-padding, var(--eds-spacing-300) var(--eds-spacing-600));
    --eds-action-item-border-radius: var(--action-item-border-radius, var(--eds-radius-300));
    --eds-action-item-border: var(--action-item-border, var(--eds-stroke-025) var(--eds-border-style-base) var(--eds-border-color-default));
    --eds-action-item-text-color: var(--action-item-text-color, var(--eds-colors-text-default));
    --eds-action-item-icon-leading-color: var(--action-item-icon-leading-color, var(--eds-colors-icon-default));
    --eds-action-item-icon-leading-size: var(--action-item-icon-leading-size, var(--eds-sizing-600));
    --eds-action-item-icon-trailing-color: var(--action-item-icon-trailing-color, var(--eds-colors-icon-light));
    --eds-action-item-icon-trailing-size: var(--action-item-icon-trailing-size, var(--eds-sizing-600));
    --eds-action-item-hover-background-color: var(--action-item-hover-background-color, var(--eds-colors-primary-lighter));
    --eds-action-item-active-background-color: var(--action-item-active-background-color, var(--eds-colors-primary-dark));
    --eds-action-item-active-text-color: var(--action-item-active-text-color, var(--eds-white));
    --eds-action-item-active-icon-color: var(--action-item-active-icon-color, var(--eds-white));
  }

  [part="base"] {
    display: var(--eds-action-item-display);
    align-items: var(--eds-action-item-align-items);
    justify-content: var(--eds-action-item-justify-content);
    gap: var(--eds-action-item-gap);
    padding: var(--eds-action-item-padding);
    border-radius: var(--eds-action-item-border-radius);
    border: var(--eds-action-item-border);
    width: 100%;
    cursor: var(--eds-action-item-cursor);

    &:hover {
      background-color: var(--eds-action-item-hover-background-color);
    }

    &:active {
      background-color: var(--eds-action-item-active-background-color);
      --eds-action-item-text-color: var(--eds-action-item-active-text-color);
      --eds-action-item-icon-leading-color: var(--eds-action-item-active-icon-color);
      --eds-action-item-icon-trailing-color: var(--eds-action-item-active-icon-color);
    }
  }

  [part="label"] {
    display: flex;
    align-items: center;
    gap: var(--eds-action-item-gap);

    eds-text {
      color: var(--eds-action-item-text-color);
    }
  }

  ::slotted([slot="leading-icon"]) {
    color: var(--eds-action-item-icon-leading-color);
    width: var(--eds-action-item-icon-leading-size);
    height: var(--eds-action-item-icon-leading-size);
    flex-shrink: 0;
  }

  ::slotted([slot="trailing-icon"]) {
    color: var(--eds-action-item-icon-trailing-color);
    width: var(--eds-action-item-icon-trailing-size);
    height: var(--eds-action-item-icon-trailing-size);
    flex-shrink: 0;
`;
