import { unsafeCSS } from "lit";
import { css } from "lit";

export const animationsStyle = css`
  @keyframes spin {
    from {
      transform: rotate(0deg);
    }

    to {
      transform: rotate(360deg);
    }
  }

  @keyframes indeterminate {
    from {
      left: -100%;
    }

    to {
      left: 100%;
    }
  }

  @keyframes circleIndeterminate {
    0% {
      transform: rotate(0deg);
    }

    50% {
      transform: rotate(300deg);
    }
  }

  @keyframes autoDismissSlider {
    from {
      right: 0;
    }

    to {
      right: 100%;
    }
  }
`;
