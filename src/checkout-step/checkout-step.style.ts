import { css } from "lit";

export const checkoutStepStyle = css`
  :host {
    --eds-checkout-step-border-properties: var(--checkout-step-border-properties, var(--eds-stroke-025) var(--eds-border-style-base) var(--eds-border-color-default));
    --eds-checkout-step-border-radius: var(--checkout-step-border-radius, var(--eds-radius-500));

    --eds-checkout-step-header-background-color: var(--checkout-step-header-background-color, var(--eds-colors-surface-level-1));
    --eds-checkout-step-header-padding: var(--checkout-step-header-padding, var(--eds-spacing-400) var(--eds-spacing-600));
    --eds-checkout-step-header-gap: var(--checkout-step-header-gap, var(--eds-spacing-300));

    --eds-checkout-step-title-gap: var(--checkout-step-title-gap, var(--eds-spacing-300));

    --eds-checkout-step-content-gap: var(--checkout-step-content-gap, var(--eds-spacing-400));
    --eds-checkout-step-content-border-top: var(--checkout-step-content-border-top, var(--eds-stroke-025) var(--eds-border-style-base) var(--eds-border-color-default));
    --eds-checkout-step-content-padding: var(--checkout-step-content-padding, var(--eds-spacing-600));

    --eds-checkout-step-completed-background-color: var(--checkout-step-completed-background-color, var(--eds-colors-surface-default));

    --eds-checkout-step-inactive-border-color: var(--checkout-step-inactive-border-color, var(--eds-border-color-default));
    --eds-checkout-step-inactive-background-color: var(--checkout-step-inactive-background-color, var(--eds-colors-surface-disabled));
    --eds-checkout-step-inactive-text-color: var(--checkout-step-inactive-text-color, var(--eds-colors-text-disabled));
    --eds-checkout-step-inactive-badge-border: var(--checkout-step-inactive-badge-border, var(--eds-stroke-025) var(--eds-border-style-base) var(--eds-border-color-default));
  }

  [part="base"] {
    display: flex;
    flex-direction: column;
    overflow: hidden;
    border: var(--eds-checkout-step-border-properties);
    border-radius: var(--eds-checkout-step-border-radius);
  }

  [part="header"] {
    display: flex;
    align-items: center;
    justify-content: space-between;
    gap: var(--eds-checkout-step-header-gap);
    padding: var(--eds-checkout-step-header-padding);
    background-color: var(--eds-checkout-step-header-background-color);
  }

  [part="title"] {
    display: flex;
    align-items: center;
    gap: var(--eds-checkout-step-title-gap);
  }

  [part="content"] {
    display: flex;
    flex-direction: column;
    gap: var(--eds-checkout-step-content-gap);
    border-top: var(--eds-checkout-step-content-border-top);
    padding: var(--eds-checkout-step-content-padding);
  }

  .completed [part="header"] {
    background-color: var(--eds-checkout-step-completed-background-color);
  }

  .inactive {
    border-color: var(--eds-checkout-step-inactive-border-color);
  }

  .inactive [part="header"] {
    background-color: var(--eds-checkout-step-inactive-background-color);
  }

  .inactive [part="header"] eds-heading {
    color: var(--eds-checkout-step-inactive-text-color);
  }

  .inactive [part="header"] eds-badge::part(base) {
    border: var(--eds-checkout-step-inactive-badge-border);
  }
`;
