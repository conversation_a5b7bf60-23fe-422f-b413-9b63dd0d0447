import { LitElement, html, type CSSResultGroup } from 'lit';
import { property } from 'lit/decorators.js';
import { CheckoutStepType } from './types';
import { styles } from '../styles';
import { checkoutStepStyle } from './checkout-step.style';
import '../badge';
import '../heading';

class CheckoutStep extends LitElement {
  static styles: CSSResultGroup = [styles, checkoutStepStyle];

  @property({ type: String, reflect: true }) type: CheckoutStepType = "active";
  @property({ type: String }) number = "1";
  @property({ type: String }) label = "Step 1";
  @property({ type: Boolean, reflect: true }) readyToComplete = false;

  render() {
    return html`
      <div part="base" class=${this.type}>
        <div part="header">
          <div part="title">
            ${this.type === 'completed' ? html`
              <eds-icon name="checkmarkCircle"></eds-icon>
            `: html`<eds-badge type="circle" size="medium" appearance="${this.type === 'inactive' ? 'opacity' : 'primary'}" label=${this.number}></eds-badge>`}
            <eds-heading as="h3" size="md" text=${this.label}></eds-heading>
          </div>
          ${this.type === 'completed' ? html`
            <eds-link @link-click=${this._onEdit} href="#">Edit</eds-link>
          `: ''}
        </div>
        ${this.type === 'active' ? html`
          <div part="content">
            <slot></slot>
            ${this.readyToComplete ? html`
              <eds-button type="button" appearance="primary" shouldFitContainer @button-click=${this._onComplete}>Complete</eds-button>
            `: ''}
          </div>`: ''
        }
      </div>
    `;
  }

  private _onEdit() {
    this.type = 'active';
  }

  private _onComplete() {
    this.type = 'completed';
  }
}

if (!customElements.get('eds-checkout-step')) {
  customElements.define('eds-checkout-step', CheckoutStep);
}

export { CheckoutStep };
