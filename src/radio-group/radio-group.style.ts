import { css } from 'lit';

export const radioGroupStyles = css`
  :host {
    --eds-radio-gap: var(--radio-gap, var(--eds-spacing-200));
    --eds-radio-wrapper-font-size: var(--radio-wrapper-font-size, var(--eds-colors-text-default));
    --eds-radio-wrapper-line-height: var(--radio-wrapper-line-height, var(--eds-line-height-heading-md));
  }
  
  [part=items] {
    display: flex;
    flex-direction: column;
    gap: var(--eds-radio-gap);
    font-size: var(--eds-radio-wrapper-font-size);
    line-height: var(--eds-radio-wrapper-line-height);
  }
`;
