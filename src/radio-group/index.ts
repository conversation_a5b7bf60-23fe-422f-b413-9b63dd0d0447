import {LitElement, html, type TemplateResult, type CSSResultGroup, PropertyValues} from 'lit';
import { property, query, queryAssignedElements } from 'lit/decorators.js';
import { styles } from '../styles';
import { radioGroupStyles } from './radio-group.style';
import "../radio";
import {type Radio} from "../radio";

class RadioGroup extends LitElement {
  static styles: CSSResultGroup = [styles, radioGroupStyles];

  @property({ type: String }) name = 'radio';
  @property({ type: Boolean, reflect: true }) isRequired = false;
  @property({ type: Boolean, reflect: true }) isInvalid = false;
  @property({ type: String }) value: string | null = null;
  @queryAssignedElements({ selector: 'eds-radio' }) radioElements!: Radio[];

  protected firstUpdated() {
    this.setupRadioElements();
  }

  updated(changedProperties: PropertyValues) {
    if (changedProperties.has('name') || changedProperties.has('isRequired') || changedProperties.has('isInvalid')) {
      this.setupRadioElements();
    }
    
    if (changedProperties.has('value')) {
      this.updateCheckedState();
    }
  }

  private setupRadioElements() {
    this.radioElements.forEach(item => {
      item.name = this.name;
      item.isRequired = this.isRequired;
      item.isInvalid = this.isInvalid;
    });
  }

  private updateCheckedState() {
    if (this.radioElements && this.value !== null) {
      this.radioElements.forEach(radio => {
        radio.isChecked = radio.value === this.value;
      });
    }
  }

  private _handleChange = (e: CustomEvent<{ target: Radio }>) => {
    const selectedRadio = e.detail.target;
    const input = selectedRadio.shadowRoot?.querySelector('input') as HTMLInputElement;
    
    this.value = input.value;
    
    this.radioElements.forEach(item => {
      if (item !== selectedRadio) {
        item.isChecked = false;
      }
    });

    this.dispatchEvent(new CustomEvent('value-change', {
      bubbles: true,
      composed: true,
      detail: {
        value: this.value,
        target: input,
      },
    }));
  };

  protected render = (): TemplateResult => {
    return html`
      <div part="base" @radio-change=${this._handleChange}>
        <slot part="items">
        </slot>
      </div>`;
  };
}

if (!customElements.get('eds-radio-group')) {
  customElements.define('eds-radio-group', RadioGroup);
}

declare global {
  interface HTMLElementTagNameMap {
    "eds-radio-group": RadioGroup;
  }
}

export { RadioGroup };
