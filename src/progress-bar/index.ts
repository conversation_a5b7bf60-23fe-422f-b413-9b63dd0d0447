import { LitElement, html, type TemplateResult, type CSSResultGroup } from 'lit';
import { property, query, state } from 'lit/decorators.js';
import { classMap } from 'lit/directives/class-map.js';
import { styles } from '../styles';
import { progressBarStyle } from './progress-bar.style';
import type { CircleProperties, ProgressBarTypeValues } from './types';
import '../text';

class ProgressBar extends LitElement {
  static styles: CSSResultGroup = [styles, progressBarStyle];

  @property({ type: String }) label = '';
  @property({ type: String }) helperText = '';
  @property({ type: Number }) value = 25;
  @property({ type: Number }) maxValue = 100;
  @property({ type: String }) valueType = '';
  @property({ type: Boolean }) isIndeterminate = false;
  @property({ type: Boolean }) isAnimated = false;
  @property({ type: Boolean }) isUnlimited = false;
  @property({ type: Boolean }) hideBar = false;
  @property({ type: String }) hiddenMessage = '';
  @property({ type: String }) type: ProgressBarTypeValues = 'bar';
  @property({ type: Object }) circleProperties: CircleProperties = {
    default: 0,
    breakpoints: {},
  };

  @query('[part="circle-indicator"]') private circleIndicator!: SVGPathElement;
  @query('[part="bar-indicator"]') private barIndicator!: HTMLDivElement;
  @state() private currentDiameter: number = 0;

  private readonly resizeObserver: ResizeObserver;

  constructor() {
    super();
    this.resizeObserver = new ResizeObserver(this._handleResize);
  }

  override connectedCallback(): void {
    super.connectedCallback();
    if (this.isUnlimited) this.value = this.maxValue;

    this.resizeObserver.observe(document.body);
    this.currentDiameter = this.circleProperties?.default ?? 0;
    if (this.circleProperties) {
      this._updateCircleDiameter();
    }
  }

  override disconnectedCallback(): void {
    super.disconnectedCallback();
    this.resizeObserver.unobserve(document.body);
  }

  private readonly _handleResize = (): void => {
    if (this.circleProperties) {
      this._updateCircleDiameter();
    }
  };

  private _updateCircleDiameter(): void {
    const windowWidth = window.innerWidth;
    const breakpointWidths = Object.keys(this.circleProperties.breakpoints)
      .map(Number)
      .sort((a, b) => a - b);

    if (!breakpointWidths.length || windowWidth < breakpointWidths[0]) {
      this.currentDiameter = this.circleProperties.default;
      return;
    }

    this.currentDiameter = breakpointWidths.reduce((acc, width) => {
      return windowWidth >= width ? this.circleProperties.breakpoints[width] ?? acc : acc;
    }, this.currentDiameter);
  }

  private _getPercentage(): number {
    const percentage = (this.value / this.maxValue) * 100;
    return Number.isInteger(percentage) ? percentage : Number(percentage.toFixed(1));
  }

  private _getDefaultSize(): number {
    return parseFloat(getComputedStyle(this).getPropertyValue('--eds-progress-bar-stroke-width').trim());
  }

  private get barThickness(): number {
    return this._getDefaultSize();
  }

  private get circleSize(): number {
    return this.currentDiameter;
  }

  private get radius(): number {
    return this.circleSize / 2 - this.barThickness * 16;
  }

  private get circumference(): number {
    return 2 * Math.PI * this.radius;
  }

  private get offset(): number {
    const percentage = this.isIndeterminate ? 20 : this._getPercentage();
    return this.circumference - (percentage / 100) * this.circumference;
  }

  protected override updated(): void {
    this._updateIndicator();
  }

  private _updateIndicator(): void {
    if (this.type === 'circle') {
      requestAnimationFrame(() => {
        this.circleIndicator.style.strokeDashoffset = `${this.circleIndicator.getAttribute('data-dashoffset')}px`;
      });
    } else if (this.type === 'bar' && this.barIndicator) {
      if (this.isIndeterminate) {
        this.barIndicator.style.width = '25%';
      } else {
        this.barIndicator.style.width = '0%';
        requestAnimationFrame(() => {
          this.barIndicator.style.width = `${this._getPercentage()}%`;
        });
      }
    }
  }

  private _renderUsage = (): TemplateResult => {
    return html`
      <div part="usage">
        ${this.isUnlimited
          ? html`<eds-text part="unlimited" as="p" size="sm" weight="regular" text="Unlimited"></eds-text>`
          : html`
              <eds-text part="value" as="p" size="lg" weight="medium" .text=${this.value}></eds-text>
              <eds-text
                part="maxValue"
                as="p"
                size="sm"
                weight="medium"
                .text=${`/ ${this.maxValue} ${this.valueType}`}
              >
              </eds-text>
            `}
      </div>
    `;
  };

  private _renderLabel = (): TemplateResult => {
    return html` <eds-text part="label" as="p" size="lg" weight="medium" .text=${this.label}></eds-text> `;
  };

  private _renderHelperText = (): TemplateResult => {
    return html` <eds-text part="helperText" as="p" size="sm" weight="regular" .text=${this.helperText}></eds-text> `;
  };

  private _renderHiddenText = (): TemplateResult => {
    return html` <eds-text part="helperText" as="p" size="sm" weight="regular" .text=${this.hiddenMessage}></eds-text> `;
  };

  private _renderBar = (): TemplateResult => {
    return html`
      <div
        part="progress-bar"
        role="progressbar"
        aria-valuemin="0"
        aria-valuemax="100"
        aria-valuenow=${this._getPercentage()}
      >
        <div part="info">${this.label ? this._renderLabel() : ''} ${this._renderUsage()}</div>
        ${this.hideBar ? this._renderHiddenText() : this._renderInnerBar()}
        ${this.helperText ? this._renderHelperText() : ''}
      </div>
    `;
  };

  private _renderInnerBar = (): TemplateResult => {
    return html`
      <div part="bar">
        <div
                part="bar-indicator"
                class=${classMap({
                  indeterminate: this.isIndeterminate,
                  animated: this.isAnimated,
                })}
                style=${this.isAnimated ? '' : `width: ${this.value}%;`}
        ></div>
      </div>
    `
  }

  private _renderCircle = (): TemplateResult => {
    return html`
      <div part="progress-circle">
        <div part="circle">
          <svg
            viewBox="0 0 ${this.circleSize} ${this.circleSize}"
            xmlns="http://www.w3.org/2000/svg"
            width=${this.circleSize}
            height=${this.circleSize}
          >
            <circle
              part="circle-background"
              cx=${this.circleSize / 2}
              cy=${this.circleSize / 2}
              r=${this.radius}
              fill="none"
            />
            <path
              part="circle-indicator"
              class=${classMap({
                indeterminate: this.isIndeterminate,
                animated: this.isAnimated,
              })}
              d=${`M ${this.circleSize / 2},${this.barThickness * 16}
                 A ${this.radius},${this.radius} 0 1,1 ${this.circleSize / 2},${
                this.circleSize - this.barThickness * 16
              }
                 A ${this.radius},${this.radius} 0 1,1 ${this.circleSize / 2},${this.barThickness * 16}`}
              fill="none"
              stroke-linecap="round"
              stroke-dasharray=${this.circumference}
              stroke-dashoffset=${this.isAnimated ? this.circumference : this.offset}
              data-dashoffset=${this.offset}
            />
          </svg>
          ${this._renderUsage()}
        </div>
        ${this.label ? this._renderLabel() : ''} ${this.helperText ? this._renderHelperText() : ''}
      </div>
    `;
  };

  protected override render(): TemplateResult {
    return this.type === 'circle' ? this._renderCircle() : this._renderBar();
  }
}

if (!customElements.get('eds-progress-bar')) {
  customElements.define('eds-progress-bar', ProgressBar);
}

declare global {
  interface HTMLElementTagNameMap {
    'eds-progress-bar': ProgressBar;
  }
}

export { ProgressBar };
