import { css, type CSSResultGroup } from "lit";

export const progressBarStyle: CSSResultGroup = css`
  :host {
    --eds-progress-bar-gap: var(--progress-bar-gap, var(--eds-spacing-200));
    --eds-progress-bar-info-align-items: var(--progress-bar-info-align-items, center);
    --eds-progress-bar-info-gap: var(--progress-bar-info-gap, var(--eds-spacing-400));
    --eds-progress-bar-usage-gap: var(--progress-bar-usage-gap, var(--eds-spacing-100));
    --eds-progress-bar-stroke-width: var(--progress-bar-stroke-width, .25rem);
    --eds-progress-bar-radius: var(--progress-bar-radius, var(--eds-radius-100));
    --eds-progress-bar-transition: var(--progress-bar-transition, width var(--eds-animation-duration-base) var(--eds-transition-timing-function-cubic-bezier-base));
    --eds-progress-bar-indeterminate-animation: var(--progress-bar-indeterminate-animation, indeterminate 2s infinite linear);
    --eds-progress-bar-circle-indeterminate-animation: var(--progress-bar-circle-indeterminate-animation, circleIndeterminate 2s infinite linear);
    --eds-progress-bar-background-color: var(--progress-bar-background-color, var(--eds-colors-surface-level-2));
    --eds-progress-bar-indicator-color: var(--progress-bar-indicator-color, var(--eds-colors-secondary-default));
    --eds-progress-bar-label-color: var(--progress-bar-label-color, var(--eds-colors-text-default));
    --eds-progress-bar-value-color: var(--progress-bar-value-color, var(--eds-colors-text-default));
    --eds-progress-bar-max-value-color: var(--progress-bar-max-value-color, var(--eds-colors-text-light));
    --eds-progress-bar-helper-text-color: var(--progress-bar-helper-text-color, var(--eds-colors-text-light));
    --eds-progress-bar-unlimited-text-color: var(--progress-bar-unlimited-text-color, var(--eds-colors-text-light));
  }

  [part="progress-bar"] {
    display: flex;
    flex-direction: column;
    gap: var(--eds-progress-bar-gap);
  }

  [part="progress-bar"] [part="info"] {
    display: flex;
    align-items: var(--eds-progress-bar-info-align-items);
    justify-content: end;
    gap: var(--eds-progress-bar-info-gap)
  }

  [part="progress-bar"] [part="info"]:has([part="label"]) {
    justify-content: space-between;
  }

  [part="progress-bar"] [part="usage"] {
    display: flex;
    align-items: center;
    gap: var(--eds-progress-bar-usage-gap);
    white-space: nowrap;
  }
  
  [part="label"] {
    --eds-text-color: var(--eds-progress-bar-label-color);
  }

  [part="value"] {
    --eds-text-color: var(--eds-progress-bar-value-color);
  }

  [part="maxValue"] {
    --eds-text-color: var(--eds-progress-bar-max-value-color);
  }

  [part="helperText"] {
    --eds-text-color: var(--eds-progress-bar-helper-text-color);
  }

  [part="unlimited"] {
    --eds-text-color: var(--eds-progress-bar-unlimited-text-color);
  }

  [part="bar"] {
    width: 100%;
    height: auto;
    min-height: var(--eds-progress-bar-stroke-width);
    border-radius: var(--eds-progress-bar-radius);
    background-color: var(--eds-progress-bar-background-color);
    overflow: hidden;
    position: relative;
  }

  [part="bar-indicator"] {
    height: 100%;
    min-height: var(--eds-progress-bar-stroke-width);
    border-radius: var(--eds-progress-bar-radius);
    background-color: var(--eds-progress-bar-indicator-color);
    position: absolute;
    bottom: 0;
    top: 0;
  }

  [part="bar-indicator"].animated {
    transition: var(--eds-progress-bar-transition);
  }

  [part="bar-indicator"].indeterminate {
    animation: var(--eds-progress-bar-indeterminate-animation);
  }
  
  [part="progress-circle"] {
    display: flex;
    flex-direction: column;
    gap: var(--eds-progress-bar-gap);
    align-items: center;
  }

  [part="circle"] {
    display: flex;
    position: relative;
    width: fit-content;
  }

  [part="circle"] [part="usage"] {
    display: flex;
    align-items: center;
    flex-direction: column;
    gap: var(--eds-progress-bar-usage-gap);
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
  }

  [part="circle-background"] {
    stroke: var(--eds-progress-bar-background-color);
    stroke-width: var(--eds-progress-bar-stroke-width);
  }

  [part="circle-indicator"] {
    stroke-width: var(--eds-progress-bar-stroke-width);
    transform-origin: center;
    stroke: var(--eds-progress-bar-indicator-color);
  }

  [part="circle-indicator"].animated {
    transition: stroke-dashoffset var(--eds-animation-duration-base) var(--eds-animation-timing-function-base);
  }

  [part="circle-indicator"].indeterminate {
    animation: var(--eds-progress-bar-circle-indeterminate-animation);
  }
`;