import { type CSSResultGroup, html, LitElement } from 'lit';
import { property } from 'lit/decorators.js';
import { classMap } from 'lit/directives/class-map.js';
import { BadgeType, BadgeAppearance, BadgeSize } from './types';
import { styles } from '../styles';;
import { badgeStyle } from "./badge.style";

class Badge extends LitElement {
  static styles: CSSResultGroup = [styles, badgeStyle];

  @property({ type: String }) label = "Badge";
  @property({ type: String }) type: BadgeType = "rounded";
  @property({ type: String }) appearance: BadgeAppearance = "primary";
  @property({ type: String }) size: BadgeSize = "medium";

  render() {
    return html`
      <div part="base" class="${classMap({
        [this.type]: true,
        [this.appearance]: true,
        [this.size]: true
      })}">
        <span part="label">${this.label}</span>
      </div>
    `;
  }
}

if (!customElements.get("eds-badge")) {
  customElements.define("eds-badge", Badge);
}

export { Badge };
