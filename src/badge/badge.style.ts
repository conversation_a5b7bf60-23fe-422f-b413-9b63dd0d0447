import { css } from "lit";

const badgeStyle = css`
  :host {
    --eds-badge-rounded-border-radius: var(--badge-rounded-border-radius, var(--eds-radius-100));

    --eds-badge-circle-border-radius: var(--badge-circle-border-radius, var(--eds-radius-full));

    --eds-badge-small-padding: var(--badge-padding, var(--eds-spacing-050));
    --eds-badge-small-width: var(--badge-small-width, var(--eds-sizing-400));
    --eds-badge-small-height: var(--badge-small-height, var(--eds-sizing-400));
    --eds-badge-small-font-size: var(--eds-badge-small-font-size, var(--eds-font-size-body-sm));
    --eds-badge-small-font-weight: var(--eds-badge-small-font-weight, var(--eds-font-weight-medium));

    --eds-badge-medium-padding: var(--badge-medium-padding, var(--eds-spacing-100));
    --eds-badge-medium-width: var(--badge-medium-width, var(--eds-spacing-600));
    --eds-badge-medium-height: var(--badge-medium-height, var(--eds-spacing-600));
    --eds-badge-medium-font-size: var(--eds-badge-medium-font-size, var(--eds-font-size-body-md));
    --eds-badge-medium-font-weight: var(--eds-badge-medium-font-weight, var(--eds-font-weight-medium));

    --eds-badge-primary-background-color: var(--badge-primary-background-color, var(--eds-colors-primary-default));
    --eds-badge-primary-text-color: var(--badge-primary-text-color, var(--eds-colors-text-white));

    --eds-badge-secondary-background-color: var(--badge-secondary-background-color, var(--eds-colors-secondary-default));
    --eds-badge-secondary-text-color: var(--badge-secondary-text-color, var(--eds-colors-text-white));

    --eds-badge-opacity-background-color: var(--badge-opacity-background-color, var(--eds-colors-surface-disabled));
    --eds-badge-opacity-text-color: var(--badge-opacity-text-color, var(--eds-colors-text-disabled));

    --eds-badge-stroke-background-color: var(--badge-stroke-background-color, transparent);
    --eds-badge-stroke-text-color: var(--badge-stroke-text-color, var(--eds-colors-text-default));
    --eds-badge-stroke-border: var(--badge-stroke-border, var(--eds-border-color-default) var(--eds-border-style-base) var(--eds-stroke-025));
  }


  [part="base"] {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    padding: var(--eds-badge-padding);
    border-radius: var(--eds-badge-border-radius);
    min-width: var(--eds-badge-width);
    height: var(--eds-badge-height);
    color: var(--eds-badge-text-color);
    background-color: var(--eds-badge-background-color);
    font-size: var(--eds-badge-font-size);
    font-weight: var(--eds-badge-font-weight);
  }

  [part="label"] {
    display: flex;
    align-items: center;
    justify-content: center;
    text-align: center;
    line-height: 1rem;
  }

  .rounded {
    --eds-badge-border-radius: var(--eds-badge-rounded-border-radius);
  }

  .circle {
    --eds-badge-border-radius: var(--eds-badge-circle-border-radius);
  }

  .small {
    --eds-badge-padding: var(--eds-badge-small-padding);
    --eds-badge-width: var(--eds-badge-small-width);
    --eds-badge-height: var(--eds-badge-small-height);
    --eds-badge-font-size: var(--eds-badge-small-font-size);
    --eds-badge-font-weight: var(--eds-badge-small-font-weight);
  }

  .medium {
    --eds-badge-padding: var(--eds-badge-medium-padding);
    --eds-badge-width: var(--eds-badge-medium-width);
    --eds-badge-height: var(--eds-badge-medium-height);
    --eds-badge-font-size: var(--eds-badge-medium-font-size);
    --eds-badge-font-weight: var(--eds-badge-medium-font-weight);
  }

  .primary {
    --eds-badge-background-color: var(--eds-badge-primary-background-color);
    --eds-badge-text-color: var(--eds-badge-primary-text-color);
  }

  .secondary {
    --eds-badge-background-color: var(--eds-badge-secondary-background-color);
    --eds-badge-text-color: var(--eds-badge-secondary-text-color);
  }

  .opacity {
    --eds-badge-background-color: var(--eds-badge-opacity-background-color);
    --eds-badge-text-color: var(--eds-badge-opacity-text-color);
  }

  .stroke {
    --eds-badge-background-color: var(--eds-badge-stroke-background-color);
    --eds-badge-text-color: var(--eds-badge-stroke-text-color);
    border: var(--eds-badge-stroke-border);
  }
`;

export { badgeStyle };
