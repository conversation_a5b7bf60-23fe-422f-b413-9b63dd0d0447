import { LitElement, html, type TemplateResult, type CSSResultGroup } from 'lit';
import { property, query } from 'lit/decorators.js';
import { styles } from '../styles';
import { checkboxStyles } from './checkbox.style';

class Checkbox extends LitElement {
  static styles: CSSResultGroup = [styles, checkboxStyles];

  @property({ type: String }) id = 'label1';
  @property({ type: String }) name = 'label1';
  @property({ type: String }) value = 'label1';
  @property({ type: String }) label = 'Label';
  @property({ type: Boolean, reflect: true }) checked = false;
  @property({ type: Boolean, reflect: true }) isDisabled = false;
  @property({ type: Boolean, reflect: true }) isInvalid = false;
  @property({ type: Boolean, reflect: true }) isRequired = false;

  @query('slot') slotElement!: HTMLSlotElement;

  protected firstUpdated() {
    this._addLabelListeners();
  }

  private _addLabelListeners = () => {
    const assignedNodes = this.slotElement?.assignedNodes({ flatten: true }) || [];
    assignedNodes.forEach((node) => {
      if (node instanceof HTMLLabelElement) {
        node.addEventListener('click', this._handleLabelClick);
      }
    });
  };

  private _handleLabelClick = (event: MouseEvent) => {
    const label = event.currentTarget as HTMLLabelElement;
    const forId = label.getAttribute('for');
    if (forId) {
      const input = this.shadowRoot?.querySelector(`#${forId}`) as HTMLInputElement;
      if (input) {
        input.click();
      }
    }
  };

  private _renderCheckbox = (): TemplateResult => {
    return html`
      <div part="checkbox">
        <input
          type="checkbox"
          part="checkbox-item"
          id="${this.id}"
          name="${this.name}"
          value="${this.value}"
          ?checked=${this.checked}
          ?disabled="${this.isDisabled}"
          ?required="${this.isRequired}"
          ?aria-invalid="${this.isInvalid}"
          @change="${this._onCheckboxChange}"
        />
        <div part="checkmark"></div>
      </div>
    `;
  };

  private _onCheckboxChange(event: Event) {
    const input = event.target as HTMLInputElement;
    this.checked = input.checked;
    this.dispatchEvent(new CustomEvent('change', { detail: this.checked }));
  }

  protected render = (): TemplateResult => {
    return html` <div part="base">
      ${this._renderCheckbox()}
      <div part="content">
        <slot></slot>
        ${this.isRequired ? html`<span part="required">*</span>` : ''}
      </div>
    </div>`;
  };
}

if (!customElements.get('eds-checkbox')) {
  customElements.define('eds-checkbox', Checkbox);
}

declare global {
  interface HTMLElementTagNameMap {
    'eds-checkbox': Checkbox;
  }
}

export { Checkbox };
