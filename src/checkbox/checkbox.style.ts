import { css } from 'lit';

export const checkboxStyles = css`
  :host {
    --eds-checkbox-gap: var(--checkbox-gap, var(--eds-spacing-200));
    --eds-checkbox-wrapper-font-size: var(--checkbox-wrapper-font-size, var(--eds-font-size-body-md));
    --eds-checkbox-wrapper-line-height: var(--checkbox-wrapper-line-height, var(--eds-line-height-body-md));
    --eds-checkbox-height: var(--checkbox-height, var(--eds-sizing-400));
    --eds-checkbox-width: var(--checkbox-width, var(--eds-sizing-400));
    --eds-checkbox-padding: var(--checkbox-padding, var(--eds-spacing-050));
    --eds-checkbox-text-color: var(--checkbox-text-color, var(--eds-colors-primary-default));
    --eds-checkbox-checkmark-border-properties: var(--checkbox-checkmark-border-properties, var(--eds-stroke-025) var(--eds-border-style-base) var(--eds-border-color-default));
    --eds-checkbox-checkmark-border-radius: var(--checkbox-checkmark-border-radius, var(--eds-radius-100));
    --eds-checkbox-checkmark-hover-background-color: var(--checkbox-checkmark-hover-background-color, var(--eds-colors-surface-level-2));
    --eds-checkbox-checkmark-hover-border-properties: var(--checkbox-checkmark-hover-border-properties, var(--eds-stroke-025) var(--eds-border-style-base) var(--eds-border-color-dark));
    --eds-checkbox-checkmark-checked-background-color: var(--checkbox-checkmark-checked-background-color, var(--eds-colors-primary-default));
    --eds-checkbox-checkmark-checked-background-image: var(--checkbox-checkmark-checked-background-image, url("data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMTIiIGhlaWdodD0iMTIiIHZpZXdCb3g9IjAgMCAxMiAxMiIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPHBhdGggZD0iTTIuNSA3TDQuMjUgOC43NUw5LjUgMy4yNSIgc3Ryb2tlPSJ3aGl0ZSIgc3Ryb2tlLXdpZHRoPSIxLjUiIHN0cm9rZS1saW5lY2FwPSJyb3VuZCIgc3Ryb2tlLWxpbmVqb2luPSJyb3VuZCIvPgo8L3N2Zz4K"));
    --eds-checkbox-checkmark-checked-border-properties: var(--checkbox-checkmark-checked-border-properties, var(--eds-stroke-025) var(--eds-border-style-base) var(--eds-colors-primary-dark));
    --eds-checkbox-checkmark-focus-box-shadow: var(--checkbox-checkmark-focus-box-shadow, --eds-ring-sm);
    --eds-checkbox-checkmark-focus-border-properties: var(--checkbox-checkmark-focus-border-properties, var(--eds-stroke-025) var(--eds-border-style-base) var(--eds-border-color-dark));
    --eds-checkbox-checkmark-disabled-background-color: var(--checkbox-checkmark-disabled-background-color, var(--eds-colors-surface-disabled));
    --eds-checkbox-checkmark-disabled-border-properties: var(--checkbox-checkmark-disabled-border-properties, var(--eds-stroke-025) var(--eds-border-style-base) var(--eds-border-color-default));
    --eds-checkbox-checkmark-disabled-background-image: var(--checkbox-checkmark-disabled-background-image, url("data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMTIiIGhlaWdodD0iMTIiIHZpZXdCb3g9IjAgMCAxMiAxMiIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPHBhdGggZD0iTTIuNSA3TDQuMjUgOC43NUw5LjUgMy4yNSIgc3Ryb2tlPSIjQjNCM0Q1IiBzdHJva2Utd2lkdGg9IjEuNSIgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIiBzdHJva2UtbGluZWpvaW49InJvdW5kIi8+Cjwvc3ZnPgo="));
    --eds-checkbox-checkmark-disabled-text-color: var(--checkbox-checkmark-disabled-text-color, var(--eds-colors-text-disabled));
    --eds-checkbox-checkmark-invalid-background-image: var(--checkbox-checkmark-invalid-background-image, url("data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMTIiIGhlaWdodD0iMTIiIHZpZXdCb3g9IjAgMCAxMiAxMiIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPHBhdGggZD0iTTIuNSA3TDQuMjUgOC43NUw5LjUgMy4yNSIgc3Ryb2tlPSIjQkIyRDIxIiBzdHJva2Utd2lkdGg9IjEuNSIgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIiBzdHJva2UtbGluZWpvaW49InJvdW5kIi8+Cjwvc3ZnPgo="));
    --eds-checkbox-checkmark-invalid-background-color: var(--checkbox-checkmark-invalid-background-color, var(--eds-colors-danger-lighter));
    --eds-checkbox-checkmark-invalid-border-properties: var(--checkbox-checkmark-invalid-border-properties, var(--eds-stroke-025) var(--eds-border-style-base) var(--eds-colors-danger-default));
    --eds-checkbox-checkmark-required-text-color: var(--checkbox-checkmark-required-text-color, var(--eds-colors-danger-default));
  }
  
  [part=base] {
    display: flex;
    gap: var(--eds-checkbox-gap);
    position: relative;
    font-size: var(--eds-checkbox-wrapper-font-size);
    line-height: var(--eds-checkbox-wrapper-line-height);
  }
  
  [part=checkbox] {
    display: grid;
    place-items: center;
    position: relative;
    cursor: pointer;
    height: var(--eds-checkbox-height);
    width: var(--eds-checkbox-width);
    padding: var(--eds-checkbox-padding);
    box-sizing: content-box;
  }
  
  [part=checkbox-item] {
    width: 100%;
    height: 100%;
    margin: 0;
    opacity: 0;
    z-index: 1;
    grid-area: 1 / 1 / 2 / 2;
    cursor: pointer;
  }
  
  [part=checkmark] {
    height: var(--eds-checkbox-height);
    width: var(--eds-checkbox-width);
    border: var(--eds-checkbox-checkmark-border-properties);
    border-radius: var(--eds-checkbox-checkmark-border-radius);
    grid-area: 1 / 1 / 2 / 2;
  }

  [part=content] {
      display: flex;
  }
  
  [part=base],
  ::slotted(*) {
    color: var(--eds-checkbox-text-color);
    cursor: pointer;
  }

  [part=base]:hover [part=checkbox-item] ~ [part=checkmark] {
    background-color: var(--eds-checkbox-checkmark-hover-background-color);
    border: var(--eds-checkbox-checkmark-hover-border-properties);
  }

  [part=base] [part=checkbox-item]:checked ~ [part=checkmark] {
    background-color: var(--eds-checkbox-checkmark-checked-background-color);
    background-image: var(--eds-checkbox-checkmark-checked-background-image);
    background-position: 50%;
    background-repeat: no-repeat;
    border: var(--eds-checkbox-checkmark-checked-border-properties);
  }

  [part=base]:focus-within [part=checkbox-item]:not(:checked) ~ [part=checkmark] {
    box-shadow: var(--eds-checkbox-checkmark-focus-box-shadow);
    border: var(--eds-checkbox-checkmark-focus-border-properties);
  }

  [part=base] [part=checkbox-item][disabled] ~ [part=checkmark] {
    background-color: var(--eds-checkbox-checkmark-disabled-background-color);
    border: var(--eds-checkbox-checkmark-disabled-border-properties);
  }

  [part=base] [part=checkbox-item][disabled]:checked ~ [part=checkmark] {
    background-image: var(--eds-checkbox-checkmark-disabled-background-image);
    background-position: 50%;
    background-repeat: no-repeat;
    background-color: var(--eds-checkbox-checkmark-disabled-background-color);
    border: var(--eds-checkbox-checkmark-disabled-border-properties);
  }
  
  [part=base]:has(input[disabled]),
  [part=base]:has(input[disabled]) ::slotted(*) {
    color: var(--eds-checkbox-checkmark-disabled-text-color);
  }

  [part=base] [part=checkbox-item][aria-invalid] ~ [part=checkmark] {
    background-color: var(--eds-checkbox-checkmark-invalid-background-color);
    border: var(--eds-checkbox-checkmark-invalid-border-properties);
  }

  [part=base] [part=checkbox-item][aria-invalid]:checked ~ [part=checkmark] {
    background-image: var(--eds-checkbox-checkmark-invalid-background-image);
    background-position: 50%;
    background-repeat: no-repeat;
    background-color: var(--eds-checkbox-checkmark-invalid-background-color);
    border: var(--eds-checkbox-checkmark-invalid-border-properties);
  }
  
  [part=base] [part=required] {
      color: var(--eds-checkbox-checkmark-required-text-color);
  }
`;
