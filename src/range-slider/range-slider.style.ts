import { css } from 'lit';

export const sliderStyles = css`
  :host {
    /* Base slider styles */
    --eds-slider-display: var(--slider-display, flex);
    --eds-slider-flex-direction: var(--slider-flex-direction, column);
    --eds-slider-gap: var(--slider-gap, var(--eds-spacing-400));
    --eds-slider-width: var(--slider-width, 100%);

    /* Label styles */
    --eds-slider-label-display: var(--slider-label-display, flex);
    --eds-slider-label-align-items: var(--slider-label-align-items, center);
    --eds-slider-label-gap: var(--slider-label-gap, var(--eds-spacing-200));
    --eds-slider-label-font-size: var(--slider-label-font-size, var(--eds-font-size-body-sm));
    --eds-slider-label-font-weight: var(--slider-label-font-weight, var(--eds-font-weight-medium));
    --eds-slider-label-color: var(--slider-label-color, var(--eds-colors-text-light));

    /* Controls styles */
    --eds-slider-controls-display: var(--slider-controls-display, flex);
    --eds-slider-controls-align-items: var(--slider-controls-align-items, center);
    --eds-slider-controls-justify-content: var(--slider-controls-justify-content, space-between);
    --eds-slider-controls-gap: var(--slider-controls-gap, var(--eds-spacing-300));

    /* Track styles */
    --eds-slider-track-height: var(--slider-track-height, var(--eds-sizing-200));
    --eds-slider-track-border-radius: var(--slider-track-border-radius, var(--eds-radius-full));
    --eds-slider-track-background: var(--slider-track-background, var(--eds-colors-surface-level-1));
    --eds-slider-track-border: var(--slider-track-border, var(--eds-stroke-025) solid var(--eds-border-color-light));

    /* Fill styles */
    --eds-slider-fill-background: var(--slider-fill-background, var(--eds-colors-secondary-default));
    --eds-slider-fill-border-radius: var(--slider-fill-border-radius, var(--eds-radius-full));
    --eds-slider-fill-transition: var(--slider-fill-transition, width var(--eds-transition-duration-base) var(--eds-transition-timing-function-base));

    /* Input styles */
    --eds-slider-input-opacity: var(--slider-input-opacity, 0);
    --eds-slider-input-cursor: var(--slider-input-cursor, pointer);

    /* Value display styles */
    --eds-slider-value-font-size: var(--slider-value-font-size, var(--eds-font-size-heading-sm));
    --eds-slider-value-font-weight: var(--slider-value-font-weight, var(--eds-font-weight-medium));
    --eds-slider-value-color: var(--slider-value-color, var(--eds-colors-text-dark));
    --eds-slider-value-text-align: var(--slider-value-text-align, right);

    /* Disabled state */
    --eds-slider-disabled-opacity: var(--slider-disabled-opacity, 0.5);
    --eds-slider-disabled-cursor: var(--slider-disabled-cursor, not-allowed);
  }

  /* Base component styles */
  [part="base"] {
    display: var(--eds-slider-display);
    flex-direction: var(--eds-slider-flex-direction);
    gap: var(--eds-slider-gap);
    width: var(--eds-slider-width);
  }

  /* Controls container - now wraps label and buttons */
  [part="controls"] {
    display: var(--eds-slider-controls-display);
    align-items: var(--eds-slider-controls-align-items);
    justify-content: var(--eds-slider-controls-justify-content);
    gap: var(--eds-slider-controls-gap);
  }

  /* Label styles */
  [part="label"] {
    display: var(--eds-slider-label-display);
    align-items: var(--eds-slider-label-align-items);
    gap: var(--eds-slider-label-gap);
    font-size: var(--eds-slider-label-font-size);
    font-weight: var(--eds-slider-label-font-weight);
    color: var(--eds-slider-label-color);
    margin: 0;
    flex: 1; /* Take up available space */
  }

  [part="icon"] {
    width: var(--eds-sizing-600);
    height: var(--eds-sizing-600);
  }

  /* Slider container */
  [part="slider-container"] {
    position: relative;
    display: flex;
    align-items: center;
    width: 100%;
  }

  /* Track styles */
  [part="track"] {
    position: relative;
    width: 100%;
    height: var(--eds-slider-track-height);
    background: var(--eds-slider-track-background);
    border-radius: var(--eds-slider-track-border-radius);
  }

  [part="range"] {
    position: relative;
    width: 100%;
    padding-right: var(--eds-spacing-300);
  }

  /* Input styles */
  [part="input"] {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    opacity: var(--eds-slider-input-opacity);
    cursor: var(--eds-slider-input-cursor);
    margin: 0;
    padding: 0;
    border: none;
    background: transparent;
    -webkit-appearance: none;
    appearance: none;
  }

  /* Fill styles */
  [part="fill"] {
    position: absolute;
    top: 0;
    left: 0;
    height: 100%;
    background: var(--eds-slider-fill-background);
    border-radius: var(--eds-slider-fill-border-radius);
    transition: var(--eds-slider-fill-transition);

    &:after {
      content: '';
      position: absolute;
      top: 50%;
      right: 0;
      transform: translate(50%, -50%);
      width: 20px;
      height: 20px;
      border-radius: 50%;
      background: var(--eds-slider-track-background);
      border: 2px solid var(--eds-slider-fill-background);
      box-shadow: 0px 4px 16px 0px rgba(0, 0, 0, 0.16);
    }

    .fill-value {
      position: absolute;
      bottom: 100%;
      right: 0;
      transform: translate(50%, 0);
      opacity: 0;
      visibility: hidden;
      background: var(--eds-slider-fill-background);
      border-radius: var(--eds-radius-200);
      padding: var(--eds-spacing-100) var(--eds-spacing-400);
      font-size: var(--eds-font-size-body-lg);
      font-weight: var(--eds-font-weight-medium);
      line-height: var(--eds-line-height-body-lg);
      text-align: center;
      white-space: nowrap;
      pointer-events: none;
      box-shadow: 0px 4px 16px 0px rgba(0, 0, 0, 0.16);
      transition: all 0.2s ease-in-out;
      transition-delay: 0.125s;

      eds-text {
        color: var(--eds-colors-text-white);
      }

      &:after {
        content: '';
        position: absolute;
        top: 100%;
        right: 50%;
        transform: translate(50%, calc(-50% - 2px)) rotate(45deg);
        background: var(--eds-slider-fill-background);
        width: var(--eds-sizing-400);
        height: var(--eds-sizing-400);
        border-radius: var(--eds-radius-100);
      }
    }
  }

  [part="range"]:hover {
    [part="fill"] {
      &:after {
        background: var(--eds-slider-fill-background);
      }

      .fill-value {
        opacity: 1;
        visibility: visible;
        transform: translate(50%, -50%);
      }
    }
  }

  /* Value display */
  [part="value-display"] {
    text-align: var(--eds-slider-value-text-align);
    min-width: calc(var(--eds-size-multiplier) * 25);
    flex-shrink: 0;
  }

  /* Control buttons */
  .control-button {
    flex-shrink: 0;
    &::part(base) {
      border: 1px solid var(--eds-border-color-light);
    }
  }

  /* Disabled state */
  .disabled {
    opacity: var(--eds-slider-disabled-opacity);
    pointer-events: none;
  }

  .disabled [part="input"] {
    cursor: var(--eds-slider-disabled-cursor);
  }

  /* Focus styles */
  [part="input"]:focus {
    outline: none;
  }

  [part="input"]:focus-visible + [part="track"] {
    box-shadow: var(--eds-ring-default);
  }

  /* Hover effects */
  [part="input"]:hover::-webkit-slider-thumb {
    background: var(--eds-colors-secondary-dark);
  }

  [part="input"]:hover::-moz-range-thumb {
    background: var(--eds-colors-secondary-dark);
  }

  /* Active state */
  [part="input"]:active::-webkit-slider-thumb {
    background: var(--eds-colors-secondary-darker);
    transform: scale(1.1);
  }

  [part="input"]:active::-moz-range-thumb {
    background: var(--eds-colors-secondary-darker);
    transform: scale(1.1);
  }

  /* Responsive adjustments */
  @media (max-width: 600px) {
    [part="controls"] {
      --eds-slider-controls-gap: var(--eds-spacing-200);
    }
    
    .control-button {
      --eds-slider-control-button-width: var(--eds-sizing-700);
      --eds-slider-control-button-height: var(--eds-sizing-700);
    }
  }
`; 