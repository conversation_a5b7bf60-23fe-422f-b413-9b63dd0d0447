import { LitElement, html, type TemplateResult, type CSSResultGroup } from 'lit';
import { property, query, state } from 'lit/decorators.js';
import { classMap } from 'lit/directives/class-map.js';
import { styles } from '../styles';
import { sliderStyles } from './range-slider.style';
import '../icons';
import '../button';

class RangeSlider extends LitElement {
  static styles: CSSResultGroup = [styles, sliderStyles];

  @property({ type: String }) label = '';
  @property() value: string | number = 0;
  @property({ type: Number }) min = 0;
  @property({ type: Number }) max = 100;
  @property({ type: Number }) step = 1;
  @property({ type: Object }) customValues: Record<string | number, string> = {};
  @property({ type: String }) unit = '';
  @property({ type: String }) icon = 'borderNone';
  @property({ type: Boolean }) disabled = false;
  @property({ type: Boolean }) showButtons = true;
  @property({ type: Boolean }) showValue = true;
  @property({ type: Number }) unlimitedOffset = 100;
  
  @state() private _currentIndex = 0;
  @state() private _sortedValues: number[] = [];
  @state() private _stepIntervals: number[] = [];
  @state() private _nonNumericKeys: string[] = [];
  @state() private _rawInputValue: number | null = null;

  @query('input') private _input!: HTMLInputElement;

  connectedCallback(): void {
    super.connectedCallback();
    this._initializeValues();
  }

  updated(changedProperties: Map<string, any>): void {
    if (changedProperties.has('customValues')) {
      this._initializeValues();
    } else if (changedProperties.has('value')) {
      this._updateCurrentIndex();
      this._syncValueWithIndex();
    }
  }

  private _initializeValues(): void {
    this._sortCustomValues();
    this._updateCurrentIndex();
    this._syncValueWithIndex();
    this._rawInputValue = null;
  }

  private _sortCustomValues(): void {
    if (Object.keys(this.customValues).length === 0) {
      this._sortedValues = [];
      this._stepIntervals = [];
      this._nonNumericKeys = [];
      return;
    }

    const numericKeys: number[] = [];
    this._nonNumericKeys = [];
    
    Object.keys(this.customValues).forEach(key => {
      const numericKey = Number(key);
      if (!isNaN(numericKey) && isFinite(numericKey)) {
        numericKeys.push(numericKey);
      } else {
        this._nonNumericKeys.push(key);
      }
    });

    this._sortedValues = numericKeys.sort((a, b) => a - b);
    
    if (this._nonNumericKeys.length > 0) {
      const maxNumeric = this._sortedValues.length > 0 ? Math.max(...this._sortedValues) : this.max;
      const virtualMax = maxNumeric + this.unlimitedOffset;
      this._sortedValues.push(virtualMax);
    }
    
    this._calculateStepIntervals();
  }

  private _calculateStepIntervals(): void {
    if (this._sortedValues.length <= 1) {
      this._stepIntervals = [];
      return;
    }

    this._stepIntervals = [];
    for (let i = 1; i < this._sortedValues.length; i++) {
      const interval = this._sortedValues[i] - this._sortedValues[i - 1];
      this._stepIntervals.push(interval);
    }
  }

  private _getCurrentStepInterval(): number | null {
    if (this._stepIntervals.length === 0 || this._currentIndex >= this._stepIntervals.length) {
      return null;
    }
    return this._stepIntervals[this._currentIndex];
  }

  private _getStepIntervals(): { from: number, to: number, interval: number }[] {
    if (this._sortedValues.length <= 1) return [];
    
    const intervals: { from: number, to: number, interval: number }[] = [];
    for (let i = 0; i < this._stepIntervals.length; i++) {
      intervals.push({
        from: this._sortedValues[i],
        to: this._sortedValues[i + 1],
        interval: this._stepIntervals[i]
      });
    }
    return intervals;
  }

  private _getNumericValues(): number[] {
    return this._sortedValues.slice(0, this._nonNumericKeys.length > 0 ? -1 : undefined);
  }

  private _getNonNumericSnapPoint(): number | null {
    if (this._nonNumericKeys.length === 0) return null;
    
    const numericValues = this._getNumericValues();
    if (numericValues.length === 0) return null;

    const lastNumericValue = numericValues[numericValues.length - 1];
    
    return lastNumericValue + this.unlimitedOffset / 2;
  }

  private _findClosestIndex(value: number): number {
    const numericValues = this._getNumericValues();
    if (numericValues.length === 0) return 0;

    let closestIndex = 0;
    let minDiff = Infinity;

    numericValues.forEach((val, i) => {
      const diff = Math.abs(value - val);
      if (diff < minDiff) {
        minDiff = diff;
        closestIndex = i;
      }
    });

    return closestIndex;
  }

  private _updateCurrentIndex(): void {
    if (this._sortedValues.length === 0) {
      const numericValue = typeof this.value === 'number' ? this.value : 0;
      this._currentIndex = Math.round((numericValue - this.min) / this.step);
      return;
    }

    if (this._nonNumericKeys.length > 0 && this._nonNumericKeys.includes(String(this.value))) {
      this._currentIndex = this._sortedValues.length - 1;
      return;
    }

    const numericValue = typeof this.value === 'number' ? this.value : 0;
    this._currentIndex = this._findClosestIndex(numericValue);
  }

  private _getCurrentValue(): number {
    return this._sortedValues.length > 0 
      ? this._sortedValues[this._currentIndex] 
      : typeof this.value === 'number' ? this.value : 0;
  }

  private _getCurrentKey(): string | null {
    if (Object.keys(this.customValues).length === 0) return null;

    if (this._rawInputValue !== null) {
      const nonNumericSnapPoint = this._getNonNumericSnapPoint();
      if (nonNumericSnapPoint !== null && this._rawInputValue >= nonNumericSnapPoint) {
        return this.customValues[this._nonNumericKeys[0]] || null;
      }
      
      const closestIndex = this._findClosestIndex(this._rawInputValue);
      const closestValue = this._sortedValues[closestIndex];
      return this.customValues[closestValue] || null;
    }

    if (this._currentIndex === this._sortedValues.length - 1 && this._nonNumericKeys.length > 0) {
      const nonNumericKey = this._nonNumericKeys[0];
      return this.customValues[nonNumericKey] || null;
    }
    
    const currentValue = this._getCurrentValue();
    const customValue = this.customValues[currentValue];
    return customValue || null;
  }

  private _getProgress(): number {
    const valueToUse = this._rawInputValue !== null ? this._rawInputValue : this._getCurrentValue();

    if (this._sortedValues.length === 1 && this._nonNumericKeys.length > 0) {
      this._input.style.setProperty('pointer-events', 'none');
      return 100;
    }
    
    if (this._sortedValues.length === 0) {
      const numericValue = this._rawInputValue !== null ? this._rawInputValue : (typeof this.value === 'number' ? this.value : 0);
      return ((numericValue - this.min) / (this.max - this.min)) * 100;
    }

    if (this._sortedValues.length <= 1) return 0;
    
    if (this._rawInputValue === null && this._currentIndex === this._sortedValues.length - 1 && this._nonNumericKeys.length > 0) {
      return 100;
    }
    
    const minValue = this._sortedValues[0];
    const maxValue = this._sortedValues[this._sortedValues.length - 1];
    
    if (maxValue === minValue) {
      return this._getCurrentValue() > minValue ? 100 : 0;
    }

    const progress = ((valueToUse - minValue) / (maxValue - minValue)) * 100;
    return Math.max(0, Math.min(progress, 100));
  }

  private _getSliderConfig() {
    if (this._sortedValues.length === 0) {
      return {
        min: this.min,
        max: this.max,
        step: this.step,
        value: typeof this.value === 'number' ? this.value : 0
      };
    }

    const minValue = this._sortedValues[0];
    const maxValue = this._sortedValues[this._sortedValues.length - 1];
    
    const smallestInterval = this._stepIntervals.length > 0 ? Math.min(...this._stepIntervals) : 1;
    const stepSize = Math.max(1, Math.floor(smallestInterval / 10));

    return {
      min: minValue,
      max: maxValue,
      step: stepSize,
      value: this._getCurrentValue()
    };
  }

  private _handleSliderInput = (event: Event): void => {
    if (this.disabled) return;
    
    const target = event.target as HTMLInputElement;
    const inputValue = parseFloat(target.value);
    
    this._rawInputValue = inputValue;
    
    this.requestUpdate();
  };

  private _handleSliderChange = (event: Event): void => {
    if (this.disabled) return;
    
    const target = event.target as HTMLInputElement;
    const inputValue = parseFloat(target.value);
    
    this._rawInputValue = null;
    
    if (this._sortedValues.length > 0) {
      const nonNumericSnapPoint = this._getNonNumericSnapPoint();
      
      if (nonNumericSnapPoint !== null && inputValue >= nonNumericSnapPoint) {
        this._currentIndex = this._sortedValues.length - 1;
        this.value = this._nonNumericKeys[0];
      } else {
        this._currentIndex = this._findClosestIndex(inputValue);
        this.value = this._sortedValues[this._currentIndex];
      }
    } else {
      this.value = inputValue;
    }
    
    this._dispatchChangeEvent();
  };

  private _handleDecrease = (): void => {
    if (this.disabled) return;
    
    this._rawInputValue = null;
    
    if (this._sortedValues.length > 0) {
      if (this._currentIndex > 0) {
        this._currentIndex--;
        if (this._currentIndex === this._sortedValues.length - 1 && this._nonNumericKeys.length > 0) {
          this.value = this._nonNumericKeys[0];
        } else {
          this.value = this._sortedValues[this._currentIndex];
        }
      }
    } else {
      const numericValue = typeof this.value === 'number' ? this.value : 0;
      this.value = Math.max(this.min, numericValue - this.step);
    }
    
    this._dispatchChangeEvent();
  };

  private _handleIncrease = (): void => {
    if (this.disabled) return;
    
    this._rawInputValue = null;
    
    if (this._sortedValues.length > 0) {
      if (this._currentIndex < this._sortedValues.length - 1) {
        this._currentIndex++;
        if (this._currentIndex === this._sortedValues.length - 1 && this._nonNumericKeys.length > 0) {
          this.value = this._nonNumericKeys[0];
        } else {
          this.value = this._sortedValues[this._currentIndex];
        }
      }
    } else {
      const numericValue = typeof this.value === 'number' ? this.value : 0;
      this.value = Math.min(this.max, numericValue + this.step);
    }
    
    this._dispatchChangeEvent();
  };

  private _dispatchChangeEvent(): void {
    const currentValue = this._getCurrentValue();
    const currentKey = this._getCurrentKey();
    const currentStepInterval = this._getCurrentStepInterval();
    const stepIntervals = this._getStepIntervals();
    
    this.dispatchEvent(new CustomEvent('slider-change', {
      detail: { 
        value: this.value,
        key: currentKey,
        numericValue: currentValue,
        index: this._currentIndex,
        stepInterval: currentStepInterval,
        allStepIntervals: stepIntervals
      },
      bubbles: true,
      composed: true
    }));
  }

  private _syncValueWithIndex(): void {
    if (this._sortedValues.length > 0 && this._currentIndex < this._sortedValues.length) {
      if (this._currentIndex === this._sortedValues.length - 1 && this._nonNumericKeys.length > 0) {
        this.value = this._nonNumericKeys[0];
      } else {
        this.value = this._sortedValues[this._currentIndex] || this.value;
      }
    }
  }

  private _isAtMinimum(): boolean {
    return this._sortedValues.length > 0 
      ? this._currentIndex <= 0 
      : typeof this.value === 'number' ? this.value <= this.min : false;
  }

  private _isAtMaximum(): boolean {
    return this._sortedValues.length > 0 
      ? this._currentIndex >= this._sortedValues.length - 1 
      : typeof this.value === 'number' ? this.value >= this.max : false;
  }

  protected render(): TemplateResult {
    const currentKey = this._getCurrentKey();
    const progress = this._getProgress();
    const sliderConfig = this._getSliderConfig();

    return html`
      <div part="base" class=${classMap({ 'disabled': this.disabled })}>
        <div part="controls" class="controls">
          ${this.label ? html`
            <label part="label" class="label">
              <eds-icon part="icon" name="${this.icon}"></eds-icon>
              <eds-text size="lg" weight="medium" text="${this.label}"></eds-text>
            </label>
          ` : ''}
          
          ${this.showButtons ? html`
            <eds-button
              part="decrease-button"
              class="control-button"
              appearance="subtle"
              size="compact"
              iconOnly
              circle
              iconLeading="minus"
              ?disabled=${this.disabled || this._isAtMinimum()}
              @button-click=${this._handleDecrease}
            ></eds-button>
            
            <eds-button
              part="increase-button"
              class="control-button"
              appearance="subtle"
              size="compact"
              iconOnly
              circle
              iconLeading="plus"
              ?disabled=${this.disabled || this._isAtMaximum()}
              @button-click=${this._handleIncrease}
            ></eds-button>
          ` : ''}
        </div>

        <div part="slider-container" class="slider-container">
          <div part="range">
            <div part="track" class="track">
              <div 
                part="fill" 
                class="fill" 
                style="width: ${progress}%"
                data-value="${(currentKey) + ' ' + this.unit}"
              >
                <div part="fill-value" class="fill-value">
                  <eds-text size="lg" weight="medium" text="${currentKey}"></eds-text>
                  <eds-text size="sm" text="${this.unit}"></eds-text>
                </div>
              </div>
            </div>
            
            <input
              part="input"
              type="range"
              class="input"
              min=${sliderConfig.min}
              max=${sliderConfig.max}
              step=${sliderConfig.step}
              .value=${sliderConfig.value.toString()}
              ?disabled=${this.disabled}
              @input=${this._handleSliderInput}
              @change=${this._handleSliderChange}
              aria-label=${this.label || 'Slider'}
            />
          </div>
          
          ${this.showValue ? html`
            <div part="value-display" class="value-display">
              <eds-text size="lg" weight="medium" text="${currentKey}"></eds-text>
              <eds-text size="sm" text="${this.unit}"></eds-text>
            </div>
          ` : ''}
        </div>
      </div>
    `;
  }
}

if (!customElements.get('eds-range-slider')) {
  customElements.define('eds-range-slider', RangeSlider);
}

declare global {
  interface HTMLElementTagNameMap {
    'eds-range-slider': RangeSlider;
  }
}

export { RangeSlider }; 