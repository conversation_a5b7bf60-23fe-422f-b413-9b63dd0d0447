import { Meta, Title, Primary, Controls, Stories, Story, Canvas, Source } from '@storybook/addon-docs/blocks';
 
{/*
  * 👇 The isTemplate property is required to tell Storybook that this is a template
  * See https://storybook.js.org/docs/api/doc-blocks/doc-block-meta
  * to learn how to use
*/}
 
<Meta isTemplate />
 
<Title />
<Primary parameters={{
    docs: {
      source: {
        code: null, // Kaynak kodu gösterimini devre dışı bırakır
      },
    },
  }} />
<Controls />
 
<Stories includePrimary={false} title={'Stories'} />