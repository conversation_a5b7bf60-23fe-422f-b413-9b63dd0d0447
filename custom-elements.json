{"schemaVersion": "1.0.0", "readme": "", "modules": [{"kind": "javascript-module", "path": "src/index.ts", "declarations": [], "exports": [{"kind": "js", "name": "Accordion", "declaration": {"name": "Accordion", "module": "./accordion"}}, {"kind": "js", "name": "AccordionGroup", "declaration": {"name": "AccordionGroup", "module": "./accordion-group"}}, {"kind": "js", "name": "ActionItem", "declaration": {"name": "ActionItem", "module": "./action-item"}}, {"kind": "js", "name": "<PERSON><PERSON>", "declaration": {"name": "<PERSON><PERSON>", "module": "./alert"}}, {"kind": "js", "name": "Badge", "declaration": {"name": "Badge", "module": "./badge"}}, {"kind": "js", "name": "<PERSON><PERSON>", "declaration": {"name": "<PERSON><PERSON>", "module": "./button"}}, {"kind": "js", "name": "Card", "declaration": {"name": "Card", "module": "./card"}}, {"kind": "js", "name": "Checkbox", "declaration": {"name": "Checkbox", "module": "./checkbox"}}, {"kind": "js", "name": "CheckboxGroup", "declaration": {"name": "CheckboxGroup", "module": "./checkbox-group"}}, {"kind": "js", "name": "CheckoutCard", "declaration": {"name": "CheckoutCard", "module": "./checkout-card"}}, {"kind": "js", "name": "CheckoutStep", "declaration": {"name": "CheckoutStep", "module": "./checkout-step"}}, {"kind": "js", "name": "DataList", "declaration": {"name": "DataList", "module": "./data-list"}}, {"kind": "js", "name": "FormField", "declaration": {"name": "FormField", "module": "./form-field"}}, {"kind": "js", "name": "Heading", "declaration": {"name": "Heading", "module": "./heading"}}, {"kind": "js", "name": "Icon", "declaration": {"name": "Icon", "module": "./icons"}}, {"kind": "js", "name": "Image", "declaration": {"name": "Image", "module": "./image"}}, {"kind": "js", "name": "Link", "declaration": {"name": "Link", "module": "./link"}}, {"kind": "js", "name": "PhoneNumber", "declaration": {"name": "PhoneNumber", "module": "./phone-number"}}, {"kind": "js", "name": "ProgressBar", "declaration": {"name": "ProgressBar", "module": "./progress-bar"}}, {"kind": "js", "name": "ProgressTracker", "declaration": {"name": "ProgressTracker", "module": "./progress-tracker"}}, {"kind": "js", "name": "Radio", "declaration": {"name": "Radio", "module": "./radio"}}, {"kind": "js", "name": "RadioGroup", "declaration": {"name": "RadioGroup", "module": "./radio-group"}}, {"kind": "js", "name": "SegmentedControl", "declaration": {"name": "SegmentedControl", "module": "./segmented-control"}}, {"kind": "js", "name": "Select", "declaration": {"name": "Select", "module": "./select"}}, {"kind": "js", "name": "Tabs", "declaration": {"name": "Tabs", "module": "./tabs"}}, {"kind": "js", "name": "Tag", "declaration": {"name": "Tag", "module": "./tag"}}, {"kind": "js", "name": "Text", "declaration": {"name": "Text", "module": "./text"}}, {"kind": "js", "name": "TextField", "declaration": {"name": "TextField", "module": "./text-field"}}, {"kind": "js", "name": "Tile", "declaration": {"name": "Tile", "module": "./tile"}}, {"kind": "js", "name": "Toast", "declaration": {"name": "Toast", "module": "./toast"}}, {"kind": "js", "name": "MediaObject", "declaration": {"name": "MediaObject", "module": "./media-object"}}, {"kind": "js", "name": "DatePicker", "declaration": {"name": "DatePicker", "module": "./date-picker"}}, {"kind": "js", "name": "globalStyles", "declaration": {"name": "globalStyles", "module": "./styles"}}]}, {"kind": "javascript-module", "path": "src/accordion/index.ts", "declarations": [{"kind": "class", "description": "", "name": "Accordion", "members": [{"kind": "field", "name": "id", "type": {"text": "string"}, "default": "''", "attribute": "id"}, {"kind": "field", "name": "caption", "type": {"text": "string"}, "default": "''", "attribute": "caption"}, {"kind": "field", "name": "isOpen", "type": {"text": "boolean"}, "default": "false", "attribute": "isOpen", "reflects": true}, {"kind": "field", "name": "isDisabled", "type": {"text": "boolean"}, "default": "false", "attribute": "isDisabled", "reflects": true}, {"kind": "field", "name": "idCounter", "type": {"text": "number"}, "privacy": "private", "static": true, "default": "0"}, {"kind": "field", "name": "uniqueId", "type": {"text": "string"}, "privacy": "private", "readonly": true, "default": "`accordion-${Accordion.idCounter++}`"}, {"kind": "field", "name": "accordionContentId", "type": {"text": "string"}, "readonly": true}, {"kind": "field", "name": "_toggleAccordion", "privacy": "private"}], "attributes": [{"name": "id", "type": {"text": "string"}, "default": "''", "fieldName": "id"}, {"name": "caption", "type": {"text": "string"}, "default": "''", "fieldName": "caption"}, {"name": "isOpen", "type": {"text": "boolean"}, "default": "false", "fieldName": "isOpen"}, {"name": "isDisabled", "type": {"text": "boolean"}, "default": "false", "fieldName": "isDisabled"}], "superclass": {"name": "LitElement", "package": "lit"}, "tagName": "eds-accordion", "customElement": true}], "exports": [{"kind": "custom-element-definition", "name": "eds-accordion", "declaration": {"name": "Accordion", "module": "src/accordion/index.ts"}}, {"kind": "js", "name": "Accordion", "declaration": {"name": "Accordion", "module": "src/accordion/index.ts"}}]}, {"kind": "javascript-module", "path": "src/accordion-group/index.ts", "declarations": [{"kind": "class", "description": "", "name": "AccordionGroup", "members": [{"kind": "field", "name": "isMultiple", "type": {"text": "boolean"}, "default": "false", "attribute": "isMultiple", "reflects": true}, {"kind": "field", "name": "accordion<PERSON><PERSON>", "type": {"text": "Accordion[]"}}, {"kind": "field", "name": "_handleClick", "privacy": "private"}], "attributes": [{"name": "isMultiple", "type": {"text": "boolean"}, "default": "false", "fieldName": "isMultiple"}], "superclass": {"name": "LitElement", "package": "lit"}, "tagName": "eds-accordion-group", "customElement": true}], "exports": [{"kind": "custom-element-definition", "name": "eds-accordion-group", "declaration": {"name": "AccordionGroup", "module": "src/accordion-group/index.ts"}}, {"kind": "js", "name": "AccordionGroup", "declaration": {"name": "AccordionGroup", "module": "src/accordion-group/index.ts"}}]}, {"kind": "javascript-module", "path": "src/action-item/index.ts", "declarations": [{"kind": "class", "description": "", "name": "ActionItem", "members": [{"kind": "field", "name": "iconLeading", "type": {"text": "string"}, "default": "''", "attribute": "iconLeading"}, {"kind": "field", "name": "text", "type": {"text": "string"}, "default": "''", "attribute": "text"}, {"kind": "field", "name": "iconTrailing", "type": {"text": "string"}, "default": "''", "attribute": "iconTrailing"}], "attributes": [{"name": "iconLeading", "type": {"text": "string"}, "default": "''", "fieldName": "iconLeading"}, {"name": "text", "type": {"text": "string"}, "default": "''", "fieldName": "text"}, {"name": "iconTrailing", "type": {"text": "string"}, "default": "''", "fieldName": "iconTrailing"}], "superclass": {"name": "LitElement", "package": "lit"}, "tagName": "eds-action-item", "customElement": true}], "exports": [{"kind": "custom-element-definition", "name": "eds-action-item", "declaration": {"name": "ActionItem", "module": "src/action-item/index.ts"}}, {"kind": "js", "name": "ActionItem", "declaration": {"name": "ActionItem", "module": "src/action-item/index.ts"}}]}, {"kind": "javascript-module", "path": "src/alert/index.ts", "declarations": [{"kind": "class", "description": "", "name": "<PERSON><PERSON>", "members": [{"kind": "field", "name": "id", "type": {"text": "string"}, "default": "\"\"", "attribute": "id"}, {"kind": "field", "name": "appearance", "type": {"text": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}, "default": "'info'", "attribute": "appearance"}, {"kind": "field", "name": "title", "type": {"text": "string"}, "default": "\"\"", "attribute": "title"}, {"kind": "field", "name": "description", "type": {"text": "string"}, "default": "\"\"", "attribute": "description"}, {"kind": "field", "name": "showIcon", "type": {"text": "boolean"}, "default": "false", "attribute": "showIcon"}, {"kind": "field", "name": "iconName", "type": {"text": "string"}, "default": "''", "attribute": "iconName"}, {"kind": "field", "name": "actions", "type": {"text": "AlertAction[]"}, "default": "[]", "attribute": "actions"}, {"kind": "field", "name": "_renderActions", "privacy": "private"}], "attributes": [{"name": "id", "type": {"text": "string"}, "default": "\"\"", "fieldName": "id"}, {"name": "appearance", "type": {"text": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}, "default": "'info'", "fieldName": "appearance"}, {"name": "title", "type": {"text": "string"}, "default": "\"\"", "fieldName": "title"}, {"name": "description", "type": {"text": "string"}, "default": "\"\"", "fieldName": "description"}, {"name": "showIcon", "type": {"text": "boolean"}, "default": "false", "fieldName": "showIcon"}, {"name": "iconName", "type": {"text": "string"}, "default": "''", "fieldName": "iconName"}, {"name": "actions", "type": {"text": "AlertAction[]"}, "default": "[]", "fieldName": "actions"}], "superclass": {"name": "LitElement", "package": "lit"}, "tagName": "eds-alert", "customElement": true}], "exports": [{"kind": "custom-element-definition", "name": "eds-alert", "declaration": {"name": "<PERSON><PERSON>", "module": "src/alert/index.ts"}}, {"kind": "js", "name": "<PERSON><PERSON>", "declaration": {"name": "<PERSON><PERSON>", "module": "src/alert/index.ts"}}]}, {"kind": "javascript-module", "path": "src/badge/index.ts", "declarations": [{"kind": "class", "description": "", "name": "Badge", "members": [{"kind": "field", "name": "appearance", "type": {"text": "Appearance"}, "default": "'primary'", "attribute": "appearance"}, {"kind": "field", "name": "label", "type": {"text": "string"}, "default": "\"\"", "attribute": "label"}, {"kind": "field", "name": "type", "type": {"text": "Type"}, "default": "\"rounded\"", "attribute": "type"}, {"kind": "field", "name": "size", "type": {"text": "Size"}, "default": "'medium'", "attribute": "size"}], "attributes": [{"name": "label", "type": {"text": "string"}, "default": "\"\"", "fieldName": "label"}, {"name": "appearance", "type": {"text": "Appearance"}, "default": "'primary'", "fieldName": "appearance"}, {"name": "type", "type": {"text": "Type"}, "default": "\"rounded\"", "fieldName": "type"}, {"name": "size", "type": {"text": "Size"}, "default": "'medium'", "fieldName": "size"}], "superclass": {"name": "LitElement", "package": "lit"}, "tagName": "eds-badge", "customElement": true}], "exports": [{"kind": "custom-element-definition", "name": "eds-badge", "declaration": {"name": "Badge", "module": "src/badge/index.ts"}}, {"kind": "js", "name": "Badge", "declaration": {"name": "Badge", "module": "src/badge/index.ts"}}]}, {"kind": "javascript-module", "path": "src/button/index.ts", "declarations": [{"kind": "class", "description": "", "name": "<PERSON><PERSON>", "members": [{"kind": "field", "name": "label", "type": {"text": "string"}, "default": "''", "attribute": "label"}, {"kind": "field", "name": "href", "type": {"text": "string"}, "default": "''", "attribute": "href"}, {"kind": "field", "name": "target", "type": {"text": "HTMLAnchorElement['target']"}, "default": "''", "attribute": "target"}, {"kind": "field", "name": "type", "type": {"text": "HTMLButtonElement['type']"}, "default": "'button'", "attribute": "type"}, {"kind": "field", "name": "appearance", "type": {"text": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}, "default": "'primary'", "attribute": "appearance"}, {"kind": "field", "name": "size", "type": {"text": "ButtonSizeV<PERSON>ues"}, "default": "'default'", "attribute": "size"}, {"kind": "field", "name": "shouldFitContainer", "type": {"text": "boolean"}, "default": "false", "attribute": "shouldFitContainer"}, {"kind": "field", "name": "iconLeading", "type": {"text": "string"}, "default": "''", "attribute": "iconLeading"}, {"kind": "field", "name": "iconTrailing", "type": {"text": "string"}, "default": "''", "attribute": "iconTrailing"}, {"kind": "field", "name": "iconOnly", "type": {"text": "boolean"}, "default": "false", "attribute": "iconOnly"}, {"kind": "field", "name": "circle", "type": {"text": "boolean"}, "default": "false", "attribute": "circle"}, {"kind": "field", "name": "isDisabled", "type": {"text": "boolean"}, "default": "false", "attribute": "disabled", "reflects": true}, {"kind": "field", "name": "loading", "type": {"text": "boolean"}, "default": "false", "attribute": "loading", "reflects": true}, {"kind": "field", "name": "button", "type": {"text": "HTMLButtonElement"}}, {"kind": "field", "name": "link", "type": {"text": "HTMLElement"}}, {"kind": "field", "name": "_onClick", "privacy": "private"}], "attributes": [{"name": "label", "type": {"text": "string"}, "default": "''", "fieldName": "label"}, {"name": "href", "type": {"text": "string"}, "default": "''", "fieldName": "href"}, {"name": "target", "type": {"text": "HTMLAnchorElement['target']"}, "default": "''", "fieldName": "target"}, {"name": "type", "type": {"text": "HTMLButtonElement['type']"}, "default": "'button'", "fieldName": "type"}, {"name": "appearance", "type": {"text": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}, "default": "'primary'", "fieldName": "appearance"}, {"name": "size", "type": {"text": "ButtonSizeV<PERSON>ues"}, "default": "'default'", "fieldName": "size"}, {"name": "shouldFitContainer", "type": {"text": "boolean"}, "default": "false", "fieldName": "shouldFitContainer"}, {"name": "iconLeading", "type": {"text": "string"}, "default": "''", "fieldName": "iconLeading"}, {"name": "iconTrailing", "type": {"text": "string"}, "default": "''", "fieldName": "iconTrailing"}, {"name": "iconOnly", "type": {"text": "boolean"}, "default": "false", "fieldName": "iconOnly"}, {"name": "circle", "type": {"text": "boolean"}, "default": "false", "fieldName": "circle"}, {"name": "disabled", "type": {"text": "boolean"}, "default": "false", "fieldName": "isDisabled"}, {"name": "loading", "type": {"text": "boolean"}, "default": "false", "fieldName": "loading"}], "superclass": {"name": "LitElement", "package": "lit"}, "tagName": "eds-button", "customElement": true}], "exports": [{"kind": "custom-element-definition", "name": "eds-button", "declaration": {"name": "<PERSON><PERSON>", "module": "src/button/index.ts"}}, {"kind": "js", "name": "<PERSON><PERSON>", "declaration": {"name": "<PERSON><PERSON>", "module": "src/button/index.ts"}}]}, {"kind": "javascript-module", "path": "src/card/index.ts", "declarations": [{"kind": "class", "description": "", "name": "Card", "members": [{"kind": "field", "name": "title", "type": {"text": "string"}, "default": "''", "attribute": "title"}, {"kind": "field", "name": "linkText", "type": {"text": "string"}, "default": "''", "attribute": "linkText"}, {"kind": "field", "name": "linkURL", "type": {"text": "string"}, "default": "''", "attribute": "linkURL"}, {"kind": "field", "name": "linkIcon", "type": {"text": "string"}, "default": "''", "attribute": "linkIcon"}, {"kind": "field", "name": "description", "type": {"text": "string"}, "default": "''", "attribute": "description"}], "attributes": [{"name": "title", "type": {"text": "string"}, "default": "''", "fieldName": "title"}, {"name": "linkText", "type": {"text": "string"}, "default": "''", "fieldName": "linkText"}, {"name": "linkURL", "type": {"text": "string"}, "default": "''", "fieldName": "linkURL"}, {"name": "linkIcon", "type": {"text": "string"}, "default": "''", "fieldName": "linkIcon"}, {"name": "description", "type": {"text": "string"}, "default": "''", "fieldName": "description"}], "superclass": {"name": "LitElement", "package": "lit"}, "tagName": "eds-card", "customElement": true}], "exports": [{"kind": "custom-element-definition", "name": "eds-card", "declaration": {"name": "Card", "module": "src/card/index.ts"}}, {"kind": "js", "name": "Card", "declaration": {"name": "Card", "module": "src/card/index.ts"}}]}, {"kind": "javascript-module", "path": "src/checkbox/index.ts", "declarations": [{"kind": "class", "description": "", "name": "Checkbox", "members": [{"kind": "field", "name": "id", "type": {"text": "string"}, "default": "'label1'", "attribute": "id"}, {"kind": "field", "name": "name", "type": {"text": "string"}, "default": "'label1'", "attribute": "name"}, {"kind": "field", "name": "value", "type": {"text": "string"}, "default": "'label1'", "attribute": "value"}, {"kind": "field", "name": "label", "type": {"text": "string"}, "default": "'Label'", "attribute": "label"}, {"kind": "field", "name": "isChecked", "type": {"text": "boolean"}, "default": "false", "attribute": "isChecked", "reflects": true}, {"kind": "field", "name": "isDisabled", "type": {"text": "boolean"}, "default": "false", "attribute": "isDisabled", "reflects": true}, {"kind": "field", "name": "isInvalid", "type": {"text": "boolean"}, "default": "false", "attribute": "isInvalid", "reflects": true}, {"kind": "field", "name": "isRequired", "type": {"text": "boolean"}, "default": "false", "attribute": "isRequired", "reflects": true}, {"kind": "field", "name": "slotElement", "type": {"text": "HTMLSlotElement"}}, {"kind": "field", "name": "_addLabelListeners", "privacy": "private"}, {"kind": "field", "name": "_handleLabelClick", "privacy": "private"}, {"kind": "field", "name": "_renderCheckbox", "privacy": "private"}], "attributes": [{"name": "id", "type": {"text": "string"}, "default": "'label1'", "fieldName": "id"}, {"name": "name", "type": {"text": "string"}, "default": "'label1'", "fieldName": "name"}, {"name": "value", "type": {"text": "string"}, "default": "'label1'", "fieldName": "value"}, {"name": "label", "type": {"text": "string"}, "default": "'Label'", "fieldName": "label"}, {"name": "isChecked", "type": {"text": "boolean"}, "default": "false", "fieldName": "isChecked"}, {"name": "isDisabled", "type": {"text": "boolean"}, "default": "false", "fieldName": "isDisabled"}, {"name": "isInvalid", "type": {"text": "boolean"}, "default": "false", "fieldName": "isInvalid"}, {"name": "isRequired", "type": {"text": "boolean"}, "default": "false", "fieldName": "isRequired"}], "superclass": {"name": "LitElement", "package": "lit"}, "tagName": "eds-checkbox", "customElement": true}], "exports": [{"kind": "custom-element-definition", "name": "eds-checkbox", "declaration": {"name": "Checkbox", "module": "src/checkbox/index.ts"}}, {"kind": "js", "name": "Checkbox", "declaration": {"name": "Checkbox", "module": "src/checkbox/index.ts"}}]}, {"kind": "javascript-module", "path": "src/checkbox-group/index.ts", "declarations": [{"kind": "class", "description": "", "name": "CheckboxGroup", "members": [{"kind": "field", "name": "name", "type": {"text": "string"}, "default": "'checkbox'", "attribute": "name"}, {"kind": "field", "name": "isRequired", "type": {"text": "boolean"}, "default": "false", "attribute": "isRequired", "reflects": true}, {"kind": "field", "name": "checkboxElements", "type": {"text": "Checkbox[]"}}], "attributes": [{"name": "name", "type": {"text": "string"}, "default": "'checkbox'", "fieldName": "name"}, {"name": "isRequired", "type": {"text": "boolean"}, "default": "false", "fieldName": "isRequired"}], "superclass": {"name": "LitElement", "package": "lit"}, "tagName": "eds-checkbox-group", "customElement": true}], "exports": [{"kind": "custom-element-definition", "name": "eds-checkbox-group", "declaration": {"name": "CheckboxGroup", "module": "src/checkbox-group/index.ts"}}, {"kind": "js", "name": "CheckboxGroup", "declaration": {"name": "CheckboxGroup", "module": "src/checkbox-group/index.ts"}}]}, {"kind": "javascript-module", "path": "src/checkout-card/index.ts", "declarations": [{"kind": "class", "description": "", "name": "CheckoutCard", "members": [{"kind": "field", "name": "type", "type": {"text": "CheckoutCardType"}, "default": "'active'", "attribute": "type"}, {"kind": "field", "name": "itemAmount", "type": {"text": "string"}, "default": "'1'", "attribute": "itemAmount"}, {"kind": "field", "name": "label", "type": {"text": "string"}, "default": "'Product'", "attribute": "label"}, {"kind": "field", "name": "headerIcon", "type": {"text": "string"}, "default": "'placeholderShowcase'", "attribute": "headerIcon"}], "attributes": [{"name": "type", "type": {"text": "CheckoutCardType"}, "default": "'active'", "attribute": "type"}, {"name": "itemAmount", "type": {"text": "string"}, "default": "'1'", "attribute": "itemAmount"}, {"name": "label", "type": {"text": "string"}, "default": "'Product'", "attribute": "label"}, {"name": "headerIcon", "type": {"text": "string"}, "default": "'placeholderShowcase'", "attribute": "headerIcon"}], "superclass": {"name": "LitElement", "package": "lit"}, "tagName": "eds-checkout-card", "customElement": true}], "exports": [{"kind": "custom-element-definition", "name": "eds-checkout-card", "declaration": {"name": "CheckoutCard", "module": "src/checkout-card/index.ts"}}, {"kind": "js", "name": "CheckoutCard", "declaration": {"name": "CheckoutCard", "module": "src/checkout-card/index.ts"}}]}, {"kind": "javascript-module", "path": "src/checkout-step/index.ts", "declarations": [{"kind": "class", "description": "", "name": "CheckoutStep", "members": [{"kind": "field", "name": "type", "type": {"text": "CheckoutStepType"}, "default": "'active'", "attribute": "type"}, {"kind": "field", "name": "number", "type": {"text": "string"}, "default": "'1'", "attribute": "number"}, {"kind": "field", "name": "label", "type": {"text": "string"}, "default": "'Step 1'", "attribute": "label"}, {"kind": "field", "name": "readyToComplete", "type": {"text": "boolean"}, "default": "false", "attribute": "readyToComplete"}], "attributes": [{"name": "type", "type": {"text": "CheckoutStepType"}, "default": "'active'", "attribute": "type"}, {"name": "number", "type": {"text": "string"}, "default": "'1'", "attribute": "number"}, {"name": "label", "type": {"text": "string"}, "default": "'Step 1'", "attribute": "label"}, {"name": "readyToComplete", "type": {"text": "boolean"}, "default": "false", "attribute": "readyToComplete"}], "superclass": {"name": "LitElement", "package": "lit"}, "tagName": "eds-step", "customElement": true}], "exports": [{"kind": "custom-element-definition", "name": "eds-checkout-step", "declaration": {"name": "CheckoutStep", "module": "src/checkout-step/index.ts"}}, {"kind": "js", "name": "CheckoutStep", "declaration": {"name": "CheckoutStep", "module": "src/checkout-step/index.ts"}}]}, {"kind": "javascript-module", "path": "src/data-list/index.ts", "declarations": [{"kind": "class", "description": "", "name": "DataList", "members": [{"kind": "field", "name": "itemsSize", "type": {"text": "number"}, "default": "5", "attribute": "itemsSize"}, {"kind": "field", "name": "trim", "type": {"text": "boolean"}, "default": "false", "attribute": "trim"}, {"kind": "field", "name": "isExpanded", "type": {"text": "boolean"}, "default": "false", "attribute": "isExpanded"}, {"kind": "field", "name": "expandedText", "type": {"text": "string"}, "default": "''", "attribute": "expandedText"}, {"kind": "field", "name": "unexpandedText", "type": {"text": "string"}, "default": "''", "attribute": "unexpandedText"}, {"kind": "field", "name": "items", "type": {"text": "DataListItem[]"}, "default": "[]", "attribute": "items"}, {"kind": "field", "name": "_toggleShowMore", "privacy": "private"}, {"kind": "field", "name": "_renderShowMoreButton", "privacy": "private"}, {"kind": "field", "name": "_renderItems", "privacy": "private"}], "attributes": [{"name": "itemsSize", "type": {"text": "number"}, "default": "5", "fieldName": "itemsSize"}, {"name": "trim", "type": {"text": "boolean"}, "default": "false", "fieldName": "trim"}, {"name": "isExpanded", "type": {"text": "boolean"}, "default": "false", "fieldName": "isExpanded"}, {"name": "expandedText", "type": {"text": "string"}, "default": "''", "fieldName": "expandedText"}, {"name": "unexpandedText", "type": {"text": "string"}, "default": "''", "fieldName": "unexpandedText"}, {"name": "items", "type": {"text": "DataListItem[]"}, "default": "[]", "fieldName": "items"}], "superclass": {"name": "LitElement", "package": "lit"}, "tagName": "eds-data-list", "customElement": true}], "exports": [{"kind": "custom-element-definition", "name": "eds-data-list", "declaration": {"name": "DataList", "module": "src/data-list/index.ts"}}, {"kind": "js", "name": "DataList", "declaration": {"name": "DataList", "module": "src/data-list/index.ts"}}]}, {"kind": "javascript-module", "path": "src/date-picker/index.ts", "declarations": [{"kind": "class", "description": "", "name": "DatePicker", "members": [{"kind": "field", "name": "id", "type": {"text": "string"}, "default": "'datepickerID'", "attribute": "id"}, {"kind": "field", "name": "classes", "type": {"text": "string"}, "default": "''", "attribute": "classes"}, {"kind": "field", "name": "inline", "type": {"text": "boolean"}, "default": "false", "attribute": "inline"}, {"kind": "field", "name": "locale", "type": {"text": "DatePickerLocale"}, "default": "'tr'", "attribute": "locale"}, {"kind": "field", "name": "startDate", "type": {"text": "string"}, "default": "''", "attribute": "startDate"}, {"kind": "field", "name": "firstDay", "type": {"text": "number"}, "default": "0", "attribute": "firstDay"}, {"kind": "field", "name": "weekends", "type": {"text": "[number, number]"}, "default": "[6, 0]", "attribute": "weekends"}, {"kind": "field", "name": "isMobile", "type": {"text": "boolean"}, "default": "false", "attribute": "isMobile"}, {"kind": "field", "name": "visible", "type": {"text": "boolean"}, "default": "false", "attribute": "visible"}, {"kind": "field", "name": "dateFormat", "type": {"text": "string"}, "default": "'dd.<PERSON><PERSON><PERSON>yyyy'", "attribute": "dateFormat"}, {"kind": "field", "name": "altField", "type": {"text": "string"}, "default": "''", "attribute": "altField"}, {"kind": "field", "name": "altFieldDateFormat", "type": {"text": "string"}, "default": "'T'", "attribute": "altFieldDateFormat"}, {"kind": "field", "name": "toggleSelected", "type": {"text": "boolean"}, "default": "true", "attribute": "toggleSelected"}, {"kind": "field", "name": "keyboardNav", "type": {"text": "boolean"}, "default": "true", "attribute": "keyboardNav"}, {"kind": "field", "name": "selectedDates", "type": {"text": "array"}, "default": "[]", "attribute": "selectedDates"}, {"kind": "field", "name": "container", "type": {"text": "string"}, "default": "''", "attribute": "container"}, {"kind": "field", "name": "position", "type": {"text": "DatePickerPosition"}, "default": "'bottom left'", "attribute": "position"}, {"kind": "field", "name": "view", "type": {"text": "DatePickerView"}, "default": "'days'", "attribute": "view"}, {"kind": "field", "name": "minView", "type": {"text": "DatePickerView"}, "default": "'days'", "attribute": "minView"}, {"kind": "field", "name": "showOtherMonths", "type": {"text": "boolean"}, "default": "true", "attribute": "showOtherMonths"}, {"kind": "field", "name": "selectOtherMonths", "type": {"text": "boolean"}, "default": "true", "attribute": "selectOtherMonths"}, {"kind": "field", "name": "moveToOtherMonthsOnSelect", "type": {"text": "boolean"}, "default": "true", "attribute": "moveToOtherMonthsOnSelect"}, {"kind": "field", "name": "minDate", "type": {"text": "string"}, "default": "''", "attribute": "minDate"}, {"kind": "field", "name": "maxDate", "type": {"text": "string"}, "default": "''", "attribute": "maxDate"}, {"kind": "field", "name": "disableNavWhenOutOfRange", "type": {"text": "boolean"}, "default": "true", "attribute": "disableNavWhenOutOfRange"}, {"kind": "field", "name": "multipleDates", "type": {"text": "boolean"}, "default": "false", "attribute": "multipleDates"}, {"kind": "field", "name": "multipleDatesSeparator", "type": {"text": "string"}, "default": "', '", "attribute": "multipleDatesSeparator"}, {"kind": "field", "name": "range", "type": {"text": "boolean"}, "default": "false", "attribute": "range"}, {"kind": "field", "name": "dynamicRange", "type": {"text": "boolean"}, "default": "true", "attribute": "dynamicRange"}, {"kind": "field", "name": "buttons", "type": {"text": "AirDatepickerButton[]"}, "default": "[]", "attribute": "buttons"}, {"kind": "field", "name": "<PERSON><PERSON>ield", "type": {"text": "string"}, "default": "'monthsShort'", "attribute": "<PERSON><PERSON>ield"}, {"kind": "field", "name": "showEvent", "type": {"text": "string"}, "default": "'focus'", "attribute": "showEvent"}, {"kind": "field", "name": "autoClose", "type": {"text": "boolean"}, "default": "true", "attribute": "autoClose"}, {"kind": "field", "name": "navTitles", "type": {"text": "object"}, "default": "{ days: 'MMMM <i>yyyy</i>', months: 'yyyy', years: 'yyyy1 - yyyy2' }", "attribute": "navTitles"}, {"kind": "field", "name": "fixedHeight", "type": {"text": "boolean"}, "default": "false", "attribute": "fixedHeight"}, {"kind": "field", "name": "targetId", "type": {"text": "string"}, "default": "''", "attribute": "targetId"}, {"kind": "field", "name": "timepicker", "type": {"text": "boolean"}, "default": "false", "attribute": "timepicker"}, {"kind": "field", "name": "only<PERSON>imepic<PERSON>", "type": {"text": "boolean"}, "default": "false", "attribute": "only<PERSON>imepic<PERSON>"}, {"kind": "field", "name": "dateTimeSeparator", "type": {"text": "string"}, "default": "' '", "attribute": "dateTimeSeparator"}, {"kind": "field", "name": "timeFormat", "type": {"text": "string"}, "default": "''", "attribute": "timeFormat"}, {"kind": "field", "name": "minHours", "type": {"text": "number"}, "default": "0", "attribute": "minHours"}, {"kind": "field", "name": "maxHours", "type": {"text": "number"}, "default": "24", "attribute": "maxHours"}, {"kind": "field", "name": "minMinutes", "type": {"text": "number"}, "default": "0", "attribute": "minMinutes"}, {"kind": "field", "name": "maxMinutes", "type": {"text": "number"}, "default": "59", "attribute": "maxMinutes"}, {"kind": "field", "name": "hoursStep", "type": {"text": "number"}, "default": "1", "attribute": "hoursStep"}, {"kind": "field", "name": "minutesStep", "type": {"text": "number"}, "default": "1", "attribute": "minutesStep"}, {"kind": "field", "name": "datepicker", "type": {"text": "AirDatepicker<HTMLElement> | null"}, "privacy": "private", "default": "null"}, {"kind": "method", "name": "getDatePickerOptions", "privacy": "private", "return": {"type": {"text": "AirDatepickerOptions<HTMLElement>"}}}], "events": [{"name": "date-select", "type": {"text": "CustomEvent"}}], "attributes": [{"name": "id", "type": {"text": "string"}, "default": "'datepickerID'", "fieldName": "id"}, {"name": "classes", "type": {"text": "string"}, "default": "''", "fieldName": "classes"}, {"name": "inline", "type": {"text": "boolean"}, "default": "false", "fieldName": "inline"}, {"name": "locale", "type": {"text": "DatePickerLocale"}, "default": "'tr'", "fieldName": "locale"}, {"name": "startDate", "type": {"text": "string"}, "default": "''", "fieldName": "startDate"}, {"name": "firstDay", "type": {"text": "number"}, "default": "0", "fieldName": "firstDay"}, {"name": "weekends", "type": {"text": "[number, number]"}, "default": "[6, 0]", "fieldName": "weekends"}, {"name": "isMobile", "type": {"text": "boolean"}, "default": "false", "fieldName": "isMobile"}, {"name": "visible", "type": {"text": "boolean"}, "default": "false", "fieldName": "visible"}, {"name": "dateFormat", "type": {"text": "string"}, "default": "'dd.<PERSON><PERSON><PERSON>yyyy'", "fieldName": "dateFormat"}, {"name": "altField", "type": {"text": "string"}, "default": "''", "fieldName": "altField"}, {"name": "altFieldDateFormat", "type": {"text": "string"}, "default": "'T'", "fieldName": "altFieldDateFormat"}, {"name": "toggleSelected", "type": {"text": "boolean"}, "default": "true", "fieldName": "toggleSelected"}, {"name": "keyboardNav", "type": {"text": "boolean"}, "default": "true", "fieldName": "keyboardNav"}, {"name": "selectedDates", "type": {"text": "array"}, "default": "[]", "fieldName": "selectedDates"}, {"name": "container", "type": {"text": "string"}, "default": "''", "fieldName": "container"}, {"name": "position", "type": {"text": "DatePickerPosition"}, "default": "'bottom left'", "fieldName": "position"}, {"name": "view", "type": {"text": "DatePickerView"}, "default": "'days'", "fieldName": "view"}, {"name": "minView", "type": {"text": "DatePickerView"}, "default": "'days'", "fieldName": "minView"}, {"name": "showOtherMonths", "type": {"text": "boolean"}, "default": "true", "fieldName": "showOtherMonths"}, {"name": "selectOtherMonths", "type": {"text": "boolean"}, "default": "true", "fieldName": "selectOtherMonths"}, {"name": "moveToOtherMonthsOnSelect", "type": {"text": "boolean"}, "default": "true", "fieldName": "moveToOtherMonthsOnSelect"}, {"name": "minDate", "type": {"text": "string"}, "default": "''", "fieldName": "minDate"}, {"name": "maxDate", "type": {"text": "string"}, "default": "''", "fieldName": "maxDate"}, {"name": "disableNavWhenOutOfRange", "type": {"text": "boolean"}, "default": "true", "fieldName": "disableNavWhenOutOfRange"}, {"name": "multipleDates", "type": {"text": "boolean"}, "default": "false", "fieldName": "multipleDates"}, {"name": "multipleDatesSeparator", "type": {"text": "string"}, "default": "', '", "fieldName": "multipleDatesSeparator"}, {"name": "range", "type": {"text": "boolean"}, "default": "false", "fieldName": "range"}, {"name": "dynamicRange", "type": {"text": "boolean"}, "default": "true", "fieldName": "dynamicRange"}, {"name": "buttons", "type": {"text": "AirDatepickerButton[]"}, "default": "[]", "fieldName": "buttons"}, {"name": "<PERSON><PERSON>ield", "type": {"text": "string"}, "default": "'monthsShort'", "fieldName": "<PERSON><PERSON>ield"}, {"name": "showEvent", "type": {"text": "string"}, "default": "'focus'", "fieldName": "showEvent"}, {"name": "autoClose", "type": {"text": "boolean"}, "default": "true", "fieldName": "autoClose"}, {"name": "navTitles", "type": {"text": "object"}, "default": "{ days: 'MMMM <i>yyyy</i>', months: 'yyyy', years: 'yyyy1 - yyyy2' }", "fieldName": "navTitles"}, {"name": "fixedHeight", "type": {"text": "boolean"}, "default": "false", "fieldName": "fixedHeight"}, {"name": "targetId", "type": {"text": "string"}, "default": "''", "fieldName": "targetId"}, {"name": "timepicker", "type": {"text": "boolean"}, "default": "false", "fieldName": "timepicker"}, {"name": "only<PERSON>imepic<PERSON>", "type": {"text": "boolean"}, "default": "false", "fieldName": "only<PERSON>imepic<PERSON>"}, {"name": "dateTimeSeparator", "type": {"text": "string"}, "default": "' '", "fieldName": "dateTimeSeparator"}, {"name": "timeFormat", "type": {"text": "string"}, "default": "''", "fieldName": "timeFormat"}, {"name": "minHours", "type": {"text": "number"}, "default": "0", "fieldName": "minHours"}, {"name": "maxHours", "type": {"text": "number"}, "default": "24", "fieldName": "maxHours"}, {"name": "minMinutes", "type": {"text": "number"}, "default": "0", "fieldName": "minMinutes"}, {"name": "maxMinutes", "type": {"text": "number"}, "default": "59", "fieldName": "maxMinutes"}, {"name": "hoursStep", "type": {"text": "number"}, "default": "1", "fieldName": "hoursStep"}, {"name": "minutesStep", "type": {"text": "number"}, "default": "1", "fieldName": "minutesStep"}], "superclass": {"name": "LitElement", "package": "lit"}, "tagName": "eds-date-picker", "customElement": true}], "exports": [{"kind": "custom-element-definition", "name": "eds-date-picker", "declaration": {"name": "DatePicker", "module": "src/date-picker/index.ts"}}, {"kind": "js", "name": "DatePicker", "declaration": {"name": "DatePicker", "module": "src/date-picker/index.ts"}}]}, {"kind": "javascript-module", "path": "src/form-field/index.ts", "declarations": [{"kind": "class", "description": "", "name": "FormField", "members": [{"kind": "field", "name": "label", "type": {"text": "string"}, "default": "''", "attribute": "label"}, {"kind": "field", "name": "helperText", "type": {"text": "string"}, "default": "''", "attribute": "helperText"}, {"kind": "field", "name": "errorMessage", "type": {"text": "string"}, "default": "''", "attribute": "errorMessage"}, {"kind": "field", "name": "successMessage", "type": {"text": "string"}, "default": "''", "attribute": "successMessage"}, {"kind": "field", "name": "slotted<PERSON>ls", "type": {"text": "Array<FormFieldElement>"}, "privacy": "private"}, {"kind": "field", "name": "slottedEl", "type": {"text": "FormFieldElement"}, "privacy": "private", "default": "this.slottedEls[0]"}, {"kind": "field", "name": "slottedElId", "type": {"text": "string"}, "privacy": "private", "default": "''"}, {"kind": "field", "name": "isRequired", "type": {"text": "boolean"}, "privacy": "private", "default": "false"}, {"kind": "field", "name": "isInvalid", "type": {"text": "boolean"}, "privacy": "private", "default": "false"}, {"kind": "field", "name": "_renderErrorMessage", "privacy": "private"}, {"kind": "field", "name": "_renderSuccessMessage", "privacy": "private"}, {"kind": "field", "name": "_handleLabelClick", "privacy": "private"}], "attributes": [{"name": "label", "type": {"text": "string"}, "default": "''", "fieldName": "label"}, {"name": "helperText", "type": {"text": "string"}, "default": "''", "fieldName": "helperText"}, {"name": "errorMessage", "type": {"text": "string"}, "default": "''", "fieldName": "errorMessage"}, {"name": "successMessage", "type": {"text": "string"}, "default": "''", "fieldName": "successMessage"}], "superclass": {"name": "LitElement", "package": "lit"}, "tagName": "eds-form-field", "customElement": true}], "exports": [{"kind": "custom-element-definition", "name": "eds-form-field", "declaration": {"name": "FormField", "module": "src/form-field/index.ts"}}, {"kind": "js", "name": "FormField", "declaration": {"name": "FormField", "module": "src/form-field/index.ts"}}]}, {"kind": "javascript-module", "path": "src/heading/index.ts", "declarations": [{"kind": "class", "description": "", "name": "Heading", "members": [{"kind": "field", "name": "as", "type": {"text": "HeadingAsValues"}, "default": "'h1'", "attribute": "as"}, {"kind": "field", "name": "text", "type": {"text": "string"}, "default": "''", "attribute": "text"}, {"kind": "field", "name": "size", "type": {"text": "string"}, "default": "''", "attribute": "size"}, {"kind": "field", "name": "tag", "privacy": "private"}, {"kind": "field", "name": "template", "privacy": "private"}], "attributes": [{"name": "as", "type": {"text": "HeadingAsValues"}, "default": "'h1'", "fieldName": "as"}, {"name": "text", "type": {"text": "string"}, "default": "''", "fieldName": "text"}, {"name": "size", "type": {"text": "string"}, "default": "''", "fieldName": "size"}], "superclass": {"name": "LitElement", "package": "lit"}, "tagName": "eds-heading", "customElement": true}], "exports": [{"kind": "custom-element-definition", "name": "eds-heading", "declaration": {"name": "Heading", "module": "src/heading/index.ts"}}, {"kind": "js", "name": "Heading", "declaration": {"name": "Heading", "module": "src/heading/index.ts"}}]}, {"kind": "javascript-module", "path": "src/icons/index.ts", "declarations": [{"kind": "class", "description": "", "name": "Icon", "members": [{"kind": "field", "name": "name", "type": {"text": "string"}, "default": "'mail'", "attribute": "name"}], "attributes": [{"name": "name", "type": {"text": "string"}, "default": "'mail'", "fieldName": "name"}], "superclass": {"name": "LitElement", "package": "lit"}, "tagName": "eds-icon", "customElement": true}], "exports": [{"kind": "custom-element-definition", "name": "eds-icon", "declaration": {"name": "Icon", "module": "src/icons/index.ts"}}, {"kind": "js", "name": "Icon", "declaration": {"name": "Icon", "module": "src/icons/index.ts"}}, {"kind": "js", "name": "Icons", "declaration": {"name": "Icons", "module": "src/icons/index.ts"}}]}, {"kind": "javascript-module", "path": "src/image/index.ts", "declarations": [{"kind": "class", "description": "", "name": "Image", "members": [{"kind": "field", "name": "src", "type": {"text": "string"}, "default": "''", "attribute": "src"}, {"kind": "field", "name": "alt", "type": {"text": "string"}, "default": "''", "attribute": "alt"}, {"kind": "field", "name": "width", "type": {"text": "string"}, "default": "''", "attribute": "width"}, {"kind": "field", "name": "height", "type": {"text": "string"}, "default": "''", "attribute": "height"}, {"kind": "field", "name": "fallbackSrc", "type": {"text": "string"}, "default": "''", "attribute": "fallbackSrc"}, {"kind": "field", "name": "imgElement", "type": {"text": "HTMLImageElement"}}, {"kind": "field", "name": "handleError", "privacy": "private"}], "attributes": [{"name": "src", "type": {"text": "string"}, "default": "''", "fieldName": "src"}, {"name": "alt", "type": {"text": "string"}, "default": "''", "fieldName": "alt"}, {"name": "width", "type": {"text": "string"}, "default": "''", "fieldName": "width"}, {"name": "height", "type": {"text": "string"}, "default": "''", "fieldName": "height"}, {"name": "fallbackSrc", "type": {"text": "string"}, "default": "''", "fieldName": "fallbackSrc"}], "superclass": {"name": "LitElement", "package": "lit"}, "tagName": "eds-image", "customElement": true}], "exports": [{"kind": "custom-element-definition", "name": "eds-image", "declaration": {"name": "Image", "module": "src/image/index.ts"}}, {"kind": "js", "name": "Image", "declaration": {"name": "Image", "module": "src/image/index.ts"}}]}, {"kind": "javascript-module", "path": "src/link/index.ts", "declarations": [{"kind": "class", "description": "", "name": "Link", "members": [{"kind": "field", "name": "href", "type": {"text": "string"}, "default": "''", "attribute": "href"}, {"kind": "field", "name": "target", "type": {"text": "HTMLAnchorElement['target']"}, "default": "'_self'", "attribute": "target"}, {"kind": "field", "name": "isDisabled", "type": {"text": "boolean"}, "default": "false", "attribute": "disabled", "reflects": true}, {"kind": "field", "name": "link", "type": {"text": "HTMLAnchorElement"}}, {"kind": "field", "name": "_onClick", "privacy": "private"}], "attributes": [{"name": "href", "type": {"text": "string"}, "default": "''", "fieldName": "href"}, {"name": "target", "type": {"text": "HTMLAnchorElement['target']"}, "default": "'_self'", "fieldName": "target"}, {"name": "disabled", "type": {"text": "boolean"}, "default": "false", "fieldName": "isDisabled"}], "superclass": {"name": "LitElement", "package": "lit"}, "tagName": "eds-link", "customElement": true}], "exports": [{"kind": "custom-element-definition", "name": "eds-link", "declaration": {"name": "Link", "module": "src/link/index.ts"}}, {"kind": "js", "name": "Link", "declaration": {"name": "Link", "module": "src/link/index.ts"}}]}, {"kind": "javascript-module", "path": "src/media-object/index.ts", "declarations": [{"kind": "class", "description": "", "name": "MediaObject", "members": [{"kind": "field", "name": "src", "type": {"text": "string"}, "default": "''", "attribute": "src"}, {"kind": "field", "name": "upperText", "type": {"text": "string"}, "default": "''", "attribute": "upperText"}, {"kind": "field", "name": "text", "type": {"text": "string"}, "default": "''", "attribute": "text"}, {"kind": "field", "name": "description", "type": {"text": "string"}, "default": "''", "attribute": "description"}, {"kind": "field", "name": "iconName", "type": {"text": "string"}, "default": "''", "attribute": "iconName"}, {"kind": "field", "name": "_renderMedia", "privacy": "private"}], "attributes": [{"name": "src", "type": {"text": "string"}, "default": "''", "fieldName": "src"}, {"name": "upperText", "type": {"text": "string"}, "default": "''", "fieldName": "upperText"}, {"name": "text", "type": {"text": "string"}, "default": "''", "fieldName": "text"}, {"name": "description", "type": {"text": "string"}, "default": "''", "fieldName": "description"}, {"name": "iconName", "type": {"text": "string"}, "default": "''", "fieldName": "iconName"}], "superclass": {"name": "LitElement", "package": "lit"}, "tagName": "eds-media-object", "customElement": true}], "exports": [{"kind": "custom-element-definition", "name": "eds-media-object", "declaration": {"name": "MediaObject", "module": "src/media-object/index.ts"}}, {"kind": "js", "name": "MediaObject", "declaration": {"name": "MediaObject", "module": "src/media-object/index.ts"}}]}, {"kind": "javascript-module", "path": "src/phone-number/index.ts", "declarations": [{"kind": "class", "description": "", "name": "PhoneNumber", "members": [{"kind": "field", "name": "id", "type": {"text": "string"}, "default": "''", "attribute": "id"}, {"kind": "field", "name": "appearance", "type": {"text": "PhoneNumberAppearanceValues"}, "default": "''", "attribute": "appearance"}, {"kind": "field", "name": "phoneNumberValue", "type": {"text": "string"}, "default": "''", "attribute": "phoneNumberValue"}, {"kind": "field", "name": "countryCodeValue", "type": {"text": "string"}, "default": "''", "attribute": "countryCodeValue"}, {"kind": "field", "name": "countryCodePlaceholder", "type": {"text": "string"}, "default": "''", "attribute": "countryCodePlaceholder"}, {"kind": "field", "name": "phoneNumberPlaceholder", "type": {"text": "string"}, "default": "''", "attribute": "phoneNumberPlaceholder"}, {"kind": "field", "name": "isInvalid", "type": {"text": "boolean"}, "default": "false", "attribute": "isInvalid"}, {"kind": "field", "name": "isDisabled", "type": {"text": "boolean"}, "default": "false", "attribute": "isDisabled"}, {"kind": "field", "name": "isRequired", "type": {"text": "boolean"}, "default": "false", "attribute": "isRequired"}, {"kind": "field", "name": "isCompact", "type": {"text": "boolean"}, "default": "false", "attribute": "isCompact"}, {"kind": "field", "name": "countryOptions", "type": {"text": "countryOptions[]"}, "default": "[]", "attribute": "countryOptions"}], "attributes": [{"name": "id", "type": {"text": "string"}, "default": "''", "fieldName": "id"}, {"name": "appearance", "type": {"text": "PhoneNumberAppearanceValues"}, "default": "''", "fieldName": "appearance"}, {"name": "phoneNumberValue", "type": {"text": "string"}, "default": "''", "fieldName": "phoneNumberValue"}, {"name": "countryCodeValue", "type": {"text": "string"}, "default": "''", "fieldName": "countryCodeValue"}, {"name": "countryCodePlaceholder", "type": {"text": "string"}, "default": "''", "fieldName": "countryCodePlaceholder"}, {"name": "phoneNumberPlaceholder", "type": {"text": "string"}, "default": "''", "fieldName": "phoneNumberPlaceholder"}, {"name": "isInvalid", "type": {"text": "boolean"}, "default": "false", "fieldName": "isInvalid"}, {"name": "isDisabled", "type": {"text": "boolean"}, "default": "false", "fieldName": "isDisabled"}, {"name": "isRequired", "type": {"text": "boolean"}, "default": "false", "fieldName": "isRequired"}, {"name": "isCompact", "type": {"text": "boolean"}, "default": "false", "fieldName": "isCompact"}, {"name": "countryOptions", "type": {"text": "countryOptions[]"}, "default": "[]", "fieldName": "countryOptions"}], "superclass": {"name": "LitElement", "package": "lit"}, "tagName": "eds-phone-number", "customElement": true}], "exports": [{"kind": "custom-element-definition", "name": "eds-progress-bar", "declaration": {"name": "ProgressBar", "module": "src/progress-bar/index.ts"}}, {"kind": "js", "name": "ProgressBar", "declaration": {"name": "ProgressBar", "module": "src/progress-bar/index.ts"}}]}, {"kind": "javascript-module", "path": "src/progress-bar/index.ts", "declarations": [{"kind": "class", "description": "", "name": "ProgressBar", "members": [{"kind": "field", "name": "label", "type": {"text": "string"}, "default": "''", "attribute": "label"}, {"kind": "field", "name": "helperText", "type": {"text": "string"}, "default": "''", "attribute": "helperText"}, {"kind": "field", "name": "value", "type": {"text": "number"}, "default": "25", "attribute": "value"}, {"kind": "field", "name": "maxValue", "type": {"text": "number"}, "default": "100", "attribute": "maxValue"}, {"kind": "field", "name": "valueType", "type": {"text": "string"}, "default": "''", "attribute": "valueType"}, {"kind": "field", "name": "isIndeterminate", "type": {"text": "boolean"}, "default": "false", "attribute": "isIndeterminate"}, {"kind": "field", "name": "isAnimated", "type": {"text": "boolean"}, "default": "false", "attribute": "isAnimated"}, {"kind": "field", "name": "isUnlimited", "type": {"text": "boolean"}, "default": "false", "attribute": "isUnlimited"}, {"kind": "field", "name": "type", "type": {"text": "ProgressBarTypeValues"}, "default": "'bar'", "attribute": "type"}, {"kind": "field", "name": "circleProperties", "type": {"text": "CircleProperties"}, "default": "{ default: 0, breakpoints: {}, }", "attribute": "circleProperties"}, {"kind": "field", "name": "circleIndicator", "type": {"text": "SVGPathElement"}, "privacy": "private"}, {"kind": "field", "name": "barIndicator", "type": {"text": "HTMLDivElement"}, "privacy": "private"}, {"kind": "field", "name": "currentDiameter", "type": {"text": "number"}, "privacy": "private", "default": "0"}, {"kind": "field", "name": "resizeObserver", "type": {"text": "ResizeObserver"}, "privacy": "private", "readonly": true, "default": "new ResizeObserver(this._handleResize)"}, {"kind": "field", "name": "_handleResize", "privacy": "private", "readonly": true}, {"kind": "method", "name": "_updateCircleDiameter", "privacy": "private", "return": {"type": {"text": "void"}}}, {"kind": "method", "name": "_getPercentage", "privacy": "private", "return": {"type": {"text": "number"}}}, {"kind": "method", "name": "_getDefaultSize", "privacy": "private", "return": {"type": {"text": "number"}}}, {"kind": "field", "name": "barThickness", "type": {"text": "number"}, "privacy": "private", "readonly": true}, {"kind": "field", "name": "circleSize", "type": {"text": "number"}, "privacy": "private", "readonly": true}, {"kind": "field", "name": "radius", "type": {"text": "number"}, "privacy": "private", "readonly": true}, {"kind": "field", "name": "circumference", "type": {"text": "number"}, "privacy": "private", "readonly": true}, {"kind": "field", "name": "offset", "type": {"text": "number"}, "privacy": "private", "readonly": true}, {"kind": "method", "name": "_updateIndicator", "privacy": "private", "return": {"type": {"text": "void"}}}, {"kind": "field", "name": "_renderUsage", "privacy": "private"}, {"kind": "field", "name": "_renderLabel", "privacy": "private"}, {"kind": "field", "name": "_renderHelperText", "privacy": "private"}, {"kind": "field", "name": "_renderBar", "privacy": "private"}, {"kind": "field", "name": "_renderCircle", "privacy": "private"}], "attributes": [{"name": "label", "type": {"text": "string"}, "default": "''", "fieldName": "label"}, {"name": "helperText", "type": {"text": "string"}, "default": "''", "fieldName": "helperText"}, {"name": "value", "type": {"text": "number"}, "default": "25", "fieldName": "value"}, {"name": "maxValue", "type": {"text": "number"}, "default": "100", "fieldName": "maxValue"}, {"name": "valueType", "type": {"text": "string"}, "default": "''", "fieldName": "valueType"}, {"name": "isIndeterminate", "type": {"text": "boolean"}, "default": "false", "fieldName": "isIndeterminate"}, {"name": "isAnimated", "type": {"text": "boolean"}, "default": "false", "fieldName": "isAnimated"}, {"name": "isUnlimited", "type": {"text": "boolean"}, "default": "false", "fieldName": "isUnlimited"}, {"name": "type", "type": {"text": "ProgressBarTypeValues"}, "default": "'bar'", "fieldName": "type"}, {"name": "circleProperties", "type": {"text": "CircleProperties"}, "default": "{ default: 0, breakpoints: {}, }", "fieldName": "circleProperties"}], "superclass": {"name": "LitElement", "package": "lit"}, "tagName": "eds-progress-bar", "customElement": true}], "exports": [{"kind": "custom-element-definition", "name": "eds-progress-bar", "declaration": {"name": "ProgressBar", "module": "src/progress-bar/index.ts"}}, {"kind": "js", "name": "ProgressBar", "declaration": {"name": "ProgressBar", "module": "src/progress-bar/index.ts"}}]}, {"kind": "javascript-module", "path": "src/progress-tracker/index.ts", "declarations": [{"kind": "class", "description": "", "name": "ProgressTracker", "members": [{"kind": "field", "name": "orientation", "type": {"text": "ProgressTrackerOrientationValues"}, "default": "'horizontal'", "attribute": "orientation"}, {"kind": "field", "name": "items", "type": {"text": "ProgressTrackerItem[]"}, "default": "[]", "attribute": "items"}], "attributes": [{"name": "orientation", "type": {"text": "ProgressTrackerOrientationValues"}, "default": "'horizontal'", "fieldName": "orientation"}, {"name": "items", "type": {"text": "ProgressTrackerItem[]"}, "default": "[]", "fieldName": "items"}], "superclass": {"name": "LitElement", "package": "lit"}, "tagName": "eds-progress-tracker", "customElement": true}], "exports": [{"kind": "custom-element-definition", "name": "eds-progress-tracker", "declaration": {"name": "ProgressTracker", "module": "src/progress-tracker/index.ts"}}, {"kind": "js", "name": "ProgressTracker", "declaration": {"name": "ProgressTracker", "module": "src/progress-tracker/index.ts"}}]}, {"kind": "javascript-module", "path": "src/radio/index.ts", "declarations": [{"kind": "class", "description": "", "name": "Radio", "members": [{"kind": "field", "name": "id", "type": {"text": "string"}, "default": "'label1'", "attribute": "id"}, {"kind": "field", "name": "name", "type": {"text": "string"}, "default": "'label1'", "attribute": "name"}, {"kind": "field", "name": "value", "type": {"text": "string"}, "default": "'label1'", "attribute": "value"}, {"kind": "field", "name": "label", "type": {"text": "string"}, "default": "'Label'", "attribute": "label"}, {"kind": "field", "name": "isChecked", "type": {"text": "boolean"}, "default": "false", "attribute": "isChecked", "reflects": true}, {"kind": "field", "name": "isDisabled", "type": {"text": "boolean"}, "default": "false", "attribute": "isDisabled", "reflects": true}, {"kind": "field", "name": "isInvalid", "type": {"text": "boolean"}, "default": "false", "attribute": "isInvalid", "reflects": true}, {"kind": "field", "name": "isRequired", "type": {"text": "boolean"}, "default": "false", "attribute": "isRequired", "reflects": true}, {"kind": "field", "name": "slotElement", "type": {"text": "HTMLSlotElement"}}, {"kind": "field", "name": "_handleChange", "privacy": "private"}, {"kind": "field", "name": "_addLabelListeners", "privacy": "private"}, {"kind": "field", "name": "_handleLabelClick", "privacy": "private"}, {"kind": "field", "name": "_renderCheckbox", "privacy": "private"}], "attributes": [{"name": "id", "type": {"text": "string"}, "default": "'label1'", "fieldName": "id"}, {"name": "name", "type": {"text": "string"}, "default": "'label1'", "fieldName": "name"}, {"name": "value", "type": {"text": "string"}, "default": "'label1'", "fieldName": "value"}, {"name": "label", "type": {"text": "string"}, "default": "'Label'", "fieldName": "label"}, {"name": "isChecked", "type": {"text": "boolean"}, "default": "false", "fieldName": "isChecked"}, {"name": "isDisabled", "type": {"text": "boolean"}, "default": "false", "fieldName": "isDisabled"}, {"name": "isInvalid", "type": {"text": "boolean"}, "default": "false", "fieldName": "isInvalid"}, {"name": "isRequired", "type": {"text": "boolean"}, "default": "false", "fieldName": "isRequired"}], "superclass": {"name": "LitElement", "package": "lit"}, "tagName": "eds-radio", "customElement": true}], "exports": [{"kind": "custom-element-definition", "name": "eds-radio", "declaration": {"name": "Radio", "module": "src/radio/index.ts"}}, {"kind": "js", "name": "Radio", "declaration": {"name": "Radio", "module": "src/radio/index.ts"}}]}, {"kind": "javascript-module", "path": "src/radio-group/index.ts", "declarations": [{"kind": "class", "description": "", "name": "RadioGroup", "members": [{"kind": "field", "name": "name", "type": {"text": "string"}, "default": "'radio'", "attribute": "name"}, {"kind": "field", "name": "isRequired", "type": {"text": "boolean"}, "default": "false", "attribute": "isRequired", "reflects": true}, {"kind": "field", "name": "radioElements", "type": {"text": "Radio[]"}}, {"kind": "field", "name": "_handleChange", "privacy": "private"}], "attributes": [{"name": "name", "type": {"text": "string"}, "default": "'radio'", "fieldName": "name"}, {"name": "isRequired", "type": {"text": "boolean"}, "default": "false", "fieldName": "isRequired"}], "superclass": {"name": "LitElement", "package": "lit"}, "tagName": "eds-radio-group", "customElement": true}], "exports": [{"kind": "custom-element-definition", "name": "eds-radio-group", "declaration": {"name": "RadioGroup", "module": "src/radio-group/index.ts"}}, {"kind": "js", "name": "RadioGroup", "declaration": {"name": "RadioGroup", "module": "src/radio-group/index.ts"}}]}, {"kind": "javascript-module", "path": "src/segmented-control/index.ts", "declarations": [{"kind": "class", "description": "", "name": "SegmentedControl", "members": [{"kind": "field", "name": "isDisabled", "type": {"text": "boolean"}, "default": "false", "attribute": "isDisabled"}, {"kind": "field", "name": "options", "type": {"text": "SegmentedOption[]"}, "default": "[]", "attribute": "options"}, {"kind": "field", "name": "segmentedButtonsEl", "type": {"text": "SegmentedButton[]"}}, {"kind": "field", "name": "_renderOptions", "privacy": "private"}, {"kind": "field", "name": "_handleSegmentClick", "privacy": "private"}], "attributes": [{"name": "isDisabled", "type": {"text": "boolean"}, "default": "false", "fieldName": "isDisabled"}, {"name": "options", "type": {"text": "SegmentedOption[]"}, "default": "[]", "fieldName": "options"}], "superclass": {"name": "LitElement", "package": "lit"}, "tagName": "eds-segmented-control", "customElement": true}], "exports": [{"kind": "custom-element-definition", "name": "eds-segmented-control", "declaration": {"name": "SegmentedControl", "module": "src/segmented-control/index.ts"}}, {"kind": "js", "name": "SegmentedControl", "declaration": {"name": "SegmentedControl", "module": "src/segmented-control/index.ts"}}]}, {"kind": "javascript-module", "path": "src/select/index.ts", "declarations": [{"kind": "class", "description": "", "name": "Select", "members": [{"kind": "field", "name": "id", "type": {"text": "string"}, "default": "''", "attribute": "id"}, {"kind": "field", "name": "appearance", "type": {"text": "SelectAppearanceValues"}, "default": "'default'", "attribute": "appearance"}, {"kind": "field", "name": "name", "type": {"text": "string"}, "default": "''", "attribute": "name"}, {"kind": "field", "name": "value", "type": {"text": "string"}, "default": "''", "attribute": "value"}, {"kind": "field", "name": "placeholder", "type": {"text": "string"}, "default": "''", "attribute": "placeholder"}, {"kind": "field", "name": "iconName", "type": {"text": "string"}, "default": "''", "attribute": "iconName"}, {"kind": "field", "name": "isRequired", "type": {"text": "boolean"}, "default": "false", "attribute": "isRequired"}, {"kind": "field", "name": "isCompact", "type": {"text": "boolean"}, "default": "false", "attribute": "isCompact"}, {"kind": "field", "name": "isInvalid", "type": {"text": "boolean"}, "default": "false", "attribute": "isInvalid"}, {"kind": "field", "name": "isMultiple", "type": {"text": "boolean"}, "default": "false", "attribute": "isMultiple"}, {"kind": "field", "name": "showTags", "type": {"text": "boolean"}, "default": "false", "attribute": "showTags"}, {"kind": "field", "name": "isDisabled", "type": {"text": "boolean"}, "default": "false", "attribute": "isDisabled", "reflects": true}, {"kind": "field", "name": "isLoading", "type": {"text": "boolean"}, "default": "false", "attribute": "isLoading", "reflects": true}, {"kind": "field", "name": "isSearchable", "type": {"text": "boolean"}, "default": "false", "attribute": "isSearchable", "reflects": true}, {"kind": "field", "name": "isOpen", "type": {"text": "boolean"}, "privacy": "private", "default": "false"}, {"kind": "field", "name": "allOptions", "type": {"text": "SelectOption[]"}, "privacy": "private", "default": "[]"}, {"kind": "field", "name": "hasOptions", "type": {"text": "boolean"}, "privacy": "private", "default": "false"}, {"kind": "field", "name": "selectedOptions", "type": {"text": "SelectOption[]"}, "privacy": "private", "default": "[]"}, {"kind": "field", "name": "hasUpdatedBefore", "type": {"text": "boolean"}, "privacy": "private", "default": "false"}, {"kind": "field", "name": "selectOptions", "type": {"text": "SelectOption[]"}}, {"kind": "field", "name": "input", "type": {"text": "HTMLInputElement"}}, {"kind": "field", "name": "optionsSlot", "type": {"text": "HTMLSlotElement"}}, {"kind": "field", "name": "inputWrapper", "type": {"text": "HTMLDivElement"}}, {"kind": "field", "name": "optionsContainer", "type": {"text": "HTMLDivElement"}}, {"kind": "field", "name": "_updateSelectedOptions", "privacy": "private"}, {"kind": "field", "name": "_inputFocus", "privacy": "private"}, {"kind": "field", "name": "_toggleSelect", "privacy": "private"}, {"kind": "field", "name": "_scrollToSelectedOption", "privacy": "private"}, {"kind": "field", "name": "_updateSelectValue", "privacy": "private"}, {"kind": "field", "name": "_selectOption", "privacy": "private"}, {"kind": "field", "name": "_filterSearch", "privacy": "private"}, {"kind": "field", "name": "_onInputChange", "privacy": "private"}, {"kind": "field", "name": "_updateSelect", "privacy": "private"}, {"kind": "field", "name": "_removeOption", "privacy": "private"}, {"kind": "field", "name": "_clearInput", "privacy": "private"}, {"kind": "field", "name": "_removeTag", "privacy": "private"}, {"kind": "field", "name": "_clearTags", "privacy": "private"}, {"kind": "field", "name": "_highlightNextOption", "privacy": "private"}, {"kind": "field", "name": "_highlightPreviousOption", "privacy": "private"}, {"kind": "field", "name": "_selectHighlightedOption", "privacy": "private"}, {"kind": "field", "name": "_selectPreviousOption", "privacy": "private"}, {"kind": "field", "name": "_selectNextOption", "privacy": "private"}, {"kind": "field", "name": "_onKeyDown", "privacy": "private"}, {"kind": "field", "name": "_renderTags", "privacy": "private"}, {"kind": "field", "name": "_renderClearTagsButton", "privacy": "private"}, {"kind": "field", "name": "_renderPlaceholder", "privacy": "private"}, {"kind": "field", "name": "_renderMultipleInput", "privacy": "private"}, {"kind": "field", "name": "_renderInput", "privacy": "private"}, {"kind": "field", "name": "_removeOptionFocus", "privacy": "private"}], "attributes": [{"name": "id", "type": {"text": "string"}, "default": "''", "fieldName": "id"}, {"name": "appearance", "type": {"text": "SelectAppearanceValues"}, "default": "'default'", "fieldName": "appearance"}, {"name": "name", "type": {"text": "string"}, "default": "''", "fieldName": "name"}, {"name": "value", "type": {"text": "string"}, "default": "''", "fieldName": "value"}, {"name": "placeholder", "type": {"text": "string"}, "default": "''", "fieldName": "placeholder"}, {"name": "iconName", "type": {"text": "string"}, "default": "''", "fieldName": "iconName"}, {"name": "isRequired", "type": {"text": "boolean"}, "default": "false", "fieldName": "isRequired"}, {"name": "isCompact", "type": {"text": "boolean"}, "default": "false", "fieldName": "isCompact"}, {"name": "isInvalid", "type": {"text": "boolean"}, "default": "false", "fieldName": "isInvalid"}, {"name": "isMultiple", "type": {"text": "boolean"}, "default": "false", "fieldName": "isMultiple"}, {"name": "showTags", "type": {"text": "boolean"}, "default": "false", "fieldName": "showTags"}, {"name": "isDisabled", "type": {"text": "boolean"}, "default": "false", "fieldName": "isDisabled"}, {"name": "isLoading", "type": {"text": "boolean"}, "default": "false", "fieldName": "isLoading"}, {"name": "isSearchable", "type": {"text": "boolean"}, "default": "false", "fieldName": "isSearchable"}], "superclass": {"name": "LitElement", "package": "lit"}, "tagName": "eds-select", "customElement": true}], "exports": [{"kind": "custom-element-definition", "name": "eds-select", "declaration": {"name": "Select", "module": "src/select/index.ts"}}, {"kind": "js", "name": "Select", "declaration": {"name": "Select", "module": "src/select/index.ts"}}]}, {"kind": "javascript-module", "path": "src/styles/index.ts", "declarations": [{"kind": "variable", "name": "globalStyles", "type": {"text": "CSSResultGroup"}, "default": "[ css` /* Box sizing rules */ :host *, :host *::before, :host *::after, :root *, :root *::before, :root *::after { box-sizing: border-box; } /* Make images easier to work with */ img, picture, :host img, :host picture { max-width: 100%; display: block; } /* Make sure textareas without a rows attribute are not tiny */ textarea:not([rows]), :host textarea:not([rows]) { min-height: 10em; } /* Anything that has been anchored to should have extra scroll margin */ :target, :host :target { scroll-margin-block: 5ex; } /* Button Reset */ button { all: unset; } `, typography, animations, tokens, primitives ]"}], "exports": [{"kind": "js", "name": "globalStyles", "declaration": {"name": "globalStyles", "module": "src/styles/index.ts"}}]}, {"kind": "javascript-module", "path": "src/tabs/index.ts", "declarations": [{"kind": "class", "description": "", "name": "Tabs", "members": [{"kind": "field", "name": "id", "type": {"text": "string"}, "default": "'uniqueId'", "attribute": "id"}, {"kind": "field", "name": "className", "type": {"text": "string"}, "default": "'custom-class'", "attribute": "className"}, {"kind": "field", "name": "navClass", "type": {"text": "string"}, "default": "'custom-class'", "attribute": "navClass"}, {"kind": "field", "name": "appearance", "type": {"text": "'primary' | 'secondary'"}, "default": "'primary'", "attribute": "appearance"}, {"kind": "field", "name": "tabListEl", "type": {"text": "HTMLDivElement"}}, {"kind": "field", "name": "tabEls", "type": {"text": "Tab[]"}}, {"kind": "field", "name": "tabPanelEls", "type": {"text": "TabPanel[]"}}, {"kind": "field", "name": "_showSelectedTabPanel", "privacy": "private"}, {"kind": "field", "name": "_scrollToSelectedTab", "privacy": "private"}, {"kind": "field", "name": "_handleClick", "privacy": "private"}], "attributes": [{"name": "id", "type": {"text": "string"}, "default": "'uniqueId'", "fieldName": "id"}, {"name": "className", "type": {"text": "string"}, "default": "'custom-class'", "fieldName": "className"}, {"name": "navClass", "type": {"text": "string"}, "default": "'custom-class'", "fieldName": "navClass"}, {"name": "appearance", "type": {"text": "'primary' | 'secondary'"}, "default": "'primary'", "fieldName": "appearance"}], "superclass": {"name": "LitElement", "package": "lit"}, "tagName": "eds-tabs", "customElement": true}], "exports": [{"kind": "custom-element-definition", "name": "eds-tabs", "declaration": {"name": "Tabs", "module": "src/tabs/index.ts"}}, {"kind": "js", "name": "Tabs", "declaration": {"name": "Tabs", "module": "src/tabs/index.ts"}}]}, {"kind": "javascript-module", "path": "src/tag/index.ts", "declarations": [{"kind": "class", "description": "", "name": "Tag", "members": [{"kind": "field", "name": "content", "type": {"text": "string"}, "default": "\"\"", "attribute": "content"}, {"kind": "field", "name": "appearance", "type": {"text": "TagAppearances"}, "default": "'grey'", "attribute": "appearance"}], "attributes": [{"name": "content", "type": {"text": "string"}, "default": "\"\"", "fieldName": "content"}, {"name": "appearance", "type": {"text": "TagAppearances"}, "default": "'grey'", "fieldName": "appearance"}], "superclass": {"name": "LitElement", "package": "lit"}, "tagName": "eds-tag", "customElement": true}], "exports": [{"kind": "custom-element-definition", "name": "eds-tag", "declaration": {"name": "Tag", "module": "src/tag/index.ts"}}, {"kind": "js", "name": "Tag", "declaration": {"name": "Tag", "module": "src/tag/index.ts"}}]}, {"kind": "javascript-module", "path": "src/text/index.ts", "declarations": [{"kind": "class", "description": "", "name": "Text", "members": [{"kind": "field", "name": "id", "type": {"text": "string"}, "default": "''", "attribute": "id"}, {"kind": "field", "name": "as", "type": {"text": "TextAsValues"}, "default": "'span'", "attribute": "as"}, {"kind": "field", "name": "size", "type": {"text": "TextSizeValues"}, "default": "'md'", "attribute": "size"}, {"kind": "field", "name": "weight", "type": {"text": "TextWeightValues"}, "default": "'regular'", "attribute": "weight"}, {"kind": "field", "name": "text", "type": {"text": "string"}, "default": "''", "attribute": "text"}, {"kind": "field", "name": "maxLines", "type": {"text": "string"}, "default": "''", "attribute": "maxLines"}, {"kind": "field", "name": "tag", "privacy": "private"}, {"kind": "field", "name": "lineClampRule", "privacy": "private"}, {"kind": "field", "name": "template", "privacy": "private"}], "attributes": [{"name": "id", "type": {"text": "string"}, "default": "''", "fieldName": "id"}, {"name": "as", "type": {"text": "TextAsValues"}, "default": "'span'", "fieldName": "as"}, {"name": "size", "type": {"text": "TextSizeValues"}, "default": "'md'", "fieldName": "size"}, {"name": "weight", "type": {"text": "TextWeightValues"}, "default": "'regular'", "fieldName": "weight"}, {"name": "text", "type": {"text": "string"}, "default": "''", "fieldName": "text"}, {"name": "maxLines", "type": {"text": "string"}, "default": "''", "fieldName": "maxLines"}], "superclass": {"name": "LitElement", "package": "lit"}, "tagName": "eds-text", "customElement": true}], "exports": [{"kind": "custom-element-definition", "name": "eds-text", "declaration": {"name": "Text", "module": "src/text/index.ts"}}, {"kind": "js", "name": "Text", "declaration": {"name": "Text", "module": "src/text/index.ts"}}]}, {"kind": "javascript-module", "path": "src/text-field/index.ts", "declarations": [{"kind": "class", "description": "", "name": "TextField", "members": [{"kind": "field", "name": "id", "type": {"text": "string"}, "default": "''", "attribute": "id"}, {"kind": "field", "name": "appearance", "type": {"text": "TextFieldAppearanceValues"}, "default": "'standard'", "attribute": "appearance"}, {"kind": "field", "name": "type", "type": {"text": "TextFieldTypeValues"}, "default": "'text'", "attribute": "type"}, {"kind": "field", "name": "value", "type": {"text": "string"}, "default": "''", "attribute": "value"}, {"kind": "field", "name": "placeholder", "type": {"text": "string"}, "default": "''", "attribute": "placeholder"}, {"kind": "field", "name": "iconLeading", "type": {"text": "string"}, "default": "''", "attribute": "iconLeading"}, {"kind": "field", "name": "iconTrailing", "type": {"text": "string"}, "default": "''", "attribute": "iconTrailing"}, {"kind": "field", "name": "maxlength", "type": {"text": "string"}, "default": "''", "attribute": "maxlength"}, {"kind": "field", "name": "minlength", "type": {"text": "string"}, "default": "''", "attribute": "minlength"}, {"kind": "field", "name": "showValue", "type": {"text": "boolean"}, "default": "false", "attribute": "showValue"}, {"kind": "field", "name": "isDisabled", "type": {"text": "boolean"}, "default": "false", "attribute": "isDisabled"}, {"kind": "field", "name": "isInvalid", "type": {"text": "boolean"}, "default": "false", "attribute": "isInvalid"}, {"kind": "field", "name": "isRequired", "type": {"text": "boolean"}, "default": "false", "attribute": "isRequired"}, {"kind": "field", "name": "isCompact", "type": {"text": "boolean"}, "default": "false", "attribute": "isCompact"}, {"kind": "field", "name": "is<PERSON><PERSON><PERSON>ly", "type": {"text": "boolean"}, "default": "false", "attribute": "is<PERSON><PERSON><PERSON>ly"}, {"kind": "field", "name": "inputWrapperEl", "type": {"text": "HTMLDivElement"}}, {"kind": "field", "name": "inputEl", "type": {"text": "HTMLInputElement"}}, {"kind": "field", "name": "iconPasswordEl", "type": {"text": "Icon"}}, {"kind": "field", "name": "_handleClick", "privacy": "private"}, {"kind": "field", "name": "_handleInput", "privacy": "private"}, {"kind": "field", "name": "_togglePassword", "privacy": "private"}], "attributes": [{"name": "id", "type": {"text": "string"}, "default": "''", "fieldName": "id"}, {"name": "appearance", "type": {"text": "TextFieldAppearanceValues"}, "default": "'standard'", "fieldName": "appearance"}, {"name": "type", "type": {"text": "TextFieldTypeValues"}, "default": "'text'", "fieldName": "type"}, {"name": "value", "type": {"text": "string"}, "default": "''", "fieldName": "value"}, {"name": "placeholder", "type": {"text": "string"}, "default": "''", "fieldName": "placeholder"}, {"name": "iconLeading", "type": {"text": "string"}, "default": "''", "fieldName": "iconLeading"}, {"name": "iconTrailing", "type": {"text": "string"}, "default": "''", "fieldName": "iconTrailing"}, {"name": "maxlength", "type": {"text": "string"}, "default": "''", "fieldName": "maxlength"}, {"name": "minlength", "type": {"text": "string"}, "default": "''", "fieldName": "minlength"}, {"name": "showValue", "type": {"text": "boolean"}, "default": "false", "fieldName": "showValue"}, {"name": "isDisabled", "type": {"text": "boolean"}, "default": "false", "fieldName": "isDisabled"}, {"name": "isInvalid", "type": {"text": "boolean"}, "default": "false", "fieldName": "isInvalid"}, {"name": "isRequired", "type": {"text": "boolean"}, "default": "false", "fieldName": "isRequired"}, {"name": "isCompact", "type": {"text": "boolean"}, "default": "false", "fieldName": "isCompact"}, {"name": "is<PERSON><PERSON><PERSON>ly", "type": {"text": "boolean"}, "default": "false", "fieldName": "is<PERSON><PERSON><PERSON>ly"}], "superclass": {"name": "LitElement", "package": "lit"}, "tagName": "eds-text-field", "customElement": true}], "exports": [{"kind": "custom-element-definition", "name": "eds-text-field", "declaration": {"name": "TextField", "module": "src/text-field/index.ts"}}, {"kind": "js", "name": "TextField", "declaration": {"name": "TextField", "module": "src/text-field/index.ts"}}]}, {"kind": "javascript-module", "path": "src/tile/index.ts", "declarations": [{"kind": "class", "description": "", "name": "Tile", "members": [{"kind": "field", "name": "iconName", "type": {"text": "string"}, "default": "\"\"", "attribute": "iconName"}, {"kind": "field", "name": "title", "type": {"text": "string"}, "default": "\"\"", "attribute": "title"}, {"kind": "field", "name": "text", "type": {"text": "string"}, "default": "\"\"", "attribute": "text"}], "attributes": [{"name": "iconName", "type": {"text": "string"}, "default": "\"\"", "fieldName": "iconName"}, {"name": "title", "type": {"text": "string"}, "default": "\"\"", "fieldName": "title"}, {"name": "text", "type": {"text": "string"}, "default": "\"\"", "fieldName": "text"}], "superclass": {"name": "LitElement", "package": "lit"}, "tagName": "eds-tile", "customElement": true}], "exports": [{"kind": "custom-element-definition", "name": "eds-tile", "declaration": {"name": "Tile", "module": "src/tile/index.ts"}}, {"kind": "js", "name": "Tile", "declaration": {"name": "Tile", "module": "src/tile/index.ts"}}]}, {"kind": "javascript-module", "path": "src/toast/index.ts", "declarations": [{"kind": "class", "description": "", "name": "Toast", "members": [{"kind": "field", "name": "id", "attribute": "id"}, {"kind": "field", "name": "appearance", "type": {"text": "ToastAppearance<PERSON><PERSON>ues"}, "default": "\"info\"", "attribute": "appearance"}, {"kind": "field", "name": "title", "type": {"text": "string"}, "default": "\"\"", "attribute": "title"}, {"kind": "field", "name": "description", "type": {"text": "string"}, "default": "\"\"", "attribute": "description"}, {"kind": "field", "name": "iconName", "type": {"text": "string"}, "default": "\"\"", "attribute": "iconName"}, {"kind": "field", "name": "showIcon", "type": {"text": "boolean"}, "default": "false", "attribute": "showIcon"}, {"kind": "field", "name": "actions", "type": {"text": "ToastAction[]"}, "default": "[]", "attribute": "actions"}, {"kind": "field", "name": "placement", "type": {"text": "ToastPlacementValues"}, "default": "\"top\"", "attribute": "placement"}, {"kind": "field", "name": "duration", "type": {"text": "number"}, "default": "300", "attribute": "duration"}, {"kind": "field", "name": "autoDismiss", "type": {"text": "boolean"}, "default": "false", "attribute": "autoDismiss"}, {"kind": "field", "name": "autoDismissDuration", "type": {"text": "number"}, "default": "3000", "attribute": "autoDismissDuration"}, {"kind": "field", "name": "toastElement", "type": {"text": "HTMLDivElement"}, "privacy": "private"}, {"kind": "field", "name": "autoDismissTimeout", "type": {"text": "number | undefined"}, "privacy": "private"}, {"kind": "field", "name": "animationTimeout", "type": {"text": "number | undefined"}, "privacy": "private"}, {"kind": "method", "name": "renderActions", "privacy": "private", "return": {"type": {"text": "TemplateResult | null"}}}, {"kind": "field", "name": "dismiss", "privacy": "public"}], "attributes": [{"name": "id", "fieldName": "id"}, {"name": "appearance", "type": {"text": "ToastAppearance<PERSON><PERSON>ues"}, "default": "\"info\"", "fieldName": "appearance"}, {"name": "title", "type": {"text": "string"}, "default": "\"\"", "fieldName": "title"}, {"name": "description", "type": {"text": "string"}, "default": "\"\"", "fieldName": "description"}, {"name": "iconName", "type": {"text": "string"}, "default": "\"\"", "fieldName": "iconName"}, {"name": "showIcon", "type": {"text": "boolean"}, "default": "false", "fieldName": "showIcon"}, {"name": "actions", "type": {"text": "ToastAction[]"}, "default": "[]", "fieldName": "actions"}, {"name": "placement", "type": {"text": "ToastPlacementValues"}, "default": "\"top\"", "fieldName": "placement"}, {"name": "duration", "type": {"text": "number"}, "default": "300", "fieldName": "duration"}, {"name": "autoDismiss", "type": {"text": "boolean"}, "default": "false", "fieldName": "autoDismiss"}, {"name": "autoDismissDuration", "type": {"text": "number"}, "default": "3000", "fieldName": "autoDismissDuration"}], "superclass": {"name": "LitElement", "package": "lit"}, "tagName": "eds-toast", "customElement": true}], "exports": [{"kind": "custom-element-definition", "name": "eds-toast", "declaration": {"name": "Toast", "module": "src/toast/index.ts"}}, {"kind": "js", "name": "Toast", "declaration": {"name": "Toast", "module": "src/toast/index.ts"}}]}]}