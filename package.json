{"name": "@eds/components", "version": "0.4.10", "description": "Etiya Design System Components", "private": false, "keywords": ["etiya", "design", "system", "components"], "publishConfig": {"registry": "https://artifact-server.etiya.com/repository/eds-components/"}, "main": "./dist/index.js", "types": "./dist/index.d.ts", "exports": {".": [{"types": "./dist/index.d.ts", "require": "./dist/index.js"}, "./dist/index.js"], "./package.json": "./package.json"}, "files": ["dist"], "packageManager": "bun@1.1.34", "scripts": {"dev": "bun run build && storybook dev -p 3000", "build": "bun build.js && storybook build", "preview": "serve storybook-static", "clean": "rm -rf dist && rm -rf storybook-static && rm -rf node_modules && rm -rf .cache", "lint": "eslint ./stories/*.stories.ts --max-warnings 0 && eslint ./src/**/*.ts --max-warnings 0", "publish-packages": "bun run build && changeset version && changeset publish"}, "devDependencies": {"@biomejs/biome": "2.0.6", "@changesets/cli": "^2.29.5", "@chromatic-com/storybook": "^4.0.1", "@custom-elements-manifest/analyzer": "^0.10.4", "@storybook/addon-a11y": "^9.0.15", "@storybook/addon-designs": "^10.0.1", "@storybook/addon-docs": "^9.0.15", "@storybook/addon-links": "^9.0.15", "@storybook/web-components-vite": "^9.0.15", "@types/minimatch": "3.0.5", "air-datepicker": "^3.6.0", "bun-lightningcss": "^0.2.0", "bun-plugin-dts": "^0.3.0", "husky": "^9.1.7", "imask": "^7.6.1", "lefthook": "^1.11.16", "lint-staged": "^16.1.2", "lit": "^3.3.0", "serve": "^14.2.4", "storybook": "^9.0.15", "storybook-addon-package-json": "^2.0.0", "typescript": "^5.8.3", "ultracite": "5.0.32", "vite": "7.0.3"}, "resolutions": {"minimatch": "^10.0.3", "@types/minimatch": "^6.0.0"}, "repository": {"type": "git", "url": "git+https://artifact-server.etiya.com/repository/eds-components", "directory": "components"}, "lint-staged": {"*.{js,jsx,ts,tsx,json,jsonc,css,scss,md,mdx}": ["bunx ultracite format"]}}