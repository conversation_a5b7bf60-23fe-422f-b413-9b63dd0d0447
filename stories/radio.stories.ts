import { html } from 'lit';
import {Args, type Meta} from "@storybook/web-components";


const meta: Meta = {
  title: "Components/Radio",
  tags: ['autodocs'],
  component: 'eds-radio',
  argTypes: {
    id: {
      control: { type: "text" },
    },
    name: {
      control: { type: "text" },
    },
    value: {
      control: { type: "text" },
    },
    isChecked: {
      control: { type: "boolean" },
    },
    isDisabled: {
      control: { type: "boolean" },
    },
    isInvalid: {
      control: { type: "boolean" },
    },
    isRequired: {
      control: { type: "boolean" },
    },
  },
  args: {
    id: "label1",
    name: "label1",
    value: "label1",
    isChecked: false,
    isDisabled: true,
    isInvalid: false,
    isRequired: false
  },
  render: (args) => {
    return html `
        <eds-radio
          id=${args.id}
          name=${args.name}
          value=${args.value}
          ?isChecked=${args.isChecked}
          ?isDisabled=${args.isDisabled}
          ?isInvalid=${args.isInvalid}
          ?isRequired=${args.isRequired}>
            <label for="${args.id}">Label</label>
        </eds-radio>
    `;
  },
};

export default meta;

export const Default = {
  args: {
    isRequired: false,
    isInvalid: false,
    isDisabled: false,
    isChecked: false
  },
};

export const Checked = {
  args: {
    isRequired: false,
    isInvalid: false,
    isDisabled: false,
    isChecked: true,
  },
};

export const Disabled = {
  args: {
    isRequired: false,
    isInvalid: false,
    isChecked: false,
    isDisabled: true,
  },
};

export const DisableAndChecked = {
  args: {
    isRequired: false,
    isInvalid: false,
    isChecked: true,
    isDisabled: true,
  },
};

export const Invalid = {
  args: {
    isDisabled: false,
    isRequired: false,
    isChecked: true,
    isInvalid: true,
  },
};

export const Required = {
  args: {
    isChecked: false,
    isDisabled: false,
    isInvalid: false,
    isRequired: true
  },
};

export const Multiline = {
  args:{
    isChecked: false,
    isDisabled: false,
    isInvalid: false,
    isRequired: false
  },
  render: (args: Args) => {
    return html `
        <eds-radio
          id=${args.id}
          name=${args.name}
          value=${args.value}
          ?isChecked=${args.isChecked}
          ?isDisabled=${args.isDisabled}
          ?isInvalid=${args.isInvalid}
          ?isRequired=${args.isRequired}>
            <label for="${args.id}" style="width: 92px;">This label doesn’t fit single line.</label>
        </eds-radio>
    `;
  },
};