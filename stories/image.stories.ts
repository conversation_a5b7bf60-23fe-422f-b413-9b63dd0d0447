import { html } from "lit";
import { type Meta } from "@storybook/web-components";


const meta: Meta = {
  title: "Components/Image",
  tags: ["autodocs"],
  parameters: {
    layout: "centered",
    docs: {
      toc: false
    }
  },
  component: "eds-image",
  argTypes: {
    src: {
      control: { type: "text" },
    },
    alt: {
      control: { type: "text" },
    },
    width: {
      control: { type: "text" },
    },
    height: {
      control: { type: "text" },
    },
    fallbackSrc: {
      control: { type: "text" },
    },
  },
  args: {
    src: "https://dummyimage.com/200x200/23233e/ffffff&text=Image",
    alt: "eds-image",
    width: "auto",
    height: "auto",
    fallbackSrc: "https://dummyimage.com/200x200/ee7d1b/ffffff&text=Fallback_Image",
  },
  render: (args) => {
    return html `<eds-image 
      src="${args.src}"
      alt="${args.alt}"
      width="${args.width}"
      height="${args.height}"
      fallbackSrc="${args.fallbackSrc}"
    ></eds-image>`
  }
};

export default meta;

export const Default = {
  args: {}
};