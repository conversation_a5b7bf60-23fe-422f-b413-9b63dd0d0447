import { html } from "lit";
import { action } from "storybook/actions";
import "../src/toast";
import "../src/alert";
import "../src/button";

const meta = {
  title: "Components/Toast",
  tags: ["autodocs"],
  component: "eds-toast",
  parameters: {
    docs: {
      description: {
        component: "Toast component for displaying notifications"
      }
    }
  },
  argTypes: {
    placement: {
      control: { type: "select" },
      options: ["top", "bottom", "top-right", "top-left", "bottom-right", "bottom-left", "center", "left", "right"],
      description: "Position of the toast container"
    }
  }
};

export default meta;

// Helper function to create and show a toast
const showToast = (toast, options) => {
  setTimeout(() => {
    if (toast) {
      toast.show(options);
    }
  }, 100);
};

export const BasicToast = {
  render: () => {
    return html`
      <div style="padding: 20px;">
        <eds-toast id="basic-toast" placement="top-right"></eds-toast>
        <eds-button appearance="primary" @button-click=${(e) => {
          const toast = document.getElementById('basic-toast');
          showToast(toast, {
            title: "Basic Toast",
            description: "This is a basic toast message",
            appearance: "info"
          });
        }}>Show Toast</eds-button>
      </div>
    `;
  }
};

export const ToastAppearances = {
  render: () => {
    return html`
      <div style="padding: 20px; display: flex; gap: 10px;">
        <eds-toast id="toast-container" placement="top-right"></eds-toast>
        
        <eds-button appearance="primary" @button-click=${(e) => {
          const toast = document.getElementById('toast-container');
          showToast(toast, {
            title: "Info Toast",
            description: "This is an informational message",
            appearance: "info",
            icon: "informationCircle"
          });
        }}>Info</eds-button>
        
        <eds-button appearance="primary" @button-click=${(e) => {
          const toast = document.getElementById('toast-container');
          showToast(toast, {
            title: "Success Toast",
            description: "Operation completed successfully",
            appearance: "success",
            icon: "checkmark"
          });
        }}>Success</eds-button>
        
        <eds-button appearance="primary" @button-click=${(e) => {
          const toast = document.getElementById('toast-container');
          showToast(toast, {
            title: "Warning Toast",
            description: "This action might cause issues",
            appearance: "warning",
            icon: "warningTriangle"
          });
        }}>Warning</eds-button>
        
        <eds-button appearance="primary" @button-click=${(e) => {
          const toast = document.getElementById('toast-container');
          showToast(toast, {
            title: "Error Toast",
            description: "An error occurred during the operation",
            appearance: "error",
            icon: "alertCircle"
          });
        }}>Error</eds-button>
      </div>
    `;
  }
};

export const ToastPlacements = {
  render: () => {
    return html`
      <div style="padding: 20px; display: flex; flex-wrap: wrap; gap: 10px;">
        <eds-toast id="top-right-toast" placement="top-right"></eds-toast>
        <eds-toast id="top-left-toast" placement="top-left"></eds-toast>
        <eds-toast id="bottom-right-toast" placement="bottom-right"></eds-toast>
        <eds-toast id="bottom-left-toast" placement="bottom-left"></eds-toast>
        
        <eds-button appearance="primary" @button-click=${(e) => {
          const toast = document.getElementById('top-right-toast');
          showToast(toast, {
            title: "Top Right",
            description: "Top right toast message",
            appearance: "info"
          });
        }}>Top Right</eds-button>
        
        <eds-button appearance="primary" @button-click=${(e) => {
          const toast = document.getElementById('top-left-toast');
          showToast(toast, {
            title: "Top Left",
            description: "Top left toast message",
            appearance: "info"
          });
        }}>Top Left</eds-button>
        
        <eds-button appearance="primary" @button-click=${(e) => {
          const toast = document.getElementById('bottom-right-toast');
          showToast(toast, {
            title: "Bottom Right",
            description: "Bottom right toast message",
            appearance: "info"
          });
        }}>Bottom Right</eds-button>
        
        <eds-button appearance="primary" @button-click=${(e) => {
          const toast = document.getElementById('bottom-left-toast');
          showToast(toast, {
            title: "Bottom Left",
            description: "Bottom left toast message",
            appearance: "info"
          });
        }}>Bottom Left</eds-button>
      </div>
    `;
  }
};

export const AutoDismiss = {
  render: () => {
    return html`
      <div style="padding: 20px;">
        <eds-toast id="auto-dismiss-toast" placement="top-right"></eds-toast>
        <eds-button appearance="primary" @button-click=${(e) => {
          const toast = document.getElementById('auto-dismiss-toast');
          showToast(toast, {
            title: "Auto Dismiss",
            description: "This toast will dismiss after 4 seconds",
            appearance: "info",
            duration: 4,
            onClose: () => action('Toast closed')()
          });
        }}>Show Auto-dismiss Toast</eds-button>
      </div>
    `;
  }
};

export const WithCustomIcon = {
  render: () => {
    return html`
      <div style="padding: 20px;">
        <eds-toast id="custom-icon-toast" placement="top-right"></eds-toast>
        <eds-button appearance="primary" @button-click=${(e) => {
          const toast = document.getElementById('custom-icon-toast');
          showToast(toast, {
            title: "Custom Icon",
            description: "This toast has a custom icon",
            appearance: "info",
            icon: "star"
          });
        }}>Show Toast with Icon</eds-button>
      </div>
    `;
  }
};

export const NonDismissible = {
  render: () => {
    return html`
      <div style="padding: 20px;">
        <eds-toast id="non-dismissible-toast" placement="top-right"></eds-toast>
        <eds-button appearance="primary" @button-click=${(e) => {
          const toast = document.getElementById('non-dismissible-toast');
          showToast(toast, {
            title: "Non-dismissible",
            description: "This toast cannot be dismissed by clicking the close button",
            appearance: "warning",
            dismissible: false
          });
        }}>Show Non-dismissible Toast</eds-button>
      </div>
    `;
  }
};