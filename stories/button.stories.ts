import { html } from 'lit';
import { type Meta } from '@storybook/web-components';
import { Icons } from '../src/icons';
import { action } from 'storybook/actions';

const meta: Meta = {
  title: 'Components/Button',
  tags: ['autodocs'],
  component: 'eds-button',
  argTypes: {
    appearance: {
      control: { type: 'select' },
      options: ['primary', 'default', 'subtle', 'link', 'destructive', 'secondary'],
    },
    size: {
      control: { type: 'select' },
      options: ['default', 'compact', 'lineHeight'],
    },
    iconOnly: {
      control: { type: 'boolean' },
    },
    iconLeading: {
      control: { type: 'select' },
      options: Object.keys(Icons),
    },
    iconTrailing: {
      control: { type: 'select' },
      options: Object.keys(Icons),
    },
  },
  args: {
    appearance: 'primary',
    size: 'default',
    shouldFitContainer: false,
    isDisabled: false,
    loading: false,
    iconOnly: false,
    iconLeading: '',
    iconTrailing: '',
    href: '',
    target: '',
  },
  render: (args) => {
    return html` <eds-button
      href="${args.href}"
      target="${args.target}"
      appearance="${args.appearance}"
      size="${args.size}"
      ?shouldFitContainer="${args.shouldFitContainer}"
      ?isDisabled="${args.isDisabled}"
      ?loading="${args.loading}"
      iconLeading="${args.iconLeading}"
      iconTrailing="${args.iconTrailing}"
      ?iconOnly="${args.iconOnly}"
      ?circle="${args.circle}"
      @button-click="${action('button-click')}"
    >
      Button
    </eds-button>`;
  },
};

export default meta;

export const DefaultButton = {
  args: {
    appearance: 'default',
  },
};

export const PrimaryButton = {
  args: {
    appearance: 'primary',
  },
};

export const SecondaryButton = {
  args: {
    appearance: 'secondary',
  },
};

export const SubtleButton = {
  args: {
    appearance: 'subtle',
  },
};

export const DestructiveButton = {
  args: {
    appearance: 'destructive',
  },
};

export const LinkButton = {
  args: {
    appearance: 'link',
  },
};

export const LinkWithHref = {
  args: {
    href: '#',
    target: '_self',
    appearance: 'link',
  },
};

export const CompactButton = {
  args: {
    size: 'compact',
  },
};

export const LineHeightButton = {
  args: {
    size: 'lineHeight',
  },
};

export const IconLeading = {
  args: {
    iconLeading: 'borderNone',
  },
};

export const IconTrailing = {
  args: {
    iconTrailing: 'borderNone',
  },
};

export const IconPositionBoth = {
  args: {
    iconLeading: 'borderNone',
    iconTrailing: 'borderNone',
  },
};

export const LoadingButton = {
  args: {
    loading: true,
  },
};

export const DisabledButton = {
  args: {
    isDisabled: true,
  },
};

export const IconOnlyButton = {
  args: {
    iconOnly: true,
    iconLeading: 'borderNone',
  },
};

export const ContainerSizedButton = {
  args: {
    shouldFitContainer: true,
  },
};
