import { html } from 'lit';
import { type Meta } from "@storybook/web-components";


const meta: Meta = {
  title: "Components/Badge",
  tags: ['autodocs'],
  component: 'eds-badge',
  argTypes: {
    label: {
      control: { type: "text" },
    },
    type: {
      control: { 
        type: "select"
      },
      options: ["rounded", "circle"] 
    },
    size: {
      control: { 
        type: "select", 
      },
      options: ["small", "medium"] 
    },
    appearance: {
      control: { 
        type: "select", 
      },
      options: ["primary", "secondary", "opacity", "stroke"] 
    }
  },
  render: (args) => {
    return html`
      <eds-badge
        label="${args.label}"
        type="${args.type}"
        size="${args.size}"
        appearance="${args.appearance}"
      >
      </eds-badge>
    `;
  },
};

export default meta;

export const Rounded = {
  args: {
    label: '1',
    type: 'rounded',
    size: 'medium',
    appearance: 'primary'
  }
};

export const Circle = {
  args: {
    label: '1',
    type: 'circle',
    size: 'medium',
    appearance: 'primary'
  }
};

export const Small = {
  args: {
    label: '1',
    type: 'rounded',
    size: 'small',
    appearance: 'primary'
  }
};

export const Primary = {
  args: {
    label: '1',
    type: 'rounded',
    size: 'medium',
    appearance: 'primary'
  }
}; 

export const Secondary = {
  args: {
    label: '1',
    type: 'rounded',
    size: 'medium',
    appearance: 'secondary'
  }
};

export const Opacity = {
  args: {
    label: '1',
    type: 'rounded',
    size: 'medium',
    appearance: 'opacity'
  }
};  

export const Stroke = {
  args: {
    label: '1',
    type: 'rounded',
    size: 'medium',
    appearance: 'stroke'
  }
};