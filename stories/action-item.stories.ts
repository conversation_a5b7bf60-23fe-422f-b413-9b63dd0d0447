import { html } from 'lit';
import { Args, type Meta } from '@storybook/web-components';
import { action } from 'storybook/actions';

const meta: Meta = {
  title: 'Components/Action Item',
  tags: ['autodocs'],
  component: 'eds-action-item',
  parameters: {
    docs: {
      toc: false,
    },
  },
  argTypes: {
    text: { control: { type: 'text' } },
  },
  args: {
    text: 'Label',
  },
  render: (args) => {
    return html` <eds-action-item text=${args.text} @action-click=${action('action-click')}> </eds-action-item>`;
  },
};

export default meta;

export const Default = {
  args: {},
};

export const WithIconTrailing = {
  args: {},
  render: (args: Args) => {
    return html` <eds-action-item text=${args.text} @action-click=${action('action-click')}>
      <eds-icon name="borderNone" slot="trailing-icon"></eds-icon>
    </eds-action-item>`;
  },
};

export const WithIconLeading = {
  args: {},
  render: (args: Args) => {
    return html` <eds-action-item text=${args.text} @action-click=${action('action-click')}>
      <eds-icon name="borderNone" slot="leading-icon"></eds-icon>
    </eds-action-item>`;
  },
};

export const WithBothIcons = {
  render: (args: Args) => {
    return html` <eds-action-item text=${args.text} @action-click=${action('action-click')}>
      <eds-icon name="borderNone" slot="leading-icon"></eds-icon>
      <eds-icon name="borderNone" slot="trailing-icon"></eds-icon>
    </eds-action-item>`;
  },
};
