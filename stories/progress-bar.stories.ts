import { html } from "lit";
import type { Meta } from "@storybook/web-components";


const meta: Meta = {
  title: "Components/ProgressBar",
  tags: ["autodocs"],
  component: "eds-progress-bar",
  argTypes: {
    label: { control: { type: "text" } },
    helperText: { control: { type: "text" } },
    value: { control: { type: "number" } },
    maxValue: { control: { type: "number" } },
    valueType: { control: { type: "text" } },
    isIndeterminate: { control: { type: "boolean" } },
    isAnimated: { control: { type: "boolean" } },
    isUnlimited: { control: { type: "boolean" } },
    type: {
      control: { type: "select" },
      options: ["bar", "circle"],
    },
    circleProperties: { control: { type: "object" } },
  },
  args: {
    label: 'Label',
    value: 20,
    maxValue: 100,
    valueType: 'GB',
    helperText: 'Helper Text',
    isIndeterminate: false,
    isAnimated: true,
    isUnlimited: false,
    type: 'bar',
    circleProperties: {
      default: 100,
      breakpoints: {
        640: 120,
        1024: 140
      }
    }
  },
  render: (args) => {
    return html `<eds-progress-bar 
        label=${args.label}
        helperText=${args.helperText}
        value=${args.value}
        maxValue=${args.maxValue}
        valueType=${args.valueType}
        ?isIndeterminate=${args.isIndeterminate}
        ?isAnimated=${args.isAnimated}
        type=${args.type}
        .circleProperties=${args.circleProperties}
      ></eds-progress-bar>`;
  },
};

export default meta;

export const Default = {
  args: {}
};

export const Unlimited = {
  args: {
    isUnlimited: true
  }
};

export const Circle = {
  args: {
    type: 'circle'
  }
};

export const Indeterminate = {
  render: (args) => html`
<div style="display: flex; flex-direction: column; gap: 24px;">
  <eds-progress-bar
    label=${args.label}
    helperText=${args.helperText}
    value=${args.value}
    maxValue=${args.maxValue}
    valueType=${args.valueType}
    ?isIndeterminate=${true}
    ?isAnimated=${args.isAnimated}
    type=${'bar'}
    .circleProperties=${args.circleProperties}
  ></eds-progress-bar>
  <eds-progress-bar
    label=${args.label}
    helperText=${args.helperText}
    value=${args.value}
    maxValue=${args.maxValue}
    valueType=${args.valueType}
    ?isIndeterminate=${true}
    ?isAnimated=${args.isAnimated}
    type=${'circle'}
    .circleProperties=${args.circleProperties}
  ></eds-progress-bar>
</div>
  `,
};