import { html } from "lit";
import { type Meta } from "@storybook/web-components";


const meta: Meta = {
  title: "Components/ProgressTracker",
  tags: ["autodocs"],
  component: "eds-progress-tracker",
  parameters: {
    docs: {
      toc: false
    }
  },
  argTypes: {
    orientation: {
      control: { type: "select" },
      options: ["horizontal", "vertical"],
    },        
    items: {
      control: { type: "object" },
    }    
  },
  args: {
    orientation: 'horizontal',
    items: [
      {
        title: 'Title 1',
        subTitle: 'Subtitle 1',
        description: 'Description of Step 1',
        status: 'active',
        isDisabled: false,
        href: '#',
        iconName: 'informationCircle',
      },
      {
        title: 'Step 2',
        subTitle: 'Subtitle 2',
        description: 'Description of Step 2',
        status: 'inactive',
        isDisabled: false,
        href: '#',
        iconName: 'informationCircle',
      },
      {
        title: 'Step 3',
        subTitle: 'Subtitle 3',
        description: 'Description of Step 3',
        status: 'inactive',
        isDisabled: false,
        href: '#',
        iconName: 'informationCircle',
      },
    ]
  },
  render: (args) => {
    return html `<eds-progress-tracker 
      orientation="${args.orientation}"
      .items="${args.items}"
    ></eds-progress-tracker>`
  }
};

export default meta;

export const Default = {
  args: {}
};

export const Statuses = {
  args: {
    orientation: 'horizontal',
    items: [
      {
        title: 'Completed',
        status: 'completed',
        isDisabled: false,
        href: '#',
        iconName: 'informationCircle',
      },
      {
        title: 'Active',
        status: 'active',
        isDisabled: false,
        href: '#',
        iconName: 'informationCircle',
      },
      {
        title: 'Inactive',
        status: 'inactive',
        isDisabled: false,
        href: '#',
        iconName: 'informationCircle',
      },
    ]
  }
};

export const Vertical = {
  args: {
    orientation: 'vertical'
  }
};