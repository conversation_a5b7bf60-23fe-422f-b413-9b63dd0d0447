import { html } from 'lit';
import { type Meta } from "@storybook/web-components";


const meta: Meta = {
  title: "Components/DataList",
  tags: ["autodocs"],
  parameters: {
    layout: "centered",
    docs: {
      toc: false
    }
  },
  component: "eds-data-list",
  argTypes: {
    expandedText: {
      control: { type: "text" },
    },
    unexpandedText: {
      control: { type: "text" },
    },
    itemsSize: {
      control: { type: "number", min: 1 },
    },
    items: {
      control: { type: "object" },
    },
    trim: {
      control: { type: "boolean" },
    },
    isExpanded: {
      control: { type: "boolean" },
    },
  },
  args: {
    expandedText: "Show less",
    unexpandedText: "Show more",
    itemsSize: 5,
    trim: true,
    isExpanded: false,
    items: [
      {
        key: "Plan name",
        value: "All-inclusive plan 100 GB",
      },
      {
        key: "End user",
        value: "<PERSON><PERSON><PERSON>",
      },
      {
        key: "Phone number",
        value: "************",
      },
      {
        key: "Status",
        value: '<eds-tag content="Active" href=""></eds-tag>',
      },
      {
        key: "Billing Account ID",
        value: "*********",
      },
      {
        key: "First activation date",
        value: "10/10/2025",
      },
      {
        key: "Valid until date",
        value: "10/10/2026",
      },
      {
        key: "Price",
        value: "<del>$50</del> $30 / month",
      },
    ],
  },
  render: (args) => {
    return html `<eds-data-list 
      itemsSize=${args.itemsSize}
      ?trim=${args.trim} 
      ?isExpanded=${args.isExpanded} 
      expandedText=${args.expandedText} 
      unexpandedText=${args.unexpandedText} 
      .items=${args.items}
      ></eds-data-list>`;
  },
};

export default meta;

export const Default = {
  args: {}
};