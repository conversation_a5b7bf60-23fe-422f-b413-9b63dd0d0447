import { html } from 'lit';
import { type Meta } from "@storybook/web-components";


const meta: Meta = {
  title: "Components/Date Picker",
  tags: ["autodocs"],
  component: "eds-date-picker",
  parameters: {
    layout: 'centered',
  },
  argTypes: {
    id: { control: 'text' },
    classes: { control: 'text' },
    inline: { control: 'boolean' },
    locale: {
      control: 'select',
      options: ['tr', 'en', 'ar', 'bg', 'ca', 'cs', 'da', 'de', 'el', 'es', 'eu', 'fi', 'fr', 'hr', 'hu', 'id', 'it', 'ja', 'ko', 'nb', 'nl', 'pl', 'pt', 'ptBr', 'ro', 'ru', 'si', 'sk', 'sl', 'sv', 'th', 'uk', 'zh']
    },
    startDate: { control: 'date' },
    firstDay: { control: 'number', min: 0, max: 6 },
    isMobile: { control: 'boolean' },
    visible: { control: 'boolean' },
    dateFormat: { control: 'text' },
    altField: { control: 'text' },
    altFieldDateFormat: { control: 'text' },
    toggleSelected: { control: 'boolean' },
    keyboardNav: { control: 'boolean' },
    container: { control: 'text' },
    position: {
      control: 'select',
      options: ['bottom left', 'bottom right', 'top left', 'top right']
    },
    view: {
      control: 'select',
      options: ['days', 'months', 'years']
    },
    minView: {
      control: 'select',
      options: ['days', 'months', 'years']
    },
    showOtherMonths: { control: 'boolean' },
    selectOtherMonths: { control: 'boolean' },
    moveToOtherMonthsOnSelect: { control: 'boolean' },
    minDate: { control: 'date' },
    maxDate: { control: 'date' },
    disableNavWhenOutOfRange: { control: 'boolean' },
    multipleDates: { control: 'boolean' },
    multipleDatesSeparator: { control: 'text' },
    range: { control: 'boolean' },
    dynamicRange: { control: 'boolean' },
    monthsField: { control: 'text' },
    showEvent: { control: 'text' },
    autoClose: { control: 'boolean' },
    navTitles: { control: 'object' },
    fixedHeight: { control: 'boolean' },

    timepicker: { control: 'boolean' },
    onlyTimepicker: { control: 'boolean' },
    dateTimeSeparator: { control: 'text' },
    timeFormat: { control: 'text' },
    minHours: { control: 'number', min: 0, max: 23 },
    maxHours: { control: 'number', min: 1, max: 24 },
    minMinutes: { control: 'number', min: 0, max: 59 },
    maxMinutes: { control: 'number', min: 0, max: 59 },
    hoursStep: { control: 'number', min: 1 },
    minutesStep: { control: 'number', min: 1 },

    targetId: { control: 'text' }
  },
  args: {
    id: 'datepickerID',
    classes: '',
    inline: false,
    locale: 'tr',
    dateFormat: 'dd.MM.yyyy',
    position: 'bottom left',
    view: 'days',
    minView: 'days',
    showOtherMonths: true,
    selectOtherMonths: true,
    moveToOtherMonthsOnSelect: true,
    autoClose: true,
    toggleSelected: true,
    keyboardNav: true,
    dynamicRange: true,
    monthsField: 'monthsShort',
    showEvent: 'focus',
    fixedHeight: false,
    timepicker: false,
    onlyTimepicker: false,
    dateTimeSeparator: ' ',
    hoursStep: 1,
    minutesStep: 1
  },
  render: (args) => html`
    <eds-date-picker
      id="${args.id}"
      classes="${args.classes}"
      ?inline="${args.inline}"
      locale="${args.locale}"
      startdate="${args.startDate || ''}"
      firstday="${args.firstDay}"
      ?ismobile="${args.isMobile}"
      ?visible="${args.visible}"
      dateformat="${args.dateFormat}"
      altfield="${args.altField}"
      altfielddateformat="${args.altFieldDateFormat}"
      ?toggleselected="${args.toggleSelected}"
      ?keyboardnav="${args.keyboardNav}"
      container="${args.container}"
      position="${args.position}"
      view="${args.view}"
      minview="${args.minView}"
      ?showothermonths="${args.showOtherMonths}"
      ?selectothermonths="${args.selectOtherMonths}"
      ?movetoothermonthsonselect="${args.moveToOtherMonthsOnSelect}"
      mindate="${args.minDate || ''}"
      maxdate="${args.maxDate || ''}"
      ?disablenavwhenoutofrange="${args.disableNavWhenOutOfRange}"
      ?multipledates="${args.multipleDates}"
      multipledatesseparator="${args.multipleDatesSeparator}"
      ?range="${args.range}"
      ?dynamicrange="${args.dynamicRange}"
      monthsfield="${args.monthsField}"
      showevent="${args.showEvent}"
      ?autoclose="${args.autoClose}"
      .navtitles="${args.navTitles}"
      ?fixedheight="${args.fixedHeight}"
      ?timepicker="${args.timepicker}"
      ?onlytimepicker="${args.onlyTimepicker}"
      datetimeseparator="${args.dateTimeSeparator}"
      timeformat="${args.timeFormat}"
      minhours="${args.minHours}"
      maxhours="${args.maxHours}"
      minminutes="${args.minMinutes}"
      maxminutes="${args.maxMinutes}"
      hoursstep="${args.hoursStep}"
      minutesstep="${args.minutesStep}"
    ></eds-date-picker>
  `
};

export default meta;

export const Default = {
  args: {}
};

export const InlineCalendar = {
  args: {
    inline: true
  }
};

export const MonthView = {
  args: {
    view: 'months'
  }
};

export const WithTimePicker = {
  args: {
    timepicker: true,
    timeFormat: 'HH:mm',
    minHours: 0,
    maxHours: 24,
    minMinutes: 0,
    maxMinutes: 59,
    hoursStep: 1,
    minutesStep: 1,
  }
};

export const RangeSelection = {
  args: {
    range: true,
    multipleDatesSeparator: ' - '
  }
};

export const MultipleDates = {
  args: {
    multipleDates: true,
    multipleDatesSeparator: ', '
  }
};

export const WithMinMaxDates = {
  args: {
    minDate: '2024-03-01',
    maxDate: '2024-03-31'
  }
};

export const CustomFormat = {
  args: {
    dateFormat: 'yyyy/MM/dd'
  }
};

export const WithExternalInput = {
  args: {
    inline: false,
    targetId: 'externalInput'
  },
  render: (args) => html`
    <eds-text-field id="${args.targetId}" value="2024-03-01" placeholder="Tarih seçin..." iconTrailing="calendar"></eds-text-field>
    <eds-date-picker
      .targetId="${args.targetId}"
      .inline="${args.inline}"
    ></eds-date-picker>
  `
};
