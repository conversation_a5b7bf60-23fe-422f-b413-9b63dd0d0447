import { type Meta } from "@storybook/web-components";
import { html } from "lit";


const meta: Meta = {
  title: "Components/Checkout Card",
  tags: ["autodocs"],
  component: "eds-checkout-card",
  parameters: {
    docs: {
      toc: false
    }
  },
  argTypes: {
    type: {
      control: { type: "select" },
      options: ["active", "completed", "inactive"]
    },
    itemAmount: {
      control: { type: "text" },
    },
    headerIcon: {
      control: { type: "text" },
    },
    label: {
      control: { type: "text" },
    }
  },
  args: {
    type: "inactive",
    itemAmount: "1",
    headerIcon: "router",
    label: "Mobile",
  },
  render: (args) => {
    return html `<eds-checkout-card 
        type="${args.type}"
        itemAmount="${args.itemAmount}"
        headerIcon="${args.headerIcon}"
        label="${args.label}"
      >
        Step content
      </eds-checkout-card>`;
  },
};

export default meta;

export const Inactive = {
  args: {
    type: "inactive",
  },
};

export const Active = {
  args: {
    type: "active",
  },
};

export const Completed = {
  args: {
    type: "completed",
  },
};