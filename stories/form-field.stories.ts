import { html } from "lit";
import { type Meta } from "@storybook/web-components";


const meta: Meta = {
  title: "Components/FormField",
  tags: ["autodocs"],
  parameters: {
    layout: "centered",
    docs: {
      toc: false
    }
  },
  component: "eds-form-field",
  argTypes: {
    label: { control: "text" },
    helperText: { control: "text" },
    errorMessage: { control: "text" },
    successMessage: { control: "text" },
  },
  args: {
    label: "Email address",
    helperText: "Accurate email needed for communication.",
    errorMessage: "Email address must be in the format: <EMAIL>",
    successMessage: "",
  },
  render: (args) => {
    return html`
<eds-form-field style="display: block; width: 232px;" 
                label="${args.label}" 
                helperText="${args.helperText}"
                errorMessage="${args.errorMessage}"
                successMessage="${args.successMessage}">
  <eds-text-field id="textField" placeholder="<EMAIL>"></eds-text-field>
</eds-form-field>`;
  }
};

export default meta;

export const Default = {
  args: {}
};

export const Required = {
  args: {},
  render: (args) => {
    return html`
<eds-form-field style="display: block; width: 232px;" 
                label="${args.label}" 
                helperText="${args.helperText}"
                errorMessage="${args.errorMessage}"
                successMessage="${args.successMessage}">
  <eds-text-field id="textField" isRequired="true" placeholder="<EMAIL>"></eds-text-field>
</eds-form-field>`;
  }
};

export const Invalid = {
  args: {},
  render: (args) => {
    return html`
<eds-form-field style="display: block; width: 232px;"
                label="${args.label}" 
                helperText="${args.helperText}"
                errorMessage="${args.errorMessage}"
                successMessage="${args.successMessage}">
  <eds-text-field id="textField" value="mail@example" isRequired="true" isInvalid="true" placeholder="<EMAIL>"></eds-text-field>
</eds-form-field>`;
  }
};

export const Valid = {
  args: {
    successMessage: 'Your email address looks great!'
  },
  render: (args) => {
    return html`
<eds-form-field style="display: block; width: 232px;" 
                label="${args.label}" 
                helperText="${args.helperText}"
                errorMessage="${args.errorMessage}"
                successMessage="${args.successMessage}">
  <eds-text-field id="textField" value="<EMAIL>" isRequired="true" placeholder="<EMAIL>"></eds-text-field>
</eds-form-field>`;
  }
};