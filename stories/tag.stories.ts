import { html } from "lit";
import { type Meta } from "@storybook/web-components";

import { Icons } from "../src/icons";

const meta: Meta = {
  title: "Components/Tag",
  tags: ["autodocs"],
  parameters: {
    layout: "centered",
  },
  component: "eds-tag",
  argTypes: {
    content: {
      control: { type: "text" },
    },
    appearance: {
      control: { type: "select" },
      options: ["blue", "green", "orange", "red", "grey"],
    },
    isRemovable: {
      control: { type: "boolean" },
    },
    iconName: {
      control: { type: "select" },
      options: Object.keys(Icons)
    },
    href: {
      control: { type: "text" },
    },
    maxlength: {
      control: { type: "number" },
    },
  },
  args: {
    content: "STATUS",
    appearance: 'blue',
    isRemovable: false,
    iconName: "",
    href: "#",
    maxlength: "",
  },
  render: (args) => {
    return html `<eds-tag
      content="${args.content}"
      appearance="${args.appearance}"
    ></eds-tag>`
  }
};

export default meta;

export const GreyTag = {
  args: {
    appearance: 'grey'
  }
};

export const BlueTag = {
  args: {
    appearance: 'blue'
  }
};

export const GreenTag = {
  args: {
    appearance: 'green'
  }
};

export const OrangeTag = {
  args: {
    appearance: 'orange'
  }
};

export const RedTag = {
  args: {
    appearance: 'red'
  }
};