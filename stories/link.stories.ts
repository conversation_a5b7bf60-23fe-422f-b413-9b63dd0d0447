import { html } from 'lit';
import { type Meta } from "@storybook/web-components";


const meta: Meta = {
  title: "Components/Link",
  tags: ['autodocs'],
  component: 'eds-link',
  argTypes: {
    href: {
      control: { type: "text" },
    },
    target: {
      control: { type: "text" },
    },
    isDisabled: {
      control: { type: "boolean" },
    },
  },
  args: {
    href: '#',
    target: '_self',
    isDisabled: false,
  },
  render: (args) => {
    return html`
      <eds-link
        href="${args.href}"
        target="${args.target}"
        ?isDisabled="${args.isDisabled}"
      >
        Link
      </eds-link>`
    ;
  },
};

export default meta;

export const Default = {
  args: {},
};

export const Disabled = {
  args: {
    isDisabled: true,
  },
};