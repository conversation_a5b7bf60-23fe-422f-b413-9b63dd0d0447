import { html } from "lit";
import { type Meta } from "@storybook/web-components";

import { Icons } from "../src/icons";

const meta: Meta = {
  title: "Components/MediaObject",
  tags: ['autodocs'],
  component: 'eds-media-object',
  parameters: {
    docs: {
      toc: false
    }
  },
  argTypes: {
    src: {control: { type: "text" }},
    upperText: {control: { type: "text" }},
    text: {control: { type: "text" }},
    description: {control: { type: "text" }},
    iconName: {
      control: { type: "select" },
      options: Object.keys(Icons)
    },
    width: {control: { type: "text" }},
    height: {control: { type: "text" }},
  },
  args: {
    src: 'https://dummyimage.com/200x200/23233e/ffffff&text=Image',
    upperText: 'Upper Text',
    text: 'Text',
    description: 'Description',
    iconName: '',
    width: '64',
    height: '64',
  },
  render: (args) => {
    return html `<eds-media-object 
      src="${args.src}"
      upperText="${args.upperText}"
      text="${args.text}"
      description="${args.description}"
      iconName="${args.iconName}"
      width="${args.width}"
      height="${args.height}"
      ></eds-media-object>`
  }
};

export default meta;

export const Default = {
  args: {},
};

export const WithIcon = {
  args: {
    src: '',
    iconName: 'informationCircle'
  },
};