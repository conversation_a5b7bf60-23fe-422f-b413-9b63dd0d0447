import { html } from 'lit';
import { type Meta } from '@storybook/web-components';


const meta: Meta = {
  title: "Components/SegmentedControl",
  tags: ['autodocs'],
  component: 'eds-segmented-control',
  argTypes: {
    isDisabled: {
      control: { type: "boolean" },
    },
    options: {
      control: { type: "object" }
    },
  },
  args: {
    options: [
      {
        text: 'All',
        ariaControls: 'all',
        onlyIcon: false,
        isDisabled: false,
        isSelected: false,
        defaultSelected: true,
      },
      {
        text: 'Odd',
        ariaControls: 'odd',
        onlyIcon: false,
        isDisabled: false,
        isSelected: false,
        defaultSelected: false,
      },
      {
        text: 'Even',
        ariaControls: 'even',
        onlyIcon: false,
        isDisabled: false,
        isSelected: false,
        defaultSelected: false,
      }
    ]
  },
  render: (args) => {
    return html`
      <eds-segmented-control
        class="${args.className}"
        ?isDisabled="${args.isDisabled}"
        .options="${args.options}"
      ></eds-segmented-control>
    `
  }
};

export default meta;

export const Default = {};

export const Disabled = {
  args: {
    options: [
      {
        text: 'All',
        ariaControls: 'all',
        iconName: 'loading',
        onlyIcon: false,
        isDisabled: false,
        isSelected: false,
        defaultSelected: false,
      },
      {
        text: 'Odd',
        ariaControls: 'odd',
        iconName: 'mail',
        onlyIcon: false,
        isDisabled: false,
        isSelected: false,
        defaultSelected: true,
      },
      {
        text: 'Even',
        ariaControls: 'even',
        iconName: 'informationCircle',
        onlyIcon: false,
        isDisabled: true,
        isSelected: false,
        defaultSelected: false,
      }
    ]
  }
};

export const WithIcons = {
  args: {
    options: [
      {
        text: 'All',
        ariaControls: 'all',
        iconName: 'loading',
        onlyIcon: false,
        isDisabled: false,
        isSelected: false,
        defaultSelected: false,
      },
      {
        text: 'Odd',
        ariaControls: 'odd',
        iconName: 'mail',
        onlyIcon: false,
        isDisabled: false,
        isSelected: false,
        defaultSelected: true,
      },
      {
        text: 'Even',
        ariaControls: 'even',
        iconName: 'informationCircle',
        onlyIcon: false,
        isDisabled: false,
        isSelected: false,
        defaultSelected: false,
      }
    ]
  }
};

export const OnlyIcon = {
  args: {
    options: [
      {
        text: '',
        ariaControls: 'all',
        iconName: 'loading',
        onlyIcon: true,
        isDisabled: false,
        isSelected: false,
        defaultSelected: true,
      },
      {
        text: '',
        ariaControls: 'odd',
        iconName: 'mail',
        onlyIcon: true,
        isDisabled: false,
        isSelected: false,
        defaultSelected: false,
      },
      {
        text: '',
        ariaControls: 'even',
        iconName: 'informationCircle',
        onlyIcon: true,
        isDisabled: false,
        isSelected: false,
        defaultSelected: false,
      }
    ]
  }
};