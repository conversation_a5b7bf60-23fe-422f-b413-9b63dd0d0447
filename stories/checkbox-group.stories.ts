import { html } from 'lit';
import {Args, type Meta} from "@storybook/web-components";


const meta: Meta = {
  title: "Components/Checkbox Group",
  tags: ['autodocs'],
  component: 'eds-checkbox-group',
  argTypes: {
    name: { control: { type: "text" } },
    isRequired: { control: { type: "boolean" } }
  },
  args: {
    name: "checkbox1",
    isRequired: false
  },
  render: (args) => {
    return html `
      <eds-checkbox-group>
        <eds-checkbox id="checkbox1" value="checkbox1"><label for="checkbox1">Label</label></eds-checkbox>
        <eds-checkbox id="checkbox2" value="checkbox2"><label for="checkbox2">Label</label></eds-checkbox>
        <eds-checkbox id="checkbox3" value="checkbox3"><label for="checkbox3">Label</label></eds-checkbox>
        <eds-checkbox id="checkbox4" value="checkbox4"><label for="checkbox4">Label</label></eds-checkbox>
        <eds-checkbox id="checkbox5" value="checkbox5"><label for="checkbox5">Label</label></eds-checkbox>
      </eds-checkbox-group>
    `;
  },
};

export default meta;

export const Default = {
  args: {},
  render: (args: Args) => {
    return html `
      <eds-checkbox-group>
        <eds-checkbox id="checkbox1" value="checkbox1"><label for="checkbox1">Choice one</label></eds-checkbox>
        <eds-checkbox id="checkbox2" value="checkbox2"><label for="checkbox2">Choice two</label></eds-checkbox>
        <eds-checkbox id="checkbox3" value="checkbox3"><label for="checkbox3">Choice three</label></eds-checkbox>
        <eds-checkbox id="checkbox4" value="checkbox4"><label for="checkbox4">Choice four</label></eds-checkbox>
      </eds-checkbox-group>
    `;
  },
};