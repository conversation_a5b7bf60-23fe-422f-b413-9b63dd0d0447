import { html } from 'lit';
import {Args, type Meta} from "@storybook/web-components";


const meta: Meta = {
  title: "Components/Radio Group",
  tags: ['autodocs'],
  component: 'eds-radio-group',
  argTypes: {
    name: { control: { type: "text" } },
    isRequired: { control: { type: "boolean" } }
  },
  args: {
    name: "radio1",
    isRequired: false
  },
  render: (args) => {
    return html `
      <eds-radio-group>
        <eds-radio id="radio1" value="radio1"><label for="radio1">Red</label></eds-radio>
        <eds-radio id="radio2" value="radio2"><label for="radio2">Blue</label></eds-radio>
        <eds-radio id="radio3" value="radio3"><label for="radio3">Yellow</label></eds-radio>
        <eds-radio id="radio4" value="radio4"><label for="radio4">Green</label></eds-radio>
        <eds-radio id="radio5" value="radio5"><label for="radio5">Black</label></eds-radio>
      </eds-radio-group>
    `;
  },
};

export default meta;

export const Default = {
  args: {},
  render: (args: Args) => {
    return html `
      <eds-radio-group>
        <eds-radio id="radio1" value="radio1"><label for="radio1">Choice one</label></eds-radio>
        <eds-radio id="radio2" value="radio2"><label for="radio2">Choice two</label></eds-radio>
        <eds-radio id="radio3" value="radio3"><label for="radio3">Choice three</label></eds-radio>
        <eds-radio id="radio4" value="radio4"><label for="radio4">Choice four</label></eds-radio>
      </eds-radio-group>
    `;
  },
};