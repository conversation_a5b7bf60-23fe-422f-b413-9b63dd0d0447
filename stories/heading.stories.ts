import { html } from "lit";
import { type Meta } from "@storybook/web-components";


const meta:Meta = {
  title: "Components/Heading",
  tags: ["autodocs"],
  parameters: {
    layout: "centered",
    docs: {
      toc: false
    }
  },
  component: "eds-heading",
  argTypes: {
    as: {
      control: { type: "select" },
      options: ["h1", "h2", "h3", "h4", "h5", "h6", "div", "span"],
    },
    text: {
      control: { type: "text" },
    },
    size: {
      control: { type: "select" },
      options: ["xl", "lg", "md", "sm"],
    },
  },
  args: {
    as: "h1",
    text: "Etiya Design System Heading",
    size: "xl",
  },
  render: (args) => {
    return html `<eds-heading 
      as="${args.as}"
      text="${args.text}"
      size="${args.size}"
      ></eds-heading>`;
  }
};

export default meta;

export const Default = {
  args: {}
};