import { html } from 'lit';
import { type Meta } from '@storybook/web-components';

import {Icons} from "../src/icons";

const meta: Meta = {
  title: "Components/Select",
  tags: ['autodocs'],
  component: 'eds-select',
  parameters: {
    docs: {
      story: {
        height: '300px',
      }
    },
  },
  argTypes: {
    id: { control: { type: "text" } },
    appearance: {
      control: { type: "select" },
      options: ['default', 'subtle']
    },
    name: { control: { type: "text" } },
    value: { control: { type: "text" } },
    placeholder: { control: { type: "text" } },
    iconName: {
      control: { type: "select" },
      options: Object.keys(Icons)
    },
    isDisabled: { control: { type: "boolean" } },
    isRequired: { control: { type: "boolean" } },
    isSearchable: { control: { type: "boolean" } },
    isCompact: { control: { type: "boolean" } },
    isInvalid: { control: { type: "boolean" } },
    isLoading: { control: { type: "boolean" } },
    showTags: { control: { type: "boolean" } },
    isMultiple: { control: { type: "boolean" } },
    maxOptionsShown: { control: { type: "number" } },
  },
  args: {
    id: 'select',
    appearance: 'default',
    name: 'select',
    value: '',
    placeholder: 'Choose a city',
    iconName: '',
    isDisabled: false,
    isRequired: false,
    isSearchable: false,
    isCompact: false,
    isInvalid: false,
    isLoading: false,
    showTags: false,
    isMultiple: false,
    maxOptionsShown: 5,
  },
  render: (args) => {
    return html`
<eds-select
  id="${args.id}"
  appearance="${args.appearance}"
  name="${args.name}"
  value="${args.value}"
  iconName="${args.iconName}"
  placeholder="${args.placeholder}"
  ?isDisabled="${args.isDisabled}"
  ?isRequired="${args.isRequired}"
  ?isSearchable="${args.isSearchable}"
  ?isCompact="${args.isCompact}"
  ?isInvalid="${args.isInvalid}"
  ?showTags="${args.showTags}"
  ?isLoading="${args.isLoading}"
  ?isMultiple="${args.isMultiple}"
  maxOptionsShown="${args.maxOptionsShown}"
>
  <eds-select-option label="Ankara" value="ankara" name="selectCity"></eds-select-option>
  <eds-select-option label="Antalya" value="antalya" name="selectCity"></eds-select-option>
  <eds-select-option label="İstanbul" value="istanbul" name="selectCity"></eds-select-option>
  <eds-select-option label="İzmir" value="izmir" name="selectCity"></eds-select-option>
  <eds-select-option label="Tokat" value="tokat" name="selectCity"></eds-select-option>
  <eds-select-option label="Yozgat" value="yozgat" name="selectCity"></eds-select-option>
  <eds-select-option label="Zonguldak" value="zonguldak" name="selectCity"></eds-select-option>
</eds-select>
    `;
  }
};

export default meta;

export const Default = {
  args: {},
};

export const Compact = {
  args: {
    isCompact: true
  },
};

export const Subtle = {
  args: {
    appearance: 'subtle'
  },
};

export const Selected = {
  args: {
    appearance: "default"
  },
  render: (args) => {
    return html`
      <eds-select
            id="${args.id}"
            name="${args.name}"
            value="${args.value}"
            iconName="${args.iconName}"
            placeholder="${args.placeholder}"
            ?isDisabled="${args.isDisabled}"
            ?isRequired="${args.isRequired}"
            ?isSearchable="${args.isSearchable}"
            ?isCompact="${args.isCompact}"
            ?isInvalid="${args.isInvalid}"
            ?showTags="${args.showTags}"
            ?isLoading="${args.isLoading}"
            ?isMultiple="${args.isMultiple}"
            maxOptionsShown="${args.maxOptionsShown}"
      >
        <eds-select-option label="Ankara" value="ankara" name="selectCity"></eds-select-option>
        <eds-select-option label="Antalya" value="antalya" name="selectCity"></eds-select-option>
        <eds-select-option label="İstanbul" value="istanbul" name="selectCity" isSelected="true"></eds-select-option>
        <eds-select-option label="İzmir" value="izmir" name="selectCity"></eds-select-option>
        <eds-select-option label="Tokat" value="tokat" name="selectCity"></eds-select-option>
        <eds-select-option label="Yozgat" value="yozgat" name="selectCity"></eds-select-option>
        <eds-select-option label="Zonguldak" value="zonguldak" name="selectCity"></eds-select-option>
      </eds-select>
    `;
  }
};

export const Invalid = {
  args: {
    isInvalid: true
  },
  render: (args) => {
    return html`
      <eds-select
            id="${args.id}"
            name="${args.name}"
            value="${args.value}"
            iconName="${args.iconName}"
            placeholder="${args.placeholder}"
            ?isDisabled="${args.isDisabled}"
            ?isRequired="${args.isRequired}"
            ?isSearchable="${args.isSearchable}"
            ?isCompact="${args.isCompact}"
            ?isInvalid="${args.isInvalid}"
            ?showTags="${args.showTags}"
            ?isLoading="${args.isLoading}"
            ?isMultiple="${args.isMultiple}"
      >
        <eds-select-option label="Ankara" value="ankara" name="selectCity"></eds-select-option>
        <eds-select-option label="Antalya" value="antalya" name="selectCity"></eds-select-option>
        <eds-select-option label="İstanbul" value="istanbul" name="selectCity" isSelected="true"></eds-select-option>
        <eds-select-option label="İzmir" value="izmir" name="selectCity"></eds-select-option>
        <eds-select-option label="Tokat" value="tokat" name="selectCity"></eds-select-option>
        <eds-select-option label="Yozgat" value="yozgat" name="selectCity"></eds-select-option>
        <eds-select-option label="Zonguldak" value="zonguldak" name="selectCity"></eds-select-option>
      </eds-select>
    `;
  }
};

export const Disabled = {
  args: {
    isDisabled: true
  },
};

export const DisabledWithIcon = {
  args: {
    isDisabled: true,
    iconName: 'placeholderShowcase'
  },
};

export const DisabledWithTag = {
  args: {
    isDisabled: true,
    iconName: 'placeholderShowcase',
    isMultiple: true,
    placeholder: 'Choose a service',
    showTags: true,
  },
  render: (args) => {
    return html`
      <eds-select
            id="${args.id}"
            name="${args.name}"
            value="${args.value}"
            iconName="${args.iconName}"
            placeholder="${args.placeholder}"
            ?isDisabled="${args.isDisabled}"
            ?isRequired="${args.isRequired}"
            ?isSearchable="${args.isSearchable}"
            ?isCompact="${args.isCompact}"
            ?isInvalid="${args.isInvalid}"
            ?showTags="${args.showTags}"
            ?isLoading="${args.isLoading}"
            ?isMultiple="${args.isMultiple}"
      >
        <eds-select-option label="Mobile" value="mobile" name="selectService" isSelected="true"></eds-select-option>
        <eds-select-option label="Mobile Broadband" value="mobileBroadband" name="selectService" isSelected="true"></eds-select-option>
        <eds-select-option label="Fiber Broadband" value="fiberBroadband" name="selectService"></eds-select-option>
        <eds-select-option label="Fixed LTE" value="fixedLTE" name="selectService"></eds-select-option>
        <eds-select-option label="Satellite" value="satellite" name="selectService"></eds-select-option>
      </eds-select>
    `;
  }
};

export const WithIcon = {
  args: {
    iconName: 'placeholderShowcase',
    placeholder: 'Choose a service'
  },
  render: (args) => {
    return html`
      <eds-select
            id="${args.id}"
            name="${args.name}"
            value="${args.value}"
            iconName="${args.iconName}"
            placeholder="${args.placeholder}"
            ?isDisabled="${args.isDisabled}"
            ?isRequired="${args.isRequired}"
            ?isSearchable="${args.isSearchable}"
            ?isCompact="${args.isCompact}"
            ?isInvalid="${args.isInvalid}"
            ?showTags="${args.showTags}"
            ?isLoading="${args.isLoading}"
            ?isMultiple="${args.isMultiple}"
      >
        <eds-select-option label="Mobile" value="mobile" name="selectService"></eds-select-option>
        <eds-select-option label="Mobile Broadband" value="mobileBroadband" name="selectService"></eds-select-option>
        <eds-select-option label="Fiber Broadband" value="fiberBroadband" name="selectService"></eds-select-option>
        <eds-select-option label="Fixed LTE" value="fixedLTE" name="selectService"></eds-select-option>
        <eds-select-option label="Satellite" value="satellite" name="selectService"></eds-select-option>
      </eds-select>
    `;
  }
};

export const Checkbox = {
  args: {
    iconName: 'placeholderShowcase',
    isMultiple: true,
    placeholder: 'Choose a few services'
  },
  render: (args) => {
    return html`
      <eds-select
            id="${args.id}"
            name="${args.name}"
            value="${args.value}"
            iconName="${args.iconName}"
            placeholder="${args.placeholder}"
            ?isDisabled="${args.isDisabled}"
            ?isRequired="${args.isRequired}"
            ?isSearchable="${args.isSearchable}"
            ?isCompact="${args.isCompact}"
            ?isInvalid="${args.isInvalid}"
            ?showTags="${args.showTags}"
            ?isLoading="${args.isLoading}"
            ?isMultiple="${args.isMultiple}"
      >
        <eds-select-option label="Mobile" value="mobile" name="selectService"></eds-select-option>
        <eds-select-option label="Mobile Broadband" value="mobileBroadband" name="selectService"></eds-select-option>
        <eds-select-option label="Fiber Broadband" value="fiberBroadband" name="selectService"></eds-select-option>
        <eds-select-option label="Fixed LTE" value="fixedLTE" name="selectService"></eds-select-option>
        <eds-select-option label="Satellite" value="satellite" name="selectService"></eds-select-option>
      </eds-select>
    `;
  }
};

export const CheckboxWithTag = {
  args: {
    iconName: 'placeholderShowcase',
    isMultiple: true,
    placeholder: 'Choose a few services',
    showTags: true
  },
  render: (args) => {
    return html`
      <eds-select
            id="${args.id}"
            name="${args.name}"
            value="${args.value}"
            iconName="${args.iconName}"
            placeholder="${args.placeholder}"
            ?isDisabled="${args.isDisabled}"
            ?isRequired="${args.isRequired}"
            ?isSearchable="${args.isSearchable}"
            ?isCompact="${args.isCompact}"
            ?isInvalid="${args.isInvalid}"
            ?showTags="${args.showTags}"
            ?isLoading="${args.isLoading}"
            ?isMultiple="${args.isMultiple}"
      >
        <eds-select-option label="Mobile" value="mobile" name="selectService"></eds-select-option>
        <eds-select-option label="Mobile Broadband" value="mobileBroadband" name="selectService"></eds-select-option>
        <eds-select-option label="Fiber Broadband" value="fiberBroadband" name="selectService"></eds-select-option>
        <eds-select-option label="Fixed LTE" value="fixedLTE" name="selectService"></eds-select-option>
        <eds-select-option label="Satellite" value="satellite" name="selectService"></eds-select-option>
      </eds-select>
    `;
  }
};

export const Searchable = {
  args: {
    isSearchable: true
  },
};

export const SearchableMultiple = {
  args: {
    isSearchable: true,
    isMultiple: true
  },
};

export const SearchableMultipleWithTag = {
  args: {
    isSearchable: true,
    isMultiple: true,
    showTags: true,
  },
};

export const Loading = {
  args: {
    isSearchable: true,
  },
  render: (args) => {
    const simulateLoading = (e) => {
      const select = e.target;
      select.isLoading = true;
      setTimeout(() => {
        select.isLoading = false;
      }, 300);
    }

    return html`
      <eds-select
            id="${args.id}"
            name="${args.name}"
            value="${args.value}"
            iconName="${args.iconName}"
            placeholder="${args.placeholder}"
            ?isDisabled="${args.isDisabled}"
            ?isRequired="${args.isRequired}"
            ?isSearchable="${args.isSearchable}"
            ?isCompact="${args.isCompact}"
            ?isInvalid="${args.isInvalid}"
            ?showTags="${args.showTags}"
            ?isLoading="${args.isLoading}"
            ?isMultiple="${args.isMultiple}"
            @input="${(e) => simulateLoading(e)}"
      >
        <eds-select-option label="Option 1" value="option1" name="selectOption" isSelected="true"></eds-select-option>
        <eds-select-option label="Option 2" value="option2" name="selectOption" isDisabled="true"></eds-select-option>
        <eds-select-option label="Option 3" value="option3" name="selectOption"></eds-select-option>
      </eds-select>
    `;
  }
};

export const NoOption = {
  args: {},
  render: (args) => {
    return html`
      <eds-select
            id="${args.id}"
            name="${args.name}"
            value="${args.value}"
            iconName="${args.iconName}"
            placeholder="${args.placeholder}"
            ?isDisabled="${args.isDisabled}"
            ?isRequired="${args.isRequired}"
            ?isSearchable="${args.isSearchable}"
            ?isCompact="${args.isCompact}"
            ?isInvalid="${args.isInvalid}"
            ?showTags="${args.showTags}"
            ?isLoading="${args.isLoading}"
            ?isMultiple="${args.isMultiple}"
      ></eds-select>
    `;
  }
};