import { type Meta } from "@storybook/web-components";
import { html } from "lit";


const meta: Meta = {
  title: "Components/Checkout Step",
  tags: ["autodocs"],
  component: "eds-checkout-step",
  parameters: {
    docs: {
      toc: false
    }
  },
  argTypes: {
    type: {
      control: { type: "select" },
      options: ['active', 'completed', 'inactive']
    },
    number: {
      control: { type: "text" },
    },
    label: {
      control: { type: "text" },
    },
    readyToComplete: {
      control: { type: "boolean" },
    }
  },
  args: {
    type: "active",
    number: "1",
    label: "Step 1",
    readyToComplete: false,
  },
  render: (args) => {
    return html `
      <eds-checkout-step 
        type="${args.type}"
        number="${args.number}"
        label="${args.label}"
        ?readytocomplete="${args.readyToComplete}"
      >
          Step content
      </eds-checkout-step>
    `;
  },
};

export default meta;

export const Inactive = {
  args: {
    type: "inactive",
    readyToComplete: false
  },
};

export const Active = {
  args: {
    type: "active",
    readyToComplete: false
  },
};

export const Completed = {
  args: {
    type: "completed",
    readyToComplete: false
  },
};

export const ReadyToComplete = {
  args: {
    type: "active",
    readyToComplete: true
  },
};