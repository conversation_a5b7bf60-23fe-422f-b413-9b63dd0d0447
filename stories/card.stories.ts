import { type Meta } from "@storybook/web-components";
import { html } from "lit";


const meta: Meta = {
  title: "Components/Card",
  tags: ["autodocs"],
  component: "eds-card",
  parameters: {
    docs: {
      toc: false
    }
  },
  argTypes: {
    title: {
      control: { type: "text" },
    },
    linkText: {
      control: { type: "text" },
    },
    linkURL: {
      control: { type: "text" },
    },
    linkIcon: {
      control: { type: "text" },
    },
    description: {
      control: { type: "text" },
    },
    tags: {
      control: { type: "object" },
    },
    hoverBorderColor: {
      control: { type: "text" },
    }
  },
  args: {
    title: "Card title",
    linkText: "Button",
    linkURL: "#",
    linkIcon: 'borderNone',
    description: "Card description",
    hoverBorderColor: "",
  },
  render: (args) => {
    return html `<eds-card 
      class="${args.className}"
      title="${args.title}"
      linktext="${args.linkText}"
      linkurl="${args.linkURL}"
      linkicon="${args.linkIcon}"
      description="${args.description}"
      .tags=${args.tags}
      hoverbordercolor="${args.hoverBorderColor}"
      >
          Card content
      </eds-card>`;
  },
};

export default meta;

export const CardElement = {
  args: {},
};

export const CardWithTags = {
  args: {
    tags: [
      { 
        text: "Tag 1", 
        appearance: "grey" 
      }, 
      { 
        text: "Tag 2", 
        appearance: "blue" 
      },
      { 
        text: "Tag 3", 
        appearance: "green" 
      },
      { 
        text: "Tag 4", 
        appearance: "red" 
      },
      { 
        text: "Tag 5", 
        appearance: "orange" 
      }
    ],
  },
};
