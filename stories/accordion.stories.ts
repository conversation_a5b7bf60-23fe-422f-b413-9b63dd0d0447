import { html } from 'lit';
import { type Meta } from "@storybook/web-components";


const meta: Meta = {
  title: "Components/Accordion",
  tags: ['autodocs'],
  component: 'eds-accordion',
  argTypes: {
    id: {
      control: { type: "text" },
    },
    caption: {
      control: { type: "text" },
    },
    isOpen: {
      control: { type: "boolean" },
    },
    isDisabled: {
      control: { type: "boolean" },
    },
    appearance: {
      control: { type: "select" },
      options: ['default', 'secondary'],
    },
  },
  args: {
    id: '',
    caption: 'Accordion',
    isOpen: false,
    isDisabled: false,
    appearance: 'default',
  },
  parameters: {
    design: {
      type: "figma",
      url: "https://www.figma.com/design/KH5Ozg1vnbTiE2zUXHyNd8/Wireframe?node-id=2606-1579&t=HZv616TPlyp2fa4a-4",
    },
  },
  render: (args) => {
    return html`
<eds-accordion
  id="${args.id}"
  caption="${args.caption}"
  ?isopen="${args.isOpen}"
  ?isdisabled="${args.isDisabled}"
  appearance="${args.appearance}"
  >
    <slot>
      Accordion Content
    </slot>
</eds-accordion>
      `;
  },
};

export default meta;

export const Default = {
  args: {},
};

export const Secondary = {
  args: {
    appearance: 'secondary',
  },
};

export const Opened = {
  args: {
    isOpen: true,
  },
};

export const Disabled = {
  args: {
    isDisabled: true,
  },
};