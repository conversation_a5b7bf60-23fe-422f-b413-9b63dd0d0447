import { Args, type Meta } from "@storybook/web-components";
import { html, type TemplateResult } from 'lit';


const meta: Meta = {
  title: "Components/Tabs",
  tags: ['autodocs'],
  component: 'eds-tabs',
  argTypes: {
    id: {control: { type: "text" }},
    isBlock: {control: { type: "boolean" }},
    appearance: {
      control: { type: "select" },
      options: ['default', 'line']
    },
  },
  args: {
    isBlock: false,
    appearance: 'default'
  },
  render: (args) => {
    return html`
      <eds-tabs appearance="${args.appearance}" ?isBlock="${args.isBlock}">
        <eds-tab id="tab1" slot="tabs">Tab 1</eds-tab>
        <eds-tab id="tab2" slot="tabs" isDisabled>Tab 2</eds-tab>
        <eds-tab id="tab3" slot="tabs">Tab 3</eds-tab>
        
        <eds-tab-panel id="panel1" tab="tab1">
          <h3>Panel 1 Content</h3>
          <p>This is the content for the first tab.</p>
        </eds-tab-panel>
        
        <eds-tab-panel id="panel2" tab="tab2">
          <h3>Panel 2 Content</h3>
          <p>This is the content for the second tab.</p>
        </eds-tab-panel>
        
        <eds-tab-panel id="panel3" tab="tab3">
          <h3>Panel 3 Content</h3>
          <p>This is the content for the third tab.</p>
        </eds-tab-panel>
      </eds-tabs>
    `
  }
};

export default meta;

export const Default = {
  args: {},
};

export const Line = {
  args: {
    appearance: 'line'
  },
};  

export const WithIcon = {
  args: {},
  render: (args: Args) => {
    return html`
      <eds-tabs>
        <eds-tab slot="tabs" id="withIconTab1">
          <eds-icon slot="icon" name="placeholderShowcase"></eds-icon>
            Tab1
        </eds-tab>
        <eds-tab slot="tabs" id="withIconTab2">
          <eds-icon slot="icon" name="placeholderShowcase"></eds-icon>
            Tab2
        </eds-tab>
        <eds-tab slot="tabs" id="withIconTab3">
          <eds-icon slot="icon" name="placeholderShowcase"></eds-icon>
            Tab3
        </eds-tab>
        <eds-tab slot="tabs" id="withIconTab4">
          <eds-icon slot="icon" name="placeholderShowcase"></eds-icon>
            Tab4
        </eds-tab>
        <eds-tab slot="tabs" id="withIconTab5">
          <eds-icon slot="icon" name="placeholderShowcase"></eds-icon>
            Tab5
        </eds-tab>
        <eds-tab-panel id="panel1" tab="withIconTab1">This is the content area of the first tab.</eds-tab-panel>
        <eds-tab-panel id="panel2" tab="withIconTab2">This is the content area of the second tab.</eds-tab-panel>
        <eds-tab-panel id="panel3" tab="withIconTab3">This is the content area of the third tab.</eds-tab-panel>
        <eds-tab-panel id="panel4" tab="withIconTab4">This is the content area of the fourth tab.</eds-tab-panel>
        <eds-tab-panel id="panel5" tab="withIconTab5">This is the content area of the fifth tab.</eds-tab-panel>
      </eds-tabs>
    `
  }
};

export const Block = {
  args: {
    isBlock: true,
  },
};