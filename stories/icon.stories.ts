import { html } from "lit";
import { Icons } from "../src/icons";

const meta = {
  title: "Components/Icon",
  tags: ['autodocs'],
  parameters: {
    layout: 'centered',
  },
  component: 'eds-icon',
  argTypes: {
    name: {
      control: { type: "select" },
      options: Object.keys(Icons)
    }
  },
  render: (args: any) => {
    return html `<eds-icon name="${args.name}"></eds-icon>`
  }
};

export default meta;

export const Icon = {
  args: {
    name: 'checkmarkCircle'
  }
};

const Template = () => {
  const container = document.createElement('div');
  container.style.display = 'flex';
  container.style.flexWrap = 'wrap';
  container.style.gap = '1rem';

  Object.keys(Icons).sort().map((icon) => {
    const row = document.createElement('div');
    const iconElement = document.createElement('eds-icon');
    const label = document.createElement('span');
    row.style.display = 'flex';
    row.style.flexDirection = 'column';
    row.style.alignItems = 'center';
    row.style.justifyContent = 'space-between';
    row.style.gap = '0.5rem';
    row.style.width = '120px';
    row.style.height = '64px';
    row.style.border = '1px solid hsla(0, 0%, 88%, 1)';
    row.style.borderRadius = '8px';
    row.style.padding = '8px';
    label.textContent = icon;
    iconElement.style.width = '24px';
    iconElement.style.height = '24px';
    iconElement.setAttribute('name', icon);
    row.appendChild(iconElement);
    row.appendChild(label);
    container.appendChild(row);
  });

  return container;
}

export const AllIcons = Template.bind({});