import { html } from "lit";
import { type Meta } from "@storybook/web-components";


const meta: Meta = {
  title: "Components/PhoneNumber",
  tags: ["autodocs"],
  parameters: {
    layout: "centered",
    docs: {
      story: {
        height: '180px',
      },
      toc: false
    }
  },
  component: "eds-phone-number",
  argTypes: {
    id: { control: "text" },
    appearance: {
      control: { type: "select" },
      options: ['default', 'subtle']
    },
    countryCodeValue: { control: "text" },
    phoneNumberValue: { control: "text" },
    countryCodePlaceholder: { control: "text" },
    phoneNumberPlaceholder: { control: "text" },
    isInvalid: { control: "boolean" },
    isDisabled: { control: "boolean" },
    isRequired: { control: "boolean" },
    isCompact: { control: "boolean" },
    countryOptions: { control: "object" }
  },
  args: {
    id: "phoneNumber",
    appearance: 'default',
    countryCodeValue: '',
    phoneNumberValue: '',
    countryCodePlaceholder: '+90',
    phoneNumberPlaceholder: '(*************',
    isInvalid: false,
    isDisabled: false,
    isRequired: false,
    isCompact: false,
    countryOptions: [
      { value: 'TR', label: '+90', mask: '(*************', placeholder: '(*************' },
      { value: 'CA', label: '+1', mask: '(*************', placeholder: '(*************' },
      { value: 'GB', label: '+44', mask: '0000 000 000', placeholder: '0000 000 000' },
    ]
  },
  render: (args) => {
    return html`
        <eds-phone-number style="display: block; width: 276px;"
                          id="${args.id}"
                          appearance="${args.appearance}"
                          countryCodeValue="${args.countryCodeValue}"
                          phoneNumberValue="${args.phoneNumberValue}"
                          countryCodePlaceholder="${args.countryCodePlaceholder}"
                          phoneNumberPlaceholder="${args.phoneNumberPlaceholder}"
                          ?isInvalid="${args.isInvalid}"
                          ?isDisabled="${args.isDisabled}"
                          ?isRequired="${args.isRequired}"
                          ?isCompact="${args.isCompact}"
                          .countryOptions="${args.countryOptions}"
        ></eds-phone-number>`;
  }
};

export default meta;

export const Default = {
  args: {}
};

export const Subtle = {
  args: {
    appearance: 'subtle'
  }
};

export const Compact = {
  args: {
    isCompact: true
  }
};

export const Disabled = {
  args: {
    isDisabled: true
  }
};

export const Invalid = {
  args: {
    isInvalid: true
  }
};

export const Filled = {
  args: {
    countryCodeValue: '+90',
    phoneNumberValue: '(*************',
  }
};