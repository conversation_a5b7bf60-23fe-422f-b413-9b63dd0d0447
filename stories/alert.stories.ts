import { html } from 'lit';
import { type Meta } from "@storybook/web-components";
import { action } from 'storybook/actions';
import { Icons } from "../src/icons";


const meta: Meta = {
  title: "Components/Alert",
  tags: ["autodocs"],
  component: "eds-alert",
  argTypes: {
    id: {
      control: { type: "text" },
    },
    appearance: {
      control: { type: "select" },
      options: ["info", "success", "warning", "error"],
    },
    title: {
      control: { type: "text" },
    },
    description: {
      control: { type: "text" },
    },
    showIcon: {
      control: { type: "boolean" },
    },
    iconName: {
      control: { type: "select" },
      options: Object.keys(Icons),
    },
    actions: {
      control: { type: "object" },
    }
  },
  args: {
    id: "",
    appearance: "info",
    title: "Alert Title",
    description: "Alert Description",
    showIcon: true,
    iconName: "informationCircle",
    actions: [
      {
        content: "Primary Action",
        onClick: action('Primary Action triggered')
      },
      {
        content: "Secondary Action",
        appearance: 'secondary',
        onClick: action('Secondary Action triggered')
      },
    ],
  },
  render: (args) => {
    return html`<eds-alert
      id="${args.id}"
      class="${args.className}"
      appearance="${args.appearance}"
      title="${args.title}"
      description="${args.description}"
      ?showicon="${args.showIcon}"
      iconname="${args.iconName}"
      .actions="${args.actions}"
      ></eds-alert>`;
  },
};

export default meta;

export const InfoAlert = {
  args: {
    appearance: "info"
  },
};

export const SuccessAlert = {
  args: {
    appearance: "success"
  },
};

export const WarningAlert = {
  args: {
    appearance: "warning"
  },
};

export const ErrorAlert = {
  args: {
    appearance: "error"
  },
};

export const AlertDismissible = {
  args: {
    dismissible: true
  },
};

