import { html } from "lit";
import { type Meta } from "@storybook/web-components";

import {Icons} from "../src/icons";

const meta: Meta = {
  title: "Components/TextField",
  tags: ["autodocs"],
  parameters: {
    layout: "centered",
    docs: {
      toc: false
    }
  },
  component: "eds-text-field",
  argTypes: {
    id: { control: "text" },
    appearance: {
      control: { type: "select" },
      options: ["default", "subtle"]
    },
    type: {
      control: { type: "select" },
      options: ["text", "number", "email", "password", "tel"]
    },
    value: { control: "text" },
    placeholder: { control: "text" },
    maxlength: { control: "number" },
    minlength: { control: "number" },
    iconLeading: {
      control: { type: "select" },
      options: Object.keys(Icons)
    },
    iconTrailing: {
      control: { type: "select" },
      options: Object.keys(Icons)
    },
    showValue: { control: "boolean" },
    isDisabled: { control: "boolean" },
    isInvalid: { control: "boolean" },
    isRequired: { control: "boolean" },
    isCompact: { control: "boolean" },
    isReadonly: { control: "boolean" },
  },
  args: {
    id: "textField",
    appearance: "default",
    type: "text",
    value: "",
    maxlength: "",
    minlength: "",
    placeholder: "Placeholder",
    iconLeading: "",
    iconTrailing: "",
    showValue: false,
    isDisabled: false,
    isInvalid: false,
    isRequired: false,
    isCompact: false,
    isReadonly: false,
  },
  render: (args) => {
    return html`
      <eds-text-field
        style="display: block; width: 300px;"
        id="${args.id}"
        type="${args.type}"
        appearance="${args.appearance}"
        value="${args.value}"
        maxlength="${args.maxlength}"
        minlength="${args.minlength}"
        placeholder="${args.placeholder}"
        iconLeading="${args.iconLeading}"
        iconTrailing="${args.iconTrailing}"
        ?showValue="${args.showValue}"
        ?isDisabled="${args.isDisabled}"
        ?isInvalid="${args.isInvalid}"
        ?isRequired="${args.isRequired}"
        ?isCompact="${args.isCompact}"
        ?isReadonly="${args.isReadonly}"
      ></eds-text-field>`;
  }
};

export default meta;

export const Default = {
  args: {}
};

export const Subtle = {
  args: {
    appearance: 'subtle'
  }
};

export const WithIcon = {
  args: {
    iconLeading: 'placeholderShowcase',
    iconTrailing: 'placeholderShowcase',
  }
};

export const Disabled = {
  args: {
    isDisabled: true,
    iconLeading: 'placeholderShowcase',
  }
};

export const Invalid = {
  args: {
    isInvalid: true,
    value: 'Value',
  }
};

export const Filled = {
  args: {
    value: 'Value',
  }
};

export const Compact = {
  args: {
    isCompact: true,
  }
};

export const Password = {
  args: {
    type: 'password',
    value: '123456789'
  }
};