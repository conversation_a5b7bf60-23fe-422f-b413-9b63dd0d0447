import { html } from "lit";
import { type Meta } from "@storybook/web-components";


const meta: Meta = {
  title: "Components/Text",
  tags: ["autodocs"],
  parameters: {
    layout: "centered",
    docs: {
      toc: false
    }
  },
  component: "eds-text",
  argTypes: {
    id: {
      control: { type: "text" },
    },
    as: {
      control: { type: "select" },
      options: ["em", "p", "span", "strong", "b", "del"],
    },    
    text: {
      control: { type: "text" },
    },
    maxLines: {
      control: { type: "number" },
    },
    size: {
      control: { type: "select" },
      options: ["lg", "md", "sm", "xs"],
    },
    weight: {
      control: { type: "select" },
      options: ["regular", "medium"],
    },
  },
  args: {
    id: "custom-id",
    as: "span",
    text: "Lorem ipsum dolor sit amet, consectetur adipiscing elit. Etiam non laoreet est. Sed consequat felis id turpis elementum dignissim. Curabitur iaculis vitae dolor eu iaculis. Aliquam dignissim sapien magna, eu tempor velit commodo vel. Aenean eget gravida massa. Pellentesque nec mi sagittis, vehicula neque ac, laoreet nunc. Praesent tempor condimentum justo. Integer porttitor ante urna, in laoreet sapien vestibulum et. Lorem ipsum dolor sit amet, consectetur adipiscing elit.",
    size: "md",
    weight: "regular",
  },
  render: (args) => {
    return html`
      <eds-text
        id="${args.id}"
        as="${args.as}"
        text="${args.text}"
        maxlines="${args.maxLines}"
        size="${args.size}"
        weight="${args.weight}"
      ></eds-text>`;
  }
};

export default meta;

export const Default = {
  args: {}
};