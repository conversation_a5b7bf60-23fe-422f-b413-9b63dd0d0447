import { html } from 'lit';
import { type Meta } from '@storybook/web-components';
import { action } from 'storybook/actions';

const meta: Meta = {
  title: 'Components/Tooltip',
  tags: ['autodocs'],
  component: 'eds-tooltip',
  argTypes: {
    content: {
      control: { type: 'text' },
      description: 'Tooltip content text'
    },
    placement: {
      control: { type: 'select' },
      options: ['top', 'bottom', 'left', 'right'],
      description: 'Preferred tooltip placement. Will automatically adjust if there\'s no room.'
    },
    trigger: {
      control: { type: 'select' },
      options: ['hover', 'focus', 'click', 'both'],
      description: 'How the tooltip is triggered'
    },
    delay: {
      control: { type: 'number', min: 0, max: 2000, step: 50 },
      description: 'Show delay in milliseconds'
    },
    hideDelay: {
      control: { type: 'number', min: 0, max: 2000, step: 50 },
      description: 'Hide delay in milliseconds'
    },
    offset: {
      control: { type: 'number', min: 0, max: 50, step: 1 },
      description: 'Distance from trigger element in pixels'
    },
    arrow: {
      control: { type: 'boolean' },
      description: 'Show/hide tooltip arrow'
    },
    disabled: {
      control: { type: 'boolean' },
      description: 'Disable tooltip'
    }
  },
  args: {
    content: 'This is a helpful tooltip!',
    placement: 'top',
    trigger: 'hover',
    delay: 100,
    hideDelay: 0,
    offset: 8,
    arrow: true,
    disabled: false
  },
  render: (args) => {
    return html`
      <div style="padding: 100px; display: grid; place-items: center; grid-template: repeat(auto-fill, 1fr) / repeat(auto-fit, 1fr);">
        <eds-tooltip
          content="${args.content}"
          placement="${args.placement}"
          trigger="${args.trigger}"
          delay="${args.delay}"
          hideDelay="${args.hideDelay}"
          offset="${args.offset}"
          ?arrow="${args.arrow}"
          ?disabled="${args.disabled}"
          @tooltip-show="${action('tooltip-show')}"
          @tooltip-hide="${action('tooltip-hide')}"
        >
          <eds-button appearance="primary" slot="trigger">
            Hover me for tooltip
          </eds-button>
        </eds-tooltip>
      </div>
    `;
  },
};

export default meta;

export const DefaultTooltip = {
  args: {
    content: 'This is a default tooltip',
    placement: 'top'
  }
};

export const TooltipPlacements = {
  render: () => html`
         <div style="padding: 150px; display: grid; grid-template-columns: 1fr 1fr; gap: 100px; place-items: center;">
      <div style="display: flex; flex-direction: column; align-items: center; gap: 20px;">
        <h3>Top Placement</h3>
        <eds-tooltip content="Tooltip on top" placement="top">
          <eds-button appearance="default" slot="trigger">Top Tooltip</eds-button>
        </eds-tooltip>
      </div>
      
      <div style="display: flex; flex-direction: column; align-items: center; gap: 20px;">
        <h3>Bottom Placement</h3>
        <eds-tooltip content="Tooltip on bottom" placement="bottom">
          <eds-button appearance="default" slot="trigger">Bottom Tooltip</eds-button>
        </eds-tooltip>
      </div>
      
      <div style="display: flex; flex-direction: column; align-items: center; gap: 20px;">
        <h3>Left Placement</h3>
        <eds-tooltip content="Tooltip on left" placement="left">
          <eds-button appearance="default" slot="trigger">Left Tooltip</eds-button>
        </eds-tooltip>
      </div>
      
      <div style="display: flex; flex-direction: column; align-items: center; gap: 20px;">
        <h3>Right Placement</h3>
        <eds-tooltip content="Tooltip on right" placement="right">
          <eds-button appearance="default" slot="trigger">Right Tooltip</eds-button>
        </eds-tooltip>
      </div>
    </div>
  `
};

export const TooltipTriggers = {
  render: () => html`
    <div style="padding: 100px; display: grid; place-items: center; grid-template: repeat(auto-fill, 1fr) / repeat(auto-fit, 1fr);">
      <div style="display: flex; flex-direction: column; align-items: center; gap: 20px;">
        <h3>Hover Trigger</h3>  
        <eds-tooltip content="Shows on hover" trigger="hover">
          <eds-button appearance="primary" slot="trigger">Hover me</eds-button>
        </eds-tooltip>
      </div>
      
      <div style="display: flex; flex-direction: column; align-items: center; gap: 20px;">
        <h3>Focus Trigger</h3>
        <eds-tooltip content="Shows on focus" trigger="focus">
          <eds-button appearance="primary" slot="trigger">Tab to focus</eds-button>
        </eds-tooltip>
      </div>
      
      <div style="display: flex; flex-direction: column; align-items: center; gap: 20px;">
        <h3>Click Trigger</h3>
        <eds-tooltip content="Shows on click" trigger="click">
          <eds-button appearance="primary" slot="trigger">Click me</eds-button>
        </eds-tooltip>
      </div>
    </div>
  `
};

export const TooltipWithSlotContent = {
  render: () => html`
    <div style="padding: 100px; display: flex; justify-content: center; align-items: center;">
       <eds-tooltip>
         <eds-button appearance="primary" iconLeading="user" slot="trigger">
           Rich Content Tooltip
         </eds-button>
         <div slot="content">
           <strong>Rich Content Tooltip</strong><br/>
           <em>This tooltip contains HTML content</em><br/>
           <span style="color: #ff7a00;">• Feature 1</span><br/>
           <span style="color: #ff7a00;">• Feature 2</span>
         </div>
       </eds-tooltip>
    </div>
    `
};

export const TooltipWithoutArrow = {
  args: {
    content: 'Tooltip without arrow',
    arrow: false
  }
};

export const TooltipWithDelay = {
  args: {
    content: 'This tooltip has a 500ms delay',
    delay: 500,
    hideDelay: 200
  }
};

export const TooltipThemes = {
  render: () => html`
    <div style="padding: 100px; display: grid; place-items: center; grid-template: repeat(auto-fill, 1fr) / repeat(auto-fit, 1fr);">
      <div style="display: flex; flex-direction: column; align-items: center; gap: 20px;">
        <h3>Default Theme</h3>
        <eds-tooltip content="Default dark tooltip">
          <eds-button appearance="primary" slot="trigger">Default</eds-button>
        </eds-tooltip>
      </div>
      
      <div style="display: flex; flex-direction: column; align-items: center; gap: 20px;">
        <h3>Light Theme</h3>
        <eds-tooltip content="Light themed tooltip" theme="light">
          <eds-button appearance="primary" slot="trigger">Light</eds-button>
        </eds-tooltip>
      </div>
      
      <div style="display: flex; flex-direction: column; align-items: center; gap: 20px;">
        <h3>Info Theme</h3>
        <eds-tooltip content="Info themed tooltip" theme="info">
          <eds-button appearance="primary" slot="trigger">Info</eds-button>
        </eds-tooltip>
      </div>
      
      <div style="display: flex; flex-direction: column; align-items: center; gap: 20px;">
        <h3>Success Theme</h3>
        <eds-tooltip content="Success themed tooltip" theme="success">
          <eds-button appearance="primary" slot="trigger">Success</eds-button>
        </eds-tooltip>
      </div>
      
      <div style="display: flex; flex-direction: column; align-items: center; gap: 20px;">
        <h3>Warning Theme</h3>
        <eds-tooltip content="Warning themed tooltip" theme="warning">
          <eds-button appearance="primary" slot="trigger">Warning</eds-button>
        </eds-tooltip>
      </div>
      
      <div style="display: flex; flex-direction: column; align-items: center; gap: 20px;">
        <h3>Danger Theme</h3>
        <eds-tooltip content="Danger themed tooltip" theme="danger">
          <eds-button appearance="primary" slot="trigger">Danger</eds-button>
        </eds-tooltip>
      </div>
    </div>
  `
};

export const TooltipWithVariousElements = {
  render: () => html`
    <div style="padding: 100px; display: grid; place-items: center; grid-template: repeat(auto-fill, 1fr) / repeat(auto-fit, 1fr); gap: 24px">
      <h2>Tooltip works with any element</h2>
      
        <!-- Button -->
        <eds-tooltip content="Tooltip on button">
          <eds-button appearance="primary" slot="trigger">Button</eds-button>
        </eds-tooltip>
        
        <!-- Text -->
        <eds-tooltip content="Tooltip on text element">
          <span slot="trigger" style="padding: 10px; border: 1px dashed #ccc;">Hover this text</span>
        </eds-tooltip>
        
        <!-- Icon -->
        <eds-tooltip content="Tooltip on icon">
          <eds-icon name="user" slot="trigger" style="font-size: 24px; cursor: pointer;"></eds-icon>
        </eds-tooltip>
        
        <!-- Input -->
        <eds-tooltip content="Tooltip on input field">
          <eds-text-field placeholder="Hover me" slot="trigger"></eds-text-field>
        </eds-tooltip>
        
        <!-- Image -->
        <eds-tooltip content="Tooltip on custom element">
          <div style="width: 50px; height: 50px; background: linear-gradient(45deg, #ff7a00, #ff9933); border-radius: 4px; cursor: pointer;" slot="trigger"></div>
        </eds-tooltip>
    </div>
  `
};

export const DisabledTooltip = {
  args: {
    content: 'This tooltip is disabled',
    disabled: true
  }
};

export const SmartPositioning = {
  render: () => html`
    <div style="height: 100vh; padding: 20px; position: relative;">
      <h2 style="text-align: center; margin-bottom: 40px;">Smart Positioning Demo</h2>
      <p style="text-align: center; margin-bottom: 40px; color: #666;">
        These tooltips will automatically adjust their position when there's no room in the preferred direction.
      </p>
      
      <!-- Top edge - tooltip should flip to bottom -->
      <div style="position: absolute; top: 20px; left: 50%; transform: translateX(-50%);">
        <eds-tooltip content="I want to be on top, but there's no room so I'll flip to bottom!" placement="top">
          <eds-button appearance="primary" slot="trigger">Top Edge (flips to bottom)</eds-button>
        </eds-tooltip>
      </div>
      
      <!-- Bottom edge - tooltip should flip to top -->
      <div style="position: absolute; bottom: 20px; left: 50%; transform: translateX(-50%);">
        <eds-tooltip content="I want to be on bottom, but there's no room so I'll flip to top!" placement="bottom">
          <eds-button appearance="primary" slot="trigger">Bottom Edge (flips to top)</eds-button>
        </eds-tooltip>
      </div>
      
      <!-- Left edge - tooltip should flip to right -->
      <div style="position: absolute; left: 20px; top: 50%; transform: translateY(-50%);">
        <eds-tooltip content="I want to be on the left, but there's no room so I'll flip to right!" placement="left">
          <eds-button appearance="primary" slot="trigger">Left Edge (flips to right)</eds-button>
        </eds-tooltip>
      </div>
      
      <!-- Right edge - tooltip should flip to left -->
      <div style="position: absolute; right: 20px; top: 50%; transform: translateY(-50%);">
        <eds-tooltip content="I want to be on the right, but there's no room so I'll flip to left!" placement="right">
          <eds-button appearance="primary" slot="trigger">Right Edge (flips to left)</eds-button>
        </eds-tooltip>
      </div>
      
      <!-- Corner cases -->
      <div style="position: absolute; top: 20px; left: 20px;">
        <eds-tooltip content="Top-left corner: I'll find the best spot!" placement="top">
          <eds-button appearance="secondary" slot="trigger">Top-Left Corner</eds-button>
        </eds-tooltip>
      </div>
      
      <div style="position: absolute; top: 20px; right: 20px;">
        <eds-tooltip content="Top-right corner: Smart positioning in action!" placement="top">
          <eds-button appearance="secondary" slot="trigger">Top-Right Corner</eds-button>
        </eds-tooltip>
      </div>
      
      <div style="position: absolute; bottom: 20px; left: 20px;">
        <eds-tooltip content="Bottom-left corner: Watch me adapt!" placement="bottom">
          <eds-button appearance="secondary" slot="trigger">Bottom-Left Corner</eds-button>
        </eds-tooltip>
      </div>
      
      <div style="position: absolute; bottom: 20px; right: 20px;">
        <eds-tooltip content="Bottom-right corner: Perfect positioning!" placement="bottom">
          <eds-button appearance="secondary" slot="trigger">Bottom-Right Corner</eds-button>
        </eds-tooltip>
      </div>
      
      <!-- Center for reference -->
      <div style="position: absolute; top: 50%; left: 50%; transform: translate(-50%, -50%);">
        <eds-tooltip content="I'm in the center with plenty of room, so I stay in my preferred position!" placement="top">
          <eds-button appearance="primary" slot="trigger">Center (stays as preferred)</eds-button>
        </eds-tooltip>
      </div>
    </div>
  `
}; 