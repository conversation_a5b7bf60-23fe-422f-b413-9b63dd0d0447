import { html } from "lit";
import { type Meta } from "@storybook/web-components";
import { Icons } from "../src/icons";


const meta: Meta = {
  title: "Components/Tile",
  tags: ['autodocs'],
  component: 'eds-tile',
  parameters: {
    docs: {
      toc: false
    }
  },
  argTypes: {
    iconName: {
      control: { type: "select" },
      options: Object.keys(Icons)
    },
    title: {control: { type: "text" }},
    text: {control: { type: "text" }},
    appearance: {control: { type: "select" },
      options: ['primary', 'secondary']
    }
  },
  args: {
    iconName: 'wifi',
    title: 'Data',
    text: '100 GB',
  },
  render: (args) => {
    return html `
      <eds-tile 
        iconName="${args.iconName}"
        title="${args.title}" 
        text="${args.text}"
        appearance="${args.appearance}">
      </eds-tile>`;
  }
};

export default meta;

export const Primary = {
  args: {
    appearance: 'primary',
  },
};

export const Secondary = {
  args: {
    appearance: 'secondary',
  },
};