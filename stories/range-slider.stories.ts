import { html } from 'lit';
import { type Meta } from "@storybook/web-components";
import { action } from 'storybook/actions';

const meta: Meta = {
  title: "Components/RangeSlider",
  tags: ['autodocs'],
  component: 'eds-range-slider',
  argTypes: {
    value: {
      control: { type: "number" },
    },
    min: {
      control: { type: "number" },
    },
    max: {
      control: { type: "number" },
    },
    step: {
      control: { type: "number" },
    },
    customValues: {
      control: { type: "object" },
    },
    unit: {
      control: { type: "text" },
    },
    label: {
      control: { type: "text" },
    },
    icon: {
      control: { type: "text" },
    },
    disabled: {
      control: { type: "boolean" },
    },
    showButtons: {
      control: { type: "boolean" },
    },
    showValue: {
      control: { type: "boolean" },
    },
    unlimitedOffset: {
      control: { type: "number" },
    }
  },
  args: {
    value: 50,
    min: 0,
    max: 100,
    step: 1,
    customValues: [],
    unit: 'GB',
    label: 'Label',
    icon: 'borderNone',
    disabled: false,
    showButtons: true,
    showValue: true,
    unlimitedOffset: 1
  },
  render: (args) => {
    return html`
      <eds-range-slider
        .value="${args.value}"
        .min="${args.min}"
        .max="${args.max}"
        .step="${args.step}"
        .customValues="${args.customValues}"
        unit="${args.unit}"
        label="${args.label}"
        icon="${args.icon}"
        ?disabled="${args.disabled}"
        ?showButtons="${args.showButtons}"
        ?showValue="${args.showValue}"
        .unlimitedOffset="${args.unlimitedOffset}"
        @slider-change="${action("slider-change")}">
      </eds-range-slider>`;
  },
};

export default meta;

export const Default = {
  args: {
    value: 50
  },
};

export const WithCustomValues = {
  args: {
    customValues: {
      '100': '100',
      '200': '200',
      '300': '300',
      '400': '400',
      '500': '500',
      Unlimited: 'Unlimited',
    },
    unlimitedOffset: 100
  },
};

export const WithUnlimitedOffset = {
  args: {
    customValues:
      { 
        Unlimited: 'Unlimited',
      },
    unlimitedOffset: 10
  },
};

export const SmoothProgressDemo = {
  args: {
    label: 'Memory Allocation',
    value: 200,

    customValues: {
      '100': '100 MB',
      '200': '200 MB',
      '500': '500 MB',
      '1000': '1 GB',
      '2000': '2 GB',
      '4000': '4 GB',
      'Unlimited': 'Unlimited'
    },

    unit: '',
    showValue: true,
    showButtons: true,
    unlimitedOffset: 1000
  },
};