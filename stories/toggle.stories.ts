import { html } from 'lit';
import { type Meta } from '@storybook/web-components';

const meta: Meta = {
  title: 'Components/Toggle',
  tags: ['autodocs'],
  parameters: {
    layout: 'centered',
    docs: {
      toc: false,
    },
  },
  component: 'eds-toggle',
  argTypes: {
    id: { control: 'text' },
    checked: { control: 'boolean' },
    disabled: { control: 'boolean' },
    name: { control: 'text' },
    value: { control: 'text' },
    required: { control: 'boolean' },
  },
  args: {
    id: 'toggle',
    checked: false,
    disabled: false,
    name: 'toggle',
    value: 'on',
    required: false,
  },
  render: (args) => {
    return html` <eds-toggle
      id="${args.id}"
      ?checked="${args.checked}"
      ?disabled="${args.disabled}"
      name="${args.name}"
      value="${args.value}"
      ?required="${args.required}"
    >
      Toggle
    </eds-toggle>`;
  },
};

export default meta;

export const Default = {
  args: {},
};
