import '../src/themes/index.module.css';
import type { Preview } from '@storybook/web-components';
import { themes } from "storybook/theming";
import { setCustomElementsManifest } from '@storybook/web-components';
import customElements from '../custom-elements.json';
import '@eds/components';


setCustomElementsManifest(customElements);

const preview: Preview = {
  tags: ["autodocs"],
  parameters: {
    controls: {
      matchers: {
        color: /(background|color)$/i,
        date: /Date$/i,
      },
    },
    docs: {
      toc: { title: 'List of Stories' },
      theme: themes.light,
    },
    options: {
      storySort: {
        method: 'alphabetical',
        order: [
          "Documentation",
          "Get Started",
          "Components",
        ],
      },
    },
  },
};

export default preview;
