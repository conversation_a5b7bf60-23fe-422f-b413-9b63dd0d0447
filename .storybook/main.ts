import path, { dirname, join } from "path";

function getAbsolutePath(value: string) {
  return dirname(require.resolve(join(value, "package.json")));
}

const config = {
  stories: ["../docs/*.mdx", "../stories/*.stories.ts"],
  addons: [
    getAbsolutePath("@storybook/addon-links"),
    getAbsolutePath("@storybook/addon-designs"),
    getAbsolutePath("@storybook/addon-a11y"),
    getAbsolutePath("@storybook/addon-docs")
  ],
  framework: {
    name: getAbsolutePath("@storybook/web-components-vite"),
  },
  core: {
    globals: {
      lit: ['^lit']
    }
  },
  staticDirs: [
    "../assets"
  ],
  docs: {},
  async viteFinal(config, { configType }) {
    // customize the Vite config here
    return {
      ...config,
      lib: {
        entry: path.resolve(__dirname, '../src/index.ts'),
        formats: ['es', 'umd']
      },
      resolve: {
        alias: {
          "@eds/components": path.resolve(__dirname, '../src')
        }
      },
      define: { "process.env": {} },
      css: {
        modules: {
          localsConvention: "camelCase",
        },
      }
    };
  }
};

export default config;
