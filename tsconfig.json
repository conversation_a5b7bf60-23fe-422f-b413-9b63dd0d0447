{"$schema": "https://json.schemastore.org/tsconfig", "display": "<PERSON><PERSON><PERSON>", "compilerOptions": {"target": "es2017", "module": "es2015", "moduleResolution": "node", "lib": ["es2017", "dom"], "importHelpers": false, "noEmitHelpers": false, "composite": false, "declaration": true, "declarationMap": true, "esModuleInterop": true, "forceConsistentCasingInFileNames": true, "inlineSources": false, "isolatedModules": true, "noUnusedLocals": false, "noUnusedParameters": false, "preserveWatchOutput": true, "strict": true, "experimentalDecorators": true, "useDefineForClassFields": false, "skipLibCheck": true, "outDir": "./dist", "types": ["node", "minimatch"], "paths": {"@eds/components": ["./src"]}, "strictNullChecks": true}, "include": ["src/index.ts", "./stories/vite-env.d.ts", "node_modules/vite/client.d.ts"], "exclude": ["node_modules"]}